<template>
	<view class="container">
		<view class="form-container">
			<view class="form-title">门店信息</view>
			
			<!-- 表单项 -->
			<view class="form-group">
				<view class="form-label">门店名称<text class="required">*</text></view>
				<input 
					type="text" 
					v-model="shopInfo.shopName" 
					class="form-input" 
					:disabled="!isEditing"
					placeholder="请输入门店名称" 
				/>
			</view>
			
			<view class="form-group">
				<view class="form-label">门店编码<text class="required">*</text></view>
				<input 
					type="text" 
					v-model="shopInfo.shopCode" 
					class="form-input" 
					:disabled="true"
					placeholder="门店编码" 
				/>
			</view>
			
			<view class="form-group">
				<view class="form-label">所属区域/城市<text class="required">*</text></view>
				<picker 
					:disabled="!isEditing" 
					:value="regionIndex" 
					:range="regionList" 
					range-key="regionName"
					@change="handleRegionChange"
					class="form-picker"
				>
					<view class="picker-value" :class="{'picker-placeholder': !shopInfo.regionName && isEditing}">
						{{ shopInfo.regionName || '请选择所属区域' }}
					</view>
				</picker>
			</view>
			
			<view class="form-group">
				<view class="form-label">门店地址<text class="required">*</text></view>
				<input 
					type="text" 
					v-model="shopInfo.location" 
					class="form-input" 
					:disabled="!isEditing"
					placeholder="请输入门店地址" 
				/>
			</view>
			
			<view class="form-group">
				<view class="form-label">联系人<text class="required">*</text></view>
				<input 
					type="text" 
					v-model="shopInfo.contactPerson" 
					class="form-input" 
					:disabled="!isEditing"
					placeholder="请输入联系人" 
				/>
			</view>
			
			<view class="form-group">
				<view class="form-label">联系电话<text class="required">*</text></view>
				<input 
					type="number" 
					v-model="shopInfo.contactPhone" 
					class="form-input" 
					:disabled="!isEditing"
					placeholder="请输入联系电话" 
					maxlength="11"
				/>
			</view>
			
			<view class="form-group">
				<view class="form-label">备注</view>
				<textarea 
					v-model="shopInfo.remark" 
					class="form-textarea" 
					:disabled="!isEditing"
					placeholder="请输入备注信息" 
				/>
			</view>
			
			<!-- 按钮区域 -->
			<view class="btn-group">
				<button 
					v-if="!isEditing" 
					class="btn btn-primary" 
					@click="handleEdit"
				>修改</button>
				
				<button 
					v-if="isEditing" 
					class="btn btn-primary" 
					:disabled="!canSubmit"
					@click="handleSubmit"
				>提交</button>
				
				<button 
					v-if="isEditing" 
					class="btn btn-default" 
					@click="handleReset"
				>重置</button>
				
				<button 
					v-if="isEditing" 
					class="btn btn-cancel" 
					@click="handleCancel"
				>取消</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getShopInfo, updateShopInfo, getAllRegions } from '@/api/work/retrun_part.js';

export default {
	data() {
		return {
			// 是否处于编辑状态
			isEditing: false,
			
			// 门店信息
			shopInfo: {
				shopId: '',
				shopName: '',
				shopCode: '',
				regionId: '',
				regionName: '', // 添加区域名称字段用于显示
				location: '',
				contactPerson: '',
				contactPhone: '',
				remark: ''
			},
			
			// 区域列表
			regionList: [],
			regionIndex: 0,
			
			// 原始数据（用于重置）
			originalShopInfo: {},
			
			loading: false
		}
	},
	computed: {
		// 判断是否可以提交表单
		canSubmit() {
			return this.shopInfo.shopName && 
				this.shopInfo.regionId && // 添加区域ID验证
				this.shopInfo.location && 
				this.shopInfo.contactPerson && 
				this.shopInfo.contactPhone && 
				/^1\d{10}$/.test(this.shopInfo.contactPhone);
		}
	},
	onLoad() {
		this.fetchRegionList();
		this.fetchShopInfo();
	},
	methods: {
		// 获取区域列表
		async fetchRegionList() {
			try {
				const res = await getAllRegions();
				
				if (res.code === 200 && res.data) {
					// 将snake_case属性映射为camelCase属性
					this.regionList = res.data.map(item => {
						return {
							regionId: item.region_id,
							regionName: item.region_name
						};
					});
				} else {
					uni.showToast({
						title: res.msg || '获取区域列表失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取区域列表失败', error);
				uni.showToast({
					title: '获取区域列表失败',
					icon: 'none'
				});
			}
		},
		
		// 处理区域选择变化
		handleRegionChange(e) {
			const index = e.detail.value;
			if (this.regionList[index]) {
				this.regionIndex = index;
				this.shopInfo.regionId = this.regionList[index].regionId;
				this.shopInfo.regionName = this.regionList[index].regionName;
			}
		},
		
		// 获取当前登录用户的门店信息
		async fetchShopInfo() {
			uni.showLoading({
				title: '加载中'
			});
			
			try {
				const res = await getShopInfo();
				
				if (res.code === 200 && res.data) {
					this.shopInfo = {
						shopId: res.data.shopId,
						shopName: res.data.shopName,
						shopCode: res.data.shopCode,
						regionId: res.data.regionId,
						location: res.data.location || '',
						contactPerson: res.data.contactPerson || '',
						contactPhone: res.data.contactPhone || '',
						remark: res.data.remark || ''
					};
					
					// 根据regionId找到对应的regionName
					if (this.regionList.length > 0 && this.shopInfo.regionId) {
						const regionObj = this.regionList.find(item => item.regionId == this.shopInfo.regionId);
						if (regionObj) {
							this.shopInfo.regionName = regionObj.regionName;
							// 设置选择器索引
							this.regionIndex = this.regionList.findIndex(item => item.regionId == this.shopInfo.regionId);
							if (this.regionIndex < 0) this.regionIndex = 0;
						}
					}
					
					// 保存原始数据（用于重置）
					this.originalShopInfo = JSON.parse(JSON.stringify(this.shopInfo));
				} else {
					uni.showToast({
						title: res.msg || '获取门店信息失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取门店信息失败', error);
				uni.showToast({
					title: '获取门店信息失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 点击修改按钮
		handleEdit() {
			this.isEditing = true;
		},
		
		// 重置表单
		handleReset() {
			this.shopInfo = {
				shopId: this.originalShopInfo.shopId,
				shopName: '',
				shopCode: this.originalShopInfo.shopCode,
				regionId: '',
				regionName: '',
				location: '',
				contactPerson: '',
				contactPhone: '',
				remark: ''
			};
			this.regionIndex = 0;
		},
		
		// 取消修改
		handleCancel() {
			this.shopInfo = JSON.parse(JSON.stringify(this.originalShopInfo));
			// 恢复区域选择器索引
			if (this.regionList.length > 0 && this.shopInfo.regionId) {
				this.regionIndex = this.regionList.findIndex(item => item.regionId == this.shopInfo.regionId);
				if (this.regionIndex < 0) this.regionIndex = 0;
			}
			this.isEditing = false;
		},
		
		// 提交修改
		async handleSubmit() {
			if (!this.canSubmit) {
				return;
			}
			
			// 验证手机号格式
			if (!/^1\d{10}$/.test(this.shopInfo.contactPhone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}
			
			uni.showLoading({
				title: '提交中'
			});
			
			try {
				const res = await updateShopInfo(this.shopInfo);
				
				if (res.code === 200) {
					uni.showToast({
						title: '更新成功',
						icon: 'success'
					});
					
					// 更新成功后，更新原始数据
					this.originalShopInfo = JSON.parse(JSON.stringify(this.shopInfo));
					this.isEditing = false;
				} else {
					uni.showToast({
						title: res.msg || '更新失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('更新门店信息失败', error);
				uni.showToast({
					title: '更新门店信息失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	box-sizing: border-box;
}

.form-container {
	background-color: #ffffff;
	border-radius: 10rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 40rpx;
	color: #333;
}

.form-group {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
	
	.required {
		color: #f56c6c;
		margin-left: 4rpx;
	}
}

.form-input {
	width: 100%;
	height: 80rpx;
	border: 1px solid #dcdfe6;
	border-radius: 4rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	box-sizing: border-box;
	
	&:disabled {
		background-color: #f5f7fa;
		color: #909399;
	}
}

/* 添加选择器样式 */
.form-picker {
	width: 100%;
	height: 80rpx;
	border: 1px solid #dcdfe6;
	border-radius: 4rpx;
	background-color: #fff;
	box-sizing: border-box;
	
	.picker-value {
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.picker-placeholder {
		color: #999;
	}
}

.form-textarea {
	width: 100%;
	height: 180rpx;
	border: 1px solid #dcdfe6;
	border-radius: 4rpx;
	padding: 15rpx 20rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	box-sizing: border-box;
	
	&:disabled {
		background-color: #f5f7fa;
		color: #909399;
	}
}

.btn-group {
	display: flex;
	justify-content: center;
	margin-top: 50rpx;
	
	.btn {
		min-width: 200rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		font-size: 28rpx;
		margin: 0 20rpx;
	}
	
	.btn-primary {
		background-color: #3c96f3;
		color: #ffffff;
		
		&:disabled {
			background-color: #a0cfff;
		}
	}
	
	.btn-default {
		background-color: #f5f7fa;
		color: #909399;
		border: 1px solid #dcdfe6;
	}
	
	.btn-cancel {
		background-color: #ffffff;
		color: #606266;
		border: 1px solid #dcdfe6;
	}
}
</style>
