{"version": 3, "names": ["_helperFunctionName", "require", "_helperReplaceSupers", "_helperEnvironmentVisitor", "_core", "_helperAnnotateAsPure", "_inlineCallSuperHelpers", "buildConstructor", "classRef", "constructorBody", "node", "func", "t", "functionDeclaration", "cloneNode", "inherits", "transformClass", "path", "file", "builtinClasses", "isLoose", "assumptions", "supportUnicodeId", "classState", "parent", "undefined", "scope", "classId", "superName", "superReturns", "isDerived", "extendsNative", "construct", "userConstructor", "userConstructorPath", "hasConstructor", "body", "superThises", "pushedConstructor", "pushedInherits", "pushedCreateClass", "protoAlias", "dynamic<PERSON>eys", "Map", "methods", "instance", "hasComputed", "list", "map", "static", "setState", "newState", "Object", "assign", "findThisesVisitor", "traverse", "visitors", "merge", "environmentVisitor", "ThisExpression", "push", "createClassHelper", "args", "callExpression", "addHelper", "maybeCreateConstructor", "classBodyPath", "get", "isClassMethod", "kind", "params", "constructor", "template", "expression", "ast", "blockStatement", "unshiftContainer", "classMethod", "identifier", "buildBody", "pushBody", "verifyConstructor", "pushDescriptors", "classBodyPaths", "isClassProperty", "isClassPrivateProperty", "buildCodeFrameError", "decorators", "isConstructor", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "constant<PERSON>uper", "refToPreserve", "replace", "ReturnStatement", "getFunctionParent", "isArrowFunctionExpression", "pushConstructor", "pushMethod", "pushInheritsToBody", "props", "placement", "length", "desc", "obj", "objectExpression", "objectProperty", "key", "properties", "arrayExpression", "nullLiteral", "lastNonNullIndex", "i", "is<PERSON>ull<PERSON>iteral", "slice", "expressionStatement", "wrapSuperCall", "bareSuper", "thisRef", "bareSuperNode", "call", "superIsCallableConstructor", "arguments", "unshift", "thisExpression", "isSpreadElement", "isIdentifier", "argument", "name", "callee", "memberExpression", "logicalExpression", "_bareSuperNode$argume", "bareSuperNodeArguments", "addCallSuperHelper", "parentPath", "isExpressionStatement", "container", "assignmentExpression", "replaceWith", "returnStatement", "ref", "generateDeclaredUidIdentifier", "thisPath", "isMemberExpression", "object", "bareSupers", "Super", "isCallExpression", "guaranteedSuperBefore<PERSON>inish", "find", "isLoop", "isConditional", "wrapReturn", "returnArg", "thisExpr", "returnParams", "bodyPaths", "pop", "isReturnStatement", "pushContainer", "returnPath", "processMethod", "<PERSON><PERSON><PERSON><PERSON>", "isNumericLiteral", "isBigIntLiteral", "stringLiteral", "String", "value", "toCom<PERSON><PERSON>ey", "fn", "toExpression", "isStringLiteral", "_nameFunction", "nameFunction", "id", "descriptor", "has", "set", "setClassMethods", "insertProtoAliasOnce", "methodName", "computed", "isLiteral", "functionExpression", "generator", "async", "_nameFunction2", "expr", "inheritsComments", "generateUidIdentifier", "classProto", "protoDeclaration", "variableDeclaration", "variableDeclarator", "method", "directives", "pushConstructorToBody", "hasInstanceDescriptors", "hasStaticDescriptors", "extractDynamicKeys", "elem", "isPure", "generateUidIdentifierBasedOnNode", "setupClosureParamsArgs", "closureParams", "closureArgs", "arg", "annotateAsPure", "param", "classTransformer", "superClass", "hasBinding", "noClassCalls", "isStrict", "isInStrictMode", "constructorOnly", "directive", "directiveLiteral", "arrowFunctionExpression"], "sources": ["../src/transformClass.ts"], "sourcesContent": ["import type { <PERSON>de<PERSON><PERSON>, Scope, Visitor } from \"@babel/traverse\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport { traverse, template, types as t, type File } from \"@babel/core\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport addCallSuperHelper from \"./inline-callSuper-helpers.ts\";\n\ntype ClassAssumptions = {\n  setClassMethods: boolean;\n  constantSuper: boolean;\n  superIsCallableConstructor: boolean;\n  noClassCalls: boolean;\n};\n\ntype ClassConstructor = t.ClassMethod & { kind: \"constructor\" };\n\nfunction buildConstructor(\n  classRef: t.Identifier,\n  constructorBody: t.BlockStatement,\n  node: t.Class,\n) {\n  const func = t.functionDeclaration(\n    t.cloneNode(classRef),\n    [],\n    constructorBody,\n  );\n  t.inherits(func, node);\n  return func;\n}\n\ntype Descriptor = {\n  key: t.Expression;\n  get?: t.Expression | null;\n  set?: t.Expression | null;\n  value?: t.Expression | null;\n  constructor?: t.Expression | null;\n};\n\ntype State = {\n  parent: t.Node;\n  scope: Scope;\n  node: t.Class;\n  path: NodePath<t.Class>;\n  file: File;\n\n  classId: t.Identifier | void;\n  classRef: t.Identifier;\n  superName: t.Expression | null;\n  superReturns: NodePath<t.ReturnStatement>[];\n  isDerived: boolean;\n  extendsNative: boolean;\n\n  construct: t.FunctionDeclaration;\n  constructorBody: t.BlockStatement;\n  userConstructor: ClassConstructor;\n  userConstructorPath: NodePath<ClassConstructor>;\n  hasConstructor: boolean;\n\n  body: t.Statement[];\n  superThises: NodePath<t.ThisExpression>[];\n  pushedConstructor: boolean;\n  pushedInherits: boolean;\n  pushedCreateClass: boolean;\n  protoAlias: t.Identifier | null;\n  isLoose: boolean;\n\n  dynamicKeys: Map<string, t.Expression>;\n\n  methods: {\n    // 'list' is in the same order as the elements appear in the class body.\n    // if there aren't computed keys, we can safely reorder class elements\n    // and use 'map' to merge duplicates.\n    instance: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n    static: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n  };\n};\n\ntype PropertyInfo = {\n  instance: t.ObjectExpression[] | null;\n  static: t.ObjectExpression[] | null;\n};\n\nexport default function transformClass(\n  path: NodePath<t.Class>,\n  file: File,\n  builtinClasses: ReadonlySet<string>,\n  isLoose: boolean,\n  assumptions: ClassAssumptions,\n  supportUnicodeId: boolean,\n) {\n  const classState: State = {\n    parent: undefined,\n    scope: undefined,\n    node: undefined,\n    path: undefined,\n    file: undefined,\n\n    classId: undefined,\n    classRef: undefined,\n    superName: null,\n    superReturns: [],\n    isDerived: false,\n    extendsNative: false,\n\n    construct: undefined,\n    constructorBody: undefined,\n    userConstructor: undefined,\n    userConstructorPath: undefined,\n    hasConstructor: false,\n\n    body: [],\n    superThises: [],\n    pushedConstructor: false,\n    pushedInherits: false,\n    pushedCreateClass: false,\n    protoAlias: null,\n    isLoose: false,\n\n    dynamicKeys: new Map(),\n\n    methods: {\n      instance: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n      static: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n    },\n  };\n\n  const setState = (newState: Partial<State>) => {\n    Object.assign(classState, newState);\n  };\n\n  const findThisesVisitor = traverse.visitors.merge([\n    environmentVisitor,\n    {\n      ThisExpression(path) {\n        classState.superThises.push(path);\n      },\n    },\n  ]);\n\n  function createClassHelper(args: t.Expression[]) {\n    return t.callExpression(classState.file.addHelper(\"createClass\"), args);\n  }\n\n  /**\n   * Creates a class constructor or bail out if there is one\n   */\n  function maybeCreateConstructor() {\n    const classBodyPath = classState.path.get(\"body\");\n    for (const path of classBodyPath.get(\"body\")) {\n      if (path.isClassMethod({ kind: \"constructor\" })) return;\n    }\n\n    let params: t.FunctionExpression[\"params\"], body;\n\n    if (classState.isDerived) {\n      const constructor = template.expression.ast`\n        (function () {\n          super(...arguments);\n        })\n      ` as t.FunctionExpression;\n      params = constructor.params;\n      body = constructor.body;\n    } else {\n      params = [];\n      body = t.blockStatement([]);\n    }\n\n    classBodyPath.unshiftContainer(\n      \"body\",\n      t.classMethod(\"constructor\", t.identifier(\"constructor\"), params, body),\n    );\n  }\n\n  function buildBody() {\n    maybeCreateConstructor();\n    pushBody();\n    verifyConstructor();\n\n    if (classState.userConstructor) {\n      const { constructorBody, userConstructor, construct } = classState;\n\n      constructorBody.body.push(...userConstructor.body.body);\n      t.inherits(construct, userConstructor);\n      t.inherits(constructorBody, userConstructor.body);\n    }\n\n    pushDescriptors();\n  }\n\n  function pushBody() {\n    const classBodyPaths: Array<any> = classState.path.get(\"body.body\");\n\n    for (const path of classBodyPaths) {\n      const node = path.node;\n\n      if (path.isClassProperty() || path.isClassPrivateProperty()) {\n        throw path.buildCodeFrameError(\"Missing class properties transform.\");\n      }\n\n      if (node.decorators) {\n        throw path.buildCodeFrameError(\n          \"Method has decorators, put the decorator plugin before the classes one.\",\n        );\n      }\n\n      if (t.isClassMethod(node)) {\n        const isConstructor = node.kind === \"constructor\";\n\n        const replaceSupers = new ReplaceSupers({\n          methodPath: path,\n          objectRef: classState.classRef,\n          superRef: classState.superName,\n          constantSuper: assumptions.constantSuper,\n          file: classState.file,\n          refToPreserve: classState.classRef,\n        });\n\n        replaceSupers.replace();\n\n        const superReturns: NodePath<t.ReturnStatement>[] = [];\n        path.traverse(\n          traverse.visitors.merge([\n            environmentVisitor,\n            {\n              ReturnStatement(path) {\n                if (!path.getFunctionParent().isArrowFunctionExpression()) {\n                  superReturns.push(path);\n                }\n              },\n            },\n          ]),\n        );\n\n        if (isConstructor) {\n          pushConstructor(superReturns, node as ClassConstructor, path);\n        } else {\n          pushMethod(node, path);\n        }\n      }\n    }\n  }\n\n  function pushDescriptors() {\n    pushInheritsToBody();\n\n    const { body } = classState;\n\n    const props: PropertyInfo = {\n      instance: null,\n      static: null,\n    };\n\n    for (const placement of [\"static\", \"instance\"] as const) {\n      if (classState.methods[placement].list.length) {\n        props[placement] = classState.methods[placement].list.map(desc => {\n          const obj = t.objectExpression([\n            t.objectProperty(t.identifier(\"key\"), desc.key),\n          ]);\n\n          for (const kind of [\"get\", \"set\", \"value\"] as const) {\n            if (desc[kind] != null) {\n              obj.properties.push(\n                t.objectProperty(t.identifier(kind), desc[kind]),\n              );\n            }\n          }\n\n          return obj;\n        });\n      }\n    }\n\n    if (props.instance || props.static) {\n      let args = [\n        t.cloneNode(classState.classRef), // Constructor\n        props.instance ? t.arrayExpression(props.instance) : t.nullLiteral(), // instanceDescriptors\n        props.static ? t.arrayExpression(props.static) : t.nullLiteral(), // staticDescriptors\n      ];\n\n      let lastNonNullIndex = 0;\n      for (let i = 0; i < args.length; i++) {\n        if (!t.isNullLiteral(args[i])) lastNonNullIndex = i;\n      }\n      args = args.slice(0, lastNonNullIndex + 1);\n\n      body.push(t.expressionStatement(createClassHelper(args)));\n      classState.pushedCreateClass = true;\n    }\n  }\n\n  function wrapSuperCall(\n    bareSuper: NodePath<t.CallExpression>,\n    superRef: t.Expression,\n    thisRef: () => t.Identifier,\n    body: NodePath<t.BlockStatement>,\n  ) {\n    const bareSuperNode = bareSuper.node;\n    let call;\n\n    if (assumptions.superIsCallableConstructor) {\n      bareSuperNode.arguments.unshift(t.thisExpression());\n      if (\n        bareSuperNode.arguments.length === 2 &&\n        t.isSpreadElement(bareSuperNode.arguments[1]) &&\n        t.isIdentifier(bareSuperNode.arguments[1].argument, {\n          name: \"arguments\",\n        })\n      ) {\n        // special case single arguments spread\n        bareSuperNode.arguments[1] = bareSuperNode.arguments[1].argument;\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"apply\"),\n        );\n      } else {\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"call\"),\n        );\n      }\n\n      call = t.logicalExpression(\"||\", bareSuperNode, t.thisExpression());\n    } else {\n      const args: t.Expression[] = [\n        t.thisExpression(),\n        t.cloneNode(classState.classRef),\n      ];\n      if (bareSuperNode.arguments?.length) {\n        const bareSuperNodeArguments = bareSuperNode.arguments as (\n          | t.Expression\n          | t.SpreadElement\n        )[];\n\n        /**\n         * test262/test/language/expressions/super/call-spread-err-sngl-err-itr-get-get.js\n         *\n         * var iter = {};\n         * Object.defineProperty(iter, Symbol.iterator, {\n         *   get: function() {\n         *     throw new Test262Error();\n         *   }\n         * })\n         * super(...iter);\n         */\n\n        if (\n          bareSuperNodeArguments.length === 1 &&\n          t.isSpreadElement(bareSuperNodeArguments[0]) &&\n          t.isIdentifier(bareSuperNodeArguments[0].argument, {\n            name: \"arguments\",\n          })\n        ) {\n          args.push(bareSuperNodeArguments[0].argument);\n        } else {\n          args.push(t.arrayExpression(bareSuperNodeArguments));\n        }\n      }\n      call = t.callExpression(addCallSuperHelper(classState.file), args);\n    }\n\n    if (\n      bareSuper.parentPath.isExpressionStatement() &&\n      bareSuper.parentPath.container === body.node.body &&\n      body.node.body.length - 1 === bareSuper.parentPath.key\n    ) {\n      // this super call is the last statement in the body so we can just straight up\n      // turn it into a return\n\n      if (classState.superThises.length) {\n        call = t.assignmentExpression(\"=\", thisRef(), call);\n      }\n\n      bareSuper.parentPath.replaceWith(t.returnStatement(call));\n    } else {\n      bareSuper.replaceWith(t.assignmentExpression(\"=\", thisRef(), call));\n    }\n  }\n\n  function verifyConstructor() {\n    if (!classState.isDerived) return;\n\n    const path = classState.userConstructorPath;\n    const body = path.get(\"body\");\n\n    path.traverse(findThisesVisitor);\n\n    let thisRef = function () {\n      const ref = path.scope.generateDeclaredUidIdentifier(\"this\");\n      thisRef = () => t.cloneNode(ref);\n      return ref;\n    };\n\n    for (const thisPath of classState.superThises) {\n      const { node, parentPath } = thisPath;\n      if (parentPath.isMemberExpression({ object: node })) {\n        thisPath.replaceWith(thisRef());\n        continue;\n      }\n      thisPath.replaceWith(\n        t.callExpression(classState.file.addHelper(\"assertThisInitialized\"), [\n          thisRef(),\n        ]),\n      );\n    }\n\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    path.traverse(\n      traverse.visitors.merge([\n        environmentVisitor,\n        {\n          Super(path) {\n            const { node, parentPath } = path;\n            if (parentPath.isCallExpression({ callee: node })) {\n              bareSupers.unshift(parentPath);\n            }\n          },\n        } as Visitor,\n      ]),\n    );\n\n    let guaranteedSuperBeforeFinish = !!bareSupers.length;\n\n    for (const bareSuper of bareSupers) {\n      wrapSuperCall(bareSuper, classState.superName, thisRef, body);\n\n      if (guaranteedSuperBeforeFinish) {\n        bareSuper.find(function (parentPath) {\n          // hit top so short circuit\n          if (parentPath === path) {\n            return true;\n          }\n\n          if (\n            parentPath.isLoop() ||\n            parentPath.isConditional() ||\n            parentPath.isArrowFunctionExpression()\n          ) {\n            guaranteedSuperBeforeFinish = false;\n            return true;\n          }\n        });\n      }\n    }\n\n    let wrapReturn;\n\n    if (classState.isLoose) {\n      wrapReturn = (returnArg: t.Expression | void) => {\n        const thisExpr = t.callExpression(\n          classState.file.addHelper(\"assertThisInitialized\"),\n          [thisRef()],\n        );\n        return returnArg\n          ? t.logicalExpression(\"||\", returnArg, thisExpr)\n          : thisExpr;\n      };\n    } else {\n      wrapReturn = (returnArg: t.Expression | undefined) => {\n        const returnParams: t.Expression[] = [thisRef()];\n        if (returnArg != null) {\n          returnParams.push(returnArg);\n        }\n        return t.callExpression(\n          classState.file.addHelper(\"possibleConstructorReturn\"),\n          returnParams,\n        );\n      };\n    }\n\n    // if we have a return as the last node in the body then we've already caught that\n    // return\n    const bodyPaths = body.get(\"body\");\n    if (!bodyPaths.length || !bodyPaths.pop().isReturnStatement()) {\n      body.pushContainer(\n        \"body\",\n        t.returnStatement(\n          guaranteedSuperBeforeFinish ? thisRef() : wrapReturn(),\n        ),\n      );\n    }\n\n    for (const returnPath of classState.superReturns) {\n      returnPath\n        .get(\"argument\")\n        .replaceWith(wrapReturn(returnPath.node.argument));\n    }\n  }\n\n  /**\n   * Push a method to its respective mutatorMap.\n   */\n  function pushMethod(node: t.ClassMethod, path?: NodePath) {\n    const scope = path ? path.scope : classState.scope;\n\n    if (node.kind === \"method\") {\n      if (processMethod(node, scope)) return;\n    }\n\n    const placement = node.static ? \"static\" : \"instance\";\n    const methods = classState.methods[placement];\n\n    const descKey = node.kind === \"method\" ? \"value\" : node.kind;\n    const key =\n      t.isNumericLiteral(node.key) || t.isBigIntLiteral(node.key)\n        ? t.stringLiteral(String(node.key.value))\n        : t.toComputedKey(node);\n\n    let fn: t.Expression = t.toExpression(node);\n\n    if (t.isStringLiteral(key)) {\n      // infer function name\n      if (node.kind === \"method\") {\n        // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n        // does not seem to support it\n        fn =\n          nameFunction(\n            // @ts-expect-error Fixme: we are passing a ClassMethod to nameFunction, but nameFunction\n            // does not seem to support it\n            { id: key, node: node, scope },\n            undefined,\n            supportUnicodeId,\n          ) ?? fn;\n      }\n    } else {\n      // todo(flow->ts) find a way to avoid \"key as t.StringLiteral\" below which relies on this assignment\n      methods.hasComputed = true;\n    }\n\n    let descriptor: Descriptor;\n    if (\n      !methods.hasComputed &&\n      methods.map.has((key as t.StringLiteral).value)\n    ) {\n      descriptor = methods.map.get((key as t.StringLiteral).value);\n      descriptor[descKey] = fn;\n\n      if (descKey === \"value\") {\n        descriptor.get = null;\n        descriptor.set = null;\n      } else {\n        descriptor.value = null;\n      }\n    } else {\n      descriptor = {\n        key:\n          // private name has been handled in class-properties transform\n          key as t.Expression,\n        [descKey]: fn,\n      } as Descriptor;\n      methods.list.push(descriptor);\n\n      if (!methods.hasComputed) {\n        methods.map.set((key as t.StringLiteral).value, descriptor);\n      }\n    }\n  }\n\n  function processMethod(node: t.ClassMethod, scope: Scope) {\n    if (assumptions.setClassMethods && !node.decorators) {\n      // use assignments instead of define properties for loose classes\n      let { classRef } = classState;\n      if (!node.static) {\n        insertProtoAliasOnce();\n        classRef = classState.protoAlias;\n      }\n      const methodName = t.memberExpression(\n        t.cloneNode(classRef),\n        node.key,\n        node.computed || t.isLiteral(node.key),\n      );\n\n      let func: t.Expression = t.functionExpression(\n        null,\n        // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n        node.params,\n        node.body,\n        node.generator,\n        node.async,\n      );\n      t.inherits(func, node);\n\n      const key = t.toComputedKey(node, node.key);\n      if (t.isStringLiteral(key)) {\n        // @ts-expect-error: requires strictNullCheck\n        func =\n          nameFunction(\n            {\n              node: func,\n              id: key,\n              scope,\n            },\n            undefined,\n            supportUnicodeId,\n          ) ?? func;\n      }\n\n      const expr = t.expressionStatement(\n        t.assignmentExpression(\"=\", methodName, func),\n      );\n      t.inheritsComments(expr, node);\n      classState.body.push(expr);\n      return true;\n    }\n\n    return false;\n  }\n\n  function insertProtoAliasOnce() {\n    if (classState.protoAlias === null) {\n      setState({ protoAlias: classState.scope.generateUidIdentifier(\"proto\") });\n      const classProto = t.memberExpression(\n        classState.classRef,\n        t.identifier(\"prototype\"),\n      );\n      const protoDeclaration = t.variableDeclaration(\"var\", [\n        t.variableDeclarator(classState.protoAlias, classProto),\n      ]);\n\n      classState.body.push(protoDeclaration);\n    }\n  }\n\n  /**\n   * Replace the constructor body of our class.\n   */\n  function pushConstructor(\n    superReturns: NodePath<t.ReturnStatement>[],\n    method: ClassConstructor,\n    path: NodePath<ClassConstructor>,\n  ) {\n    setState({\n      userConstructorPath: path,\n      userConstructor: method,\n      hasConstructor: true,\n      superReturns,\n    });\n\n    const { construct } = classState;\n\n    t.inheritsComments(construct, method);\n\n    // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n    construct.params = method.params;\n\n    t.inherits(construct.body, method.body);\n    construct.body.directives = method.body.directives;\n\n    pushConstructorToBody();\n  }\n\n  function pushConstructorToBody() {\n    if (classState.pushedConstructor) return;\n    classState.pushedConstructor = true;\n\n    // we haven't pushed any descriptors yet\n    // @ts-expect-error todo(flow->ts) maybe remove this block - properties from condition are not used anywhere else\n    if (classState.hasInstanceDescriptors || classState.hasStaticDescriptors) {\n      pushDescriptors();\n    }\n\n    classState.body.push(classState.construct);\n\n    pushInheritsToBody();\n  }\n\n  /**\n   * Push inherits helper to body.\n   */\n  function pushInheritsToBody() {\n    if (!classState.isDerived || classState.pushedInherits) return;\n\n    classState.pushedInherits = true;\n\n    // Unshift to ensure that the constructor inheritance is set up before\n    // any properties can be assigned to the prototype.\n\n    classState.body.unshift(\n      t.expressionStatement(\n        t.callExpression(\n          classState.file.addHelper(\n            classState.isLoose ? \"inheritsLoose\" : \"inherits\",\n          ),\n          [t.cloneNode(classState.classRef), t.cloneNode(classState.superName)],\n        ),\n      ),\n    );\n  }\n\n  function extractDynamicKeys() {\n    const { dynamicKeys, node, scope } = classState;\n\n    for (const elem of node.body.body) {\n      if (!t.isClassMethod(elem) || !elem.computed) continue;\n      if (scope.isPure(elem.key, /* constants only*/ true)) continue;\n\n      const id = scope.generateUidIdentifierBasedOnNode(elem.key);\n      dynamicKeys.set(id.name, elem.key);\n\n      elem.key = id;\n    }\n  }\n\n  function setupClosureParamsArgs() {\n    const { superName, dynamicKeys } = classState;\n    const closureParams = [];\n    const closureArgs = [];\n\n    if (classState.isDerived) {\n      let arg = t.cloneNode(superName);\n      if (classState.extendsNative) {\n        arg = t.callExpression(classState.file.addHelper(\"wrapNativeSuper\"), [\n          arg,\n        ]);\n        annotateAsPure(arg);\n      }\n\n      const param =\n        classState.scope.generateUidIdentifierBasedOnNode(superName);\n\n      closureParams.push(param);\n      closureArgs.push(arg);\n\n      setState({ superName: t.cloneNode(param) });\n    }\n\n    for (const [name, value] of dynamicKeys) {\n      closureParams.push(t.identifier(name));\n      closureArgs.push(value);\n    }\n\n    return { closureParams, closureArgs };\n  }\n\n  function classTransformer(\n    path: NodePath<t.Class>,\n    file: File,\n    builtinClasses: ReadonlySet<string>,\n    isLoose: boolean,\n  ) {\n    setState({\n      parent: path.parent,\n      scope: path.scope,\n      node: path.node,\n      path,\n      file,\n      isLoose,\n    });\n\n    setState({\n      classId: classState.node.id,\n      // this is the name of the binding that will **always** reference the class we've constructed\n      classRef: classState.node.id\n        ? t.identifier(classState.node.id.name)\n        : classState.scope.generateUidIdentifier(\"class\"),\n      superName: classState.node.superClass,\n      isDerived: !!classState.node.superClass,\n      constructorBody: t.blockStatement([]),\n    });\n\n    setState({\n      extendsNative:\n        t.isIdentifier(classState.superName) &&\n        builtinClasses.has(classState.superName.name) &&\n        !classState.scope.hasBinding(\n          classState.superName.name,\n          /* noGlobals */ true,\n        ),\n    });\n\n    const { classRef, node, constructorBody } = classState;\n\n    setState({\n      construct: buildConstructor(classRef, constructorBody, node),\n    });\n\n    extractDynamicKeys();\n\n    const { body } = classState;\n    const { closureParams, closureArgs } = setupClosureParamsArgs();\n\n    buildBody();\n\n    // make sure this class isn't directly called (with A() instead new A())\n    if (!assumptions.noClassCalls) {\n      constructorBody.body.unshift(\n        t.expressionStatement(\n          t.callExpression(classState.file.addHelper(\"classCallCheck\"), [\n            t.thisExpression(),\n            t.cloneNode(classState.classRef),\n          ]),\n        ),\n      );\n    }\n\n    const isStrict = path.isInStrictMode();\n    let constructorOnly = classState.classId && body.length === 1;\n    if (constructorOnly && !isStrict) {\n      for (const param of classState.construct.params) {\n        // It's illegal to put a use strict directive into the body of a function\n        // with non-simple parameters for some reason. So, we have to use a strict\n        // wrapper function.\n        if (!t.isIdentifier(param)) {\n          constructorOnly = false;\n          break;\n        }\n      }\n    }\n\n    const directives = constructorOnly\n      ? (body[0] as t.FunctionExpression | t.FunctionDeclaration).body\n          .directives\n      : [];\n    if (!isStrict) {\n      directives.push(t.directive(t.directiveLiteral(\"use strict\")));\n    }\n\n    if (constructorOnly) {\n      // named class with only a constructor\n      const expr = t.toExpression(\n        body[0] as t.FunctionExpression | t.FunctionDeclaration,\n      );\n      return classState.isLoose ? expr : createClassHelper([expr]);\n    }\n\n    let returnArg: t.Expression = t.cloneNode(classState.classRef);\n    if (!classState.pushedCreateClass && !classState.isLoose) {\n      returnArg = createClassHelper([returnArg]);\n    }\n\n    body.push(t.returnStatement(returnArg));\n    const container = t.arrowFunctionExpression(\n      closureParams,\n      t.blockStatement(body, directives),\n    );\n    return t.callExpression(container, closureArgs);\n  }\n\n  return classTransformer(path, file, builtinClasses, isLoose);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAJ,OAAA;AAEA,IAAAK,uBAAA,GAAAL,OAAA;AAWA,SAASM,gBAAgBA,CACvBC,QAAsB,EACtBC,eAAiC,EACjCC,IAAa,EACb;EACA,MAAMC,IAAI,GAAGC,WAAC,CAACC,mBAAmB,CAChCD,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrB,EAAE,EACFC,eACF,CAAC;EACDG,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;EACtB,OAAOC,IAAI;AACb;AA8De,SAASK,cAAcA,CACpCC,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChBC,WAA6B,EAC7BC,gBAAyB,EACzB;EACA,MAAMC,UAAiB,GAAG;IACxBC,MAAM,EAAEC,SAAS;IACjBC,KAAK,EAAED,SAAS;IAChBf,IAAI,EAAEe,SAAS;IACfR,IAAI,EAAEQ,SAAS;IACfP,IAAI,EAAEO,SAAS;IAEfE,OAAO,EAAEF,SAAS;IAClBjB,QAAQ,EAAEiB,SAAS;IACnBG,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IAEpBC,SAAS,EAAEP,SAAS;IACpBhB,eAAe,EAAEgB,SAAS;IAC1BQ,eAAe,EAAER,SAAS;IAC1BS,mBAAmB,EAAET,SAAS;IAC9BU,cAAc,EAAE,KAAK;IAErBC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,KAAK;IACxBC,cAAc,EAAE,KAAK;IACrBC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,IAAI;IAChBrB,OAAO,EAAE,KAAK;IAEdsB,WAAW,EAAE,IAAIC,GAAG,CAAC,CAAC;IAEtBC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf,CAAC;MACDM,MAAM,EAAE;QACNH,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf;IACF;EACF,CAAC;EAED,MAAMO,QAAQ,GAAIC,QAAwB,IAAK;IAC7CC,MAAM,CAACC,MAAM,CAAC9B,UAAU,EAAE4B,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMG,iBAAiB,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAChDC,iCAAkB,EAClB;IACEC,cAAcA,CAAC1C,IAAI,EAAE;MACnBM,UAAU,CAACc,WAAW,CAACuB,IAAI,CAAC3C,IAAI,CAAC;IACnC;EACF,CAAC,CACF,CAAC;EAEF,SAAS4C,iBAAiBA,CAACC,IAAoB,EAAE;IAC/C,OAAOlD,WAAC,CAACmD,cAAc,CAACxC,UAAU,CAACL,IAAI,CAAC8C,SAAS,CAAC,aAAa,CAAC,EAAEF,IAAI,CAAC;EACzE;EAKA,SAASG,sBAAsBA,CAAA,EAAG;IAChC,MAAMC,aAAa,GAAG3C,UAAU,CAACN,IAAI,CAACkD,GAAG,CAAC,MAAM,CAAC;IACjD,KAAK,MAAMlD,IAAI,IAAIiD,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5C,IAAIlD,IAAI,CAACmD,aAAa,CAAC;QAAEC,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;IACnD;IAEA,IAAIC,MAAsC,EAAElC,IAAI;IAEhD,IAAIb,UAAU,CAACO,SAAS,EAAE;MACxB,MAAMyC,WAAW,GAAGC,cAAQ,CAACC,UAAU,CAACC,GAAI;AAClD;AACA;AACA;AACA,OAA+B;MACzBJ,MAAM,GAAGC,WAAW,CAACD,MAAM;MAC3BlC,IAAI,GAAGmC,WAAW,CAACnC,IAAI;IACzB,CAAC,MAAM;MACLkC,MAAM,GAAG,EAAE;MACXlC,IAAI,GAAGxB,WAAC,CAAC+D,cAAc,CAAC,EAAE,CAAC;IAC7B;IAEAT,aAAa,CAACU,gBAAgB,CAC5B,MAAM,EACNhE,WAAC,CAACiE,WAAW,CAAC,aAAa,EAAEjE,WAAC,CAACkE,UAAU,CAAC,aAAa,CAAC,EAAER,MAAM,EAAElC,IAAI,CACxE,CAAC;EACH;EAEA,SAAS2C,SAASA,CAAA,EAAG;IACnBd,sBAAsB,CAAC,CAAC;IACxBe,QAAQ,CAAC,CAAC;IACVC,iBAAiB,CAAC,CAAC;IAEnB,IAAI1D,UAAU,CAACU,eAAe,EAAE;MAC9B,MAAM;QAAExB,eAAe;QAAEwB,eAAe;QAAED;MAAU,CAAC,GAAGT,UAAU;MAElEd,eAAe,CAAC2B,IAAI,CAACwB,IAAI,CAAC,GAAG3B,eAAe,CAACG,IAAI,CAACA,IAAI,CAAC;MACvDxB,WAAC,CAACG,QAAQ,CAACiB,SAAS,EAAEC,eAAe,CAAC;MACtCrB,WAAC,CAACG,QAAQ,CAACN,eAAe,EAAEwB,eAAe,CAACG,IAAI,CAAC;IACnD;IAEA8C,eAAe,CAAC,CAAC;EACnB;EAEA,SAASF,QAAQA,CAAA,EAAG;IAClB,MAAMG,cAA0B,GAAG5D,UAAU,CAACN,IAAI,CAACkD,GAAG,CAAC,WAAW,CAAC;IAEnE,KAAK,MAAMlD,IAAI,IAAIkE,cAAc,EAAE;MACjC,MAAMzE,IAAI,GAAGO,IAAI,CAACP,IAAI;MAEtB,IAAIO,IAAI,CAACmE,eAAe,CAAC,CAAC,IAAInE,IAAI,CAACoE,sBAAsB,CAAC,CAAC,EAAE;QAC3D,MAAMpE,IAAI,CAACqE,mBAAmB,CAAC,qCAAqC,CAAC;MACvE;MAEA,IAAI5E,IAAI,CAAC6E,UAAU,EAAE;QACnB,MAAMtE,IAAI,CAACqE,mBAAmB,CAC5B,yEACF,CAAC;MACH;MAEA,IAAI1E,WAAC,CAACwD,aAAa,CAAC1D,IAAI,CAAC,EAAE;QACzB,MAAM8E,aAAa,GAAG9E,IAAI,CAAC2D,IAAI,KAAK,aAAa;QAEjD,MAAMoB,aAAa,GAAG,IAAIC,4BAAa,CAAC;UACtCC,UAAU,EAAE1E,IAAI;UAChB2E,SAAS,EAAErE,UAAU,CAACf,QAAQ;UAC9BqF,QAAQ,EAAEtE,UAAU,CAACK,SAAS;UAC9BkE,aAAa,EAAEzE,WAAW,CAACyE,aAAa;UACxC5E,IAAI,EAAEK,UAAU,CAACL,IAAI;UACrB6E,aAAa,EAAExE,UAAU,CAACf;QAC5B,CAAC,CAAC;QAEFiF,aAAa,CAACO,OAAO,CAAC,CAAC;QAEvB,MAAMnE,YAA2C,GAAG,EAAE;QACtDZ,IAAI,CAACsC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;UACEuC,eAAeA,CAAChF,IAAI,EAAE;YACpB,IAAI,CAACA,IAAI,CAACiF,iBAAiB,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC,EAAE;cACzDtE,YAAY,CAAC+B,IAAI,CAAC3C,IAAI,CAAC;YACzB;UACF;QACF,CAAC,CACF,CACH,CAAC;QAED,IAAIuE,aAAa,EAAE;UACjBY,eAAe,CAACvE,YAAY,EAAEnB,IAAI,EAAsBO,IAAI,CAAC;QAC/D,CAAC,MAAM;UACLoF,UAAU,CAAC3F,IAAI,EAAEO,IAAI,CAAC;QACxB;MACF;IACF;EACF;EAEA,SAASiE,eAAeA,CAAA,EAAG;IACzBoB,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAElE;IAAK,CAAC,GAAGb,UAAU;IAE3B,MAAMgF,KAAmB,GAAG;MAC1B1D,QAAQ,EAAE,IAAI;MACdI,MAAM,EAAE;IACV,CAAC;IAED,KAAK,MAAMuD,SAAS,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAW;MACvD,IAAIjF,UAAU,CAACqB,OAAO,CAAC4D,SAAS,CAAC,CAACzD,IAAI,CAAC0D,MAAM,EAAE;QAC7CF,KAAK,CAACC,SAAS,CAAC,GAAGjF,UAAU,CAACqB,OAAO,CAAC4D,SAAS,CAAC,CAACzD,IAAI,CAACC,GAAG,CAAC0D,IAAI,IAAI;UAChE,MAAMC,GAAG,GAAG/F,WAAC,CAACgG,gBAAgB,CAAC,CAC7BhG,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACkE,UAAU,CAAC,KAAK,CAAC,EAAE4B,IAAI,CAACI,GAAG,CAAC,CAChD,CAAC;UAEF,KAAK,MAAMzC,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAW;YACnD,IAAIqC,IAAI,CAACrC,IAAI,CAAC,IAAI,IAAI,EAAE;cACtBsC,GAAG,CAACI,UAAU,CAACnD,IAAI,CACjBhD,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACkE,UAAU,CAACT,IAAI,CAAC,EAAEqC,IAAI,CAACrC,IAAI,CAAC,CACjD,CAAC;YACH;UACF;UAEA,OAAOsC,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIJ,KAAK,CAAC1D,QAAQ,IAAI0D,KAAK,CAACtD,MAAM,EAAE;MAClC,IAAIa,IAAI,GAAG,CACTlD,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAChC+F,KAAK,CAAC1D,QAAQ,GAAGjC,WAAC,CAACoG,eAAe,CAACT,KAAK,CAAC1D,QAAQ,CAAC,GAAGjC,WAAC,CAACqG,WAAW,CAAC,CAAC,EACpEV,KAAK,CAACtD,MAAM,GAAGrC,WAAC,CAACoG,eAAe,CAACT,KAAK,CAACtD,MAAM,CAAC,GAAGrC,WAAC,CAACqG,WAAW,CAAC,CAAC,CACjE;MAED,IAAIC,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,IAAI,CAAC2C,MAAM,EAAEU,CAAC,EAAE,EAAE;QACpC,IAAI,CAACvG,WAAC,CAACwG,aAAa,CAACtD,IAAI,CAACqD,CAAC,CAAC,CAAC,EAAED,gBAAgB,GAAGC,CAAC;MACrD;MACArD,IAAI,GAAGA,IAAI,CAACuD,KAAK,CAAC,CAAC,EAAEH,gBAAgB,GAAG,CAAC,CAAC;MAE1C9E,IAAI,CAACwB,IAAI,CAAChD,WAAC,CAAC0G,mBAAmB,CAACzD,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC;MACzDvC,UAAU,CAACiB,iBAAiB,GAAG,IAAI;IACrC;EACF;EAEA,SAAS+E,aAAaA,CACpBC,SAAqC,EACrC3B,QAAsB,EACtB4B,OAA2B,EAC3BrF,IAAgC,EAChC;IACA,MAAMsF,aAAa,GAAGF,SAAS,CAAC9G,IAAI;IACpC,IAAIiH,IAAI;IAER,IAAItG,WAAW,CAACuG,0BAA0B,EAAE;MAC1CF,aAAa,CAACG,SAAS,CAACC,OAAO,CAAClH,WAAC,CAACmH,cAAc,CAAC,CAAC,CAAC;MACnD,IACEL,aAAa,CAACG,SAAS,CAACpB,MAAM,KAAK,CAAC,IACpC7F,WAAC,CAACoH,eAAe,CAACN,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,IAC7CjH,WAAC,CAACqH,YAAY,CAACP,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ,EAAE;QAClDC,IAAI,EAAE;MACR,CAAC,CAAC,EACF;QAEAT,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ;QAChER,aAAa,CAACU,MAAM,GAAGxH,WAAC,CAACyH,gBAAgB,CACvCzH,WAAC,CAACE,SAAS,CAAC+E,QAAQ,CAAC,EACrBjF,WAAC,CAACkE,UAAU,CAAC,OAAO,CACtB,CAAC;MACH,CAAC,MAAM;QACL4C,aAAa,CAACU,MAAM,GAAGxH,WAAC,CAACyH,gBAAgB,CACvCzH,WAAC,CAACE,SAAS,CAAC+E,QAAQ,CAAC,EACrBjF,WAAC,CAACkE,UAAU,CAAC,MAAM,CACrB,CAAC;MACH;MAEA6C,IAAI,GAAG/G,WAAC,CAAC0H,iBAAiB,CAAC,IAAI,EAAEZ,aAAa,EAAE9G,WAAC,CAACmH,cAAc,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MAAA,IAAAQ,qBAAA;MACL,MAAMzE,IAAoB,GAAG,CAC3BlD,WAAC,CAACmH,cAAc,CAAC,CAAC,EAClBnH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC;MACD,KAAA+H,qBAAA,GAAIb,aAAa,CAACG,SAAS,aAAvBU,qBAAA,CAAyB9B,MAAM,EAAE;QACnC,MAAM+B,sBAAsB,GAAGd,aAAa,CAACG,SAG1C;QAcH,IACEW,sBAAsB,CAAC/B,MAAM,KAAK,CAAC,IACnC7F,WAAC,CAACoH,eAAe,CAACQ,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAC5C5H,WAAC,CAACqH,YAAY,CAACO,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,EAAE;UACjDC,IAAI,EAAE;QACR,CAAC,CAAC,EACF;UACArE,IAAI,CAACF,IAAI,CAAC4E,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC;QAC/C,CAAC,MAAM;UACLpE,IAAI,CAACF,IAAI,CAAChD,WAAC,CAACoG,eAAe,CAACwB,sBAAsB,CAAC,CAAC;QACtD;MACF;MACAb,IAAI,GAAG/G,WAAC,CAACmD,cAAc,CAAC,IAAA0E,+BAAkB,EAAClH,UAAU,CAACL,IAAI,CAAC,EAAE4C,IAAI,CAAC;IACpE;IAEA,IACE0D,SAAS,CAACkB,UAAU,CAACC,qBAAqB,CAAC,CAAC,IAC5CnB,SAAS,CAACkB,UAAU,CAACE,SAAS,KAAKxG,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,IACjDA,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,CAACqE,MAAM,GAAG,CAAC,KAAKe,SAAS,CAACkB,UAAU,CAAC5B,GAAG,EACtD;MAIA,IAAIvF,UAAU,CAACc,WAAW,CAACoE,MAAM,EAAE;QACjCkB,IAAI,GAAG/G,WAAC,CAACiI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC;MACrD;MAEAH,SAAS,CAACkB,UAAU,CAACI,WAAW,CAAClI,WAAC,CAACmI,eAAe,CAACpB,IAAI,CAAC,CAAC;IAC3D,CAAC,MAAM;MACLH,SAAS,CAACsB,WAAW,CAAClI,WAAC,CAACiI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC;IACrE;EACF;EAEA,SAAS1C,iBAAiBA,CAAA,EAAG;IAC3B,IAAI,CAAC1D,UAAU,CAACO,SAAS,EAAE;IAE3B,MAAMb,IAAI,GAAGM,UAAU,CAACW,mBAAmB;IAC3C,MAAME,IAAI,GAAGnB,IAAI,CAACkD,GAAG,CAAC,MAAM,CAAC;IAE7BlD,IAAI,CAACsC,QAAQ,CAACD,iBAAiB,CAAC;IAEhC,IAAImE,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,MAAMuB,GAAG,GAAG/H,IAAI,CAACS,KAAK,CAACuH,6BAA6B,CAAC,MAAM,CAAC;MAC5DxB,OAAO,GAAGA,CAAA,KAAM7G,WAAC,CAACE,SAAS,CAACkI,GAAG,CAAC;MAChC,OAAOA,GAAG;IACZ,CAAC;IAED,KAAK,MAAME,QAAQ,IAAI3H,UAAU,CAACc,WAAW,EAAE;MAC7C,MAAM;QAAE3B,IAAI;QAAEgI;MAAW,CAAC,GAAGQ,QAAQ;MACrC,IAAIR,UAAU,CAACS,kBAAkB,CAAC;QAAEC,MAAM,EAAE1I;MAAK,CAAC,CAAC,EAAE;QACnDwI,QAAQ,CAACJ,WAAW,CAACrB,OAAO,CAAC,CAAC,CAAC;QAC/B;MACF;MACAyB,QAAQ,CAACJ,WAAW,CAClBlI,WAAC,CAACmD,cAAc,CAACxC,UAAU,CAACL,IAAI,CAAC8C,SAAS,CAAC,uBAAuB,CAAC,EAAE,CACnEyD,OAAO,CAAC,CAAC,CACV,CACH,CAAC;IACH;IAEA,MAAM4B,UAAwC,GAAG,EAAE;IACnDpI,IAAI,CAACsC,QAAQ,CACXA,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACtBC,iCAAkB,EAClB;MACE4F,KAAKA,CAACrI,IAAI,EAAE;QACV,MAAM;UAAEP,IAAI;UAAEgI;QAAW,CAAC,GAAGzH,IAAI;QACjC,IAAIyH,UAAU,CAACa,gBAAgB,CAAC;UAAEnB,MAAM,EAAE1H;QAAK,CAAC,CAAC,EAAE;UACjD2I,UAAU,CAACvB,OAAO,CAACY,UAAU,CAAC;QAChC;MACF;IACF,CAAC,CACF,CACH,CAAC;IAED,IAAIc,2BAA2B,GAAG,CAAC,CAACH,UAAU,CAAC5C,MAAM;IAErD,KAAK,MAAMe,SAAS,IAAI6B,UAAU,EAAE;MAClC9B,aAAa,CAACC,SAAS,EAAEjG,UAAU,CAACK,SAAS,EAAE6F,OAAO,EAAErF,IAAI,CAAC;MAE7D,IAAIoH,2BAA2B,EAAE;QAC/BhC,SAAS,CAACiC,IAAI,CAAC,UAAUf,UAAU,EAAE;UAEnC,IAAIA,UAAU,KAAKzH,IAAI,EAAE;YACvB,OAAO,IAAI;UACb;UAEA,IACEyH,UAAU,CAACgB,MAAM,CAAC,CAAC,IACnBhB,UAAU,CAACiB,aAAa,CAAC,CAAC,IAC1BjB,UAAU,CAACvC,yBAAyB,CAAC,CAAC,EACtC;YACAqD,2BAA2B,GAAG,KAAK;YACnC,OAAO,IAAI;UACb;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAII,UAAU;IAEd,IAAIrI,UAAU,CAACH,OAAO,EAAE;MACtBwI,UAAU,GAAIC,SAA8B,IAAK;QAC/C,MAAMC,QAAQ,GAAGlJ,WAAC,CAACmD,cAAc,CAC/BxC,UAAU,CAACL,IAAI,CAAC8C,SAAS,CAAC,uBAAuB,CAAC,EAClD,CAACyD,OAAO,CAAC,CAAC,CACZ,CAAC;QACD,OAAOoC,SAAS,GACZjJ,WAAC,CAAC0H,iBAAiB,CAAC,IAAI,EAAEuB,SAAS,EAAEC,QAAQ,CAAC,GAC9CA,QAAQ;MACd,CAAC;IACH,CAAC,MAAM;MACLF,UAAU,GAAIC,SAAmC,IAAK;QACpD,MAAME,YAA4B,GAAG,CAACtC,OAAO,CAAC,CAAC,CAAC;QAChD,IAAIoC,SAAS,IAAI,IAAI,EAAE;UACrBE,YAAY,CAACnG,IAAI,CAACiG,SAAS,CAAC;QAC9B;QACA,OAAOjJ,WAAC,CAACmD,cAAc,CACrBxC,UAAU,CAACL,IAAI,CAAC8C,SAAS,CAAC,2BAA2B,CAAC,EACtD+F,YACF,CAAC;MACH,CAAC;IACH;IAIA,MAAMC,SAAS,GAAG5H,IAAI,CAAC+B,GAAG,CAAC,MAAM,CAAC;IAClC,IAAI,CAAC6F,SAAS,CAACvD,MAAM,IAAI,CAACuD,SAAS,CAACC,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC7D9H,IAAI,CAAC+H,aAAa,CAChB,MAAM,EACNvJ,WAAC,CAACmI,eAAe,CACfS,2BAA2B,GAAG/B,OAAO,CAAC,CAAC,GAAGmC,UAAU,CAAC,CACvD,CACF,CAAC;IACH;IAEA,KAAK,MAAMQ,UAAU,IAAI7I,UAAU,CAACM,YAAY,EAAE;MAChDuI,UAAU,CACPjG,GAAG,CAAC,UAAU,CAAC,CACf2E,WAAW,CAACc,UAAU,CAACQ,UAAU,CAAC1J,IAAI,CAACwH,QAAQ,CAAC,CAAC;IACtD;EACF;EAKA,SAAS7B,UAAUA,CAAC3F,IAAmB,EAAEO,IAAe,EAAE;IACxD,MAAMS,KAAK,GAAGT,IAAI,GAAGA,IAAI,CAACS,KAAK,GAAGH,UAAU,CAACG,KAAK;IAElD,IAAIhB,IAAI,CAAC2D,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAIgG,aAAa,CAAC3J,IAAI,EAAEgB,KAAK,CAAC,EAAE;IAClC;IAEA,MAAM8E,SAAS,GAAG9F,IAAI,CAACuC,MAAM,GAAG,QAAQ,GAAG,UAAU;IACrD,MAAML,OAAO,GAAGrB,UAAU,CAACqB,OAAO,CAAC4D,SAAS,CAAC;IAE7C,MAAM8D,OAAO,GAAG5J,IAAI,CAAC2D,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAG3D,IAAI,CAAC2D,IAAI;IAC5D,MAAMyC,GAAG,GACPlG,WAAC,CAAC2J,gBAAgB,CAAC7J,IAAI,CAACoG,GAAG,CAAC,IAAIlG,WAAC,CAAC4J,eAAe,CAAC9J,IAAI,CAACoG,GAAG,CAAC,GACvDlG,WAAC,CAAC6J,aAAa,CAACC,MAAM,CAAChK,IAAI,CAACoG,GAAG,CAAC6D,KAAK,CAAC,CAAC,GACvC/J,WAAC,CAACgK,aAAa,CAAClK,IAAI,CAAC;IAE3B,IAAImK,EAAgB,GAAGjK,WAAC,CAACkK,YAAY,CAACpK,IAAI,CAAC;IAE3C,IAAIE,WAAC,CAACmK,eAAe,CAACjE,GAAG,CAAC,EAAE;MAE1B,IAAIpG,IAAI,CAAC2D,IAAI,KAAK,QAAQ,EAAE;QAAA,IAAA2G,aAAA;QAG1BH,EAAE,IAAAG,aAAA,GACA,IAAAC,2BAAY,EAGV;UAAEC,EAAE,EAAEpE,GAAG;UAAEpG,IAAI,EAAEA,IAAI;UAAEgB;QAAM,CAAC,EAC9BD,SAAS,EACTH,gBACF,CAAC,YAAA0J,aAAA,GAAIH,EAAE;MACX;IACF,CAAC,MAAM;MAELjI,OAAO,CAACE,WAAW,GAAG,IAAI;IAC5B;IAEA,IAAIqI,UAAsB;IAC1B,IACE,CAACvI,OAAO,CAACE,WAAW,IACpBF,OAAO,CAACI,GAAG,CAACoI,GAAG,CAAEtE,GAAG,CAAqB6D,KAAK,CAAC,EAC/C;MACAQ,UAAU,GAAGvI,OAAO,CAACI,GAAG,CAACmB,GAAG,CAAE2C,GAAG,CAAqB6D,KAAK,CAAC;MAC5DQ,UAAU,CAACb,OAAO,CAAC,GAAGO,EAAE;MAExB,IAAIP,OAAO,KAAK,OAAO,EAAE;QACvBa,UAAU,CAAChH,GAAG,GAAG,IAAI;QACrBgH,UAAU,CAACE,GAAG,GAAG,IAAI;MACvB,CAAC,MAAM;QACLF,UAAU,CAACR,KAAK,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACLQ,UAAU,GAAG;QACXrE,GAAG,EAEDA,GAAmB;QACrB,CAACwD,OAAO,GAAGO;MACb,CAAe;MACfjI,OAAO,CAACG,IAAI,CAACa,IAAI,CAACuH,UAAU,CAAC;MAE7B,IAAI,CAACvI,OAAO,CAACE,WAAW,EAAE;QACxBF,OAAO,CAACI,GAAG,CAACqI,GAAG,CAAEvE,GAAG,CAAqB6D,KAAK,EAAEQ,UAAU,CAAC;MAC7D;IACF;EACF;EAEA,SAASd,aAAaA,CAAC3J,IAAmB,EAAEgB,KAAY,EAAE;IACxD,IAAIL,WAAW,CAACiK,eAAe,IAAI,CAAC5K,IAAI,CAAC6E,UAAU,EAAE;MAEnD,IAAI;QAAE/E;MAAS,CAAC,GAAGe,UAAU;MAC7B,IAAI,CAACb,IAAI,CAACuC,MAAM,EAAE;QAChBsI,oBAAoB,CAAC,CAAC;QACtB/K,QAAQ,GAAGe,UAAU,CAACkB,UAAU;MAClC;MACA,MAAM+I,UAAU,GAAG5K,WAAC,CAACyH,gBAAgB,CACnCzH,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrBE,IAAI,CAACoG,GAAG,EACRpG,IAAI,CAAC+K,QAAQ,IAAI7K,WAAC,CAAC8K,SAAS,CAAChL,IAAI,CAACoG,GAAG,CACvC,CAAC;MAED,IAAInG,IAAkB,GAAGC,WAAC,CAAC+K,kBAAkB,CAC3C,IAAI,EAEJjL,IAAI,CAAC4D,MAAM,EACX5D,IAAI,CAAC0B,IAAI,EACT1B,IAAI,CAACkL,SAAS,EACdlL,IAAI,CAACmL,KACP,CAAC;MACDjL,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;MAEtB,MAAMoG,GAAG,GAAGlG,WAAC,CAACgK,aAAa,CAAClK,IAAI,EAAEA,IAAI,CAACoG,GAAG,CAAC;MAC3C,IAAIlG,WAAC,CAACmK,eAAe,CAACjE,GAAG,CAAC,EAAE;QAAA,IAAAgF,cAAA;QAE1BnL,IAAI,IAAAmL,cAAA,GACF,IAAAb,2BAAY,EACV;UACEvK,IAAI,EAAEC,IAAI;UACVuK,EAAE,EAAEpE,GAAG;UACPpF;QACF,CAAC,EACDD,SAAS,EACTH,gBACF,CAAC,YAAAwK,cAAA,GAAInL,IAAI;MACb;MAEA,MAAMoL,IAAI,GAAGnL,WAAC,CAAC0G,mBAAmB,CAChC1G,WAAC,CAACiI,oBAAoB,CAAC,GAAG,EAAE2C,UAAU,EAAE7K,IAAI,CAC9C,CAAC;MACDC,WAAC,CAACoL,gBAAgB,CAACD,IAAI,EAAErL,IAAI,CAAC;MAC9Ba,UAAU,CAACa,IAAI,CAACwB,IAAI,CAACmI,IAAI,CAAC;MAC1B,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA,SAASR,oBAAoBA,CAAA,EAAG;IAC9B,IAAIhK,UAAU,CAACkB,UAAU,KAAK,IAAI,EAAE;MAClCS,QAAQ,CAAC;QAAET,UAAU,EAAElB,UAAU,CAACG,KAAK,CAACuK,qBAAqB,CAAC,OAAO;MAAE,CAAC,CAAC;MACzE,MAAMC,UAAU,GAAGtL,WAAC,CAACyH,gBAAgB,CACnC9G,UAAU,CAACf,QAAQ,EACnBI,WAAC,CAACkE,UAAU,CAAC,WAAW,CAC1B,CAAC;MACD,MAAMqH,gBAAgB,GAAGvL,WAAC,CAACwL,mBAAmB,CAAC,KAAK,EAAE,CACpDxL,WAAC,CAACyL,kBAAkB,CAAC9K,UAAU,CAACkB,UAAU,EAAEyJ,UAAU,CAAC,CACxD,CAAC;MAEF3K,UAAU,CAACa,IAAI,CAACwB,IAAI,CAACuI,gBAAgB,CAAC;IACxC;EACF;EAKA,SAAS/F,eAAeA,CACtBvE,YAA2C,EAC3CyK,MAAwB,EACxBrL,IAAgC,EAChC;IACAiC,QAAQ,CAAC;MACPhB,mBAAmB,EAAEjB,IAAI;MACzBgB,eAAe,EAAEqK,MAAM;MACvBnK,cAAc,EAAE,IAAI;MACpBN;IACF,CAAC,CAAC;IAEF,MAAM;MAAEG;IAAU,CAAC,GAAGT,UAAU;IAEhCX,WAAC,CAACoL,gBAAgB,CAAChK,SAAS,EAAEsK,MAAM,CAAC;IAGrCtK,SAAS,CAACsC,MAAM,GAAGgI,MAAM,CAAChI,MAAM;IAEhC1D,WAAC,CAACG,QAAQ,CAACiB,SAAS,CAACI,IAAI,EAAEkK,MAAM,CAAClK,IAAI,CAAC;IACvCJ,SAAS,CAACI,IAAI,CAACmK,UAAU,GAAGD,MAAM,CAAClK,IAAI,CAACmK,UAAU;IAElDC,qBAAqB,CAAC,CAAC;EACzB;EAEA,SAASA,qBAAqBA,CAAA,EAAG;IAC/B,IAAIjL,UAAU,CAACe,iBAAiB,EAAE;IAClCf,UAAU,CAACe,iBAAiB,GAAG,IAAI;IAInC,IAAIf,UAAU,CAACkL,sBAAsB,IAAIlL,UAAU,CAACmL,oBAAoB,EAAE;MACxExH,eAAe,CAAC,CAAC;IACnB;IAEA3D,UAAU,CAACa,IAAI,CAACwB,IAAI,CAACrC,UAAU,CAACS,SAAS,CAAC;IAE1CsE,kBAAkB,CAAC,CAAC;EACtB;EAKA,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAC/E,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACgB,cAAc,EAAE;IAExDhB,UAAU,CAACgB,cAAc,GAAG,IAAI;IAKhChB,UAAU,CAACa,IAAI,CAAC0F,OAAO,CACrBlH,WAAC,CAAC0G,mBAAmB,CACnB1G,WAAC,CAACmD,cAAc,CACdxC,UAAU,CAACL,IAAI,CAAC8C,SAAS,CACvBzC,UAAU,CAACH,OAAO,GAAG,eAAe,GAAG,UACzC,CAAC,EACD,CAACR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAAEI,WAAC,CAACE,SAAS,CAACS,UAAU,CAACK,SAAS,CAAC,CACtE,CACF,CACF,CAAC;EACH;EAEA,SAAS+K,kBAAkBA,CAAA,EAAG;IAC5B,MAAM;MAAEjK,WAAW;MAAEhC,IAAI;MAAEgB;IAAM,CAAC,GAAGH,UAAU;IAE/C,KAAK,MAAMqL,IAAI,IAAIlM,IAAI,CAAC0B,IAAI,CAACA,IAAI,EAAE;MACjC,IAAI,CAACxB,WAAC,CAACwD,aAAa,CAACwI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACnB,QAAQ,EAAE;MAC9C,IAAI/J,KAAK,CAACmL,MAAM,CAACD,IAAI,CAAC9F,GAAG,EAAsB,IAAI,CAAC,EAAE;MAEtD,MAAMoE,EAAE,GAAGxJ,KAAK,CAACoL,gCAAgC,CAACF,IAAI,CAAC9F,GAAG,CAAC;MAC3DpE,WAAW,CAAC2I,GAAG,CAACH,EAAE,CAAC/C,IAAI,EAAEyE,IAAI,CAAC9F,GAAG,CAAC;MAElC8F,IAAI,CAAC9F,GAAG,GAAGoE,EAAE;IACf;EACF;EAEA,SAAS6B,sBAAsBA,CAAA,EAAG;IAChC,MAAM;MAAEnL,SAAS;MAAEc;IAAY,CAAC,GAAGnB,UAAU;IAC7C,MAAMyL,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IAEtB,IAAI1L,UAAU,CAACO,SAAS,EAAE;MACxB,IAAIoL,GAAG,GAAGtM,WAAC,CAACE,SAAS,CAACc,SAAS,CAAC;MAChC,IAAIL,UAAU,CAACQ,aAAa,EAAE;QAC5BmL,GAAG,GAAGtM,WAAC,CAACmD,cAAc,CAACxC,UAAU,CAACL,IAAI,CAAC8C,SAAS,CAAC,iBAAiB,CAAC,EAAE,CACnEkJ,GAAG,CACJ,CAAC;QACF,IAAAC,6BAAc,EAACD,GAAG,CAAC;MACrB;MAEA,MAAME,KAAK,GACT7L,UAAU,CAACG,KAAK,CAACoL,gCAAgC,CAAClL,SAAS,CAAC;MAE9DoL,aAAa,CAACpJ,IAAI,CAACwJ,KAAK,CAAC;MACzBH,WAAW,CAACrJ,IAAI,CAACsJ,GAAG,CAAC;MAErBhK,QAAQ,CAAC;QAAEtB,SAAS,EAAEhB,WAAC,CAACE,SAAS,CAACsM,KAAK;MAAE,CAAC,CAAC;IAC7C;IAEA,KAAK,MAAM,CAACjF,IAAI,EAAEwC,KAAK,CAAC,IAAIjI,WAAW,EAAE;MACvCsK,aAAa,CAACpJ,IAAI,CAAChD,WAAC,CAACkE,UAAU,CAACqD,IAAI,CAAC,CAAC;MACtC8E,WAAW,CAACrJ,IAAI,CAAC+G,KAAK,CAAC;IACzB;IAEA,OAAO;MAAEqC,aAAa;MAAEC;IAAY,CAAC;EACvC;EAEA,SAASI,gBAAgBA,CACvBpM,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChB;IACA8B,QAAQ,CAAC;MACP1B,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBE,KAAK,EAAET,IAAI,CAACS,KAAK;MACjBhB,IAAI,EAAEO,IAAI,CAACP,IAAI;MACfO,IAAI;MACJC,IAAI;MACJE;IACF,CAAC,CAAC;IAEF8B,QAAQ,CAAC;MACPvB,OAAO,EAAEJ,UAAU,CAACb,IAAI,CAACwK,EAAE;MAE3B1K,QAAQ,EAAEe,UAAU,CAACb,IAAI,CAACwK,EAAE,GACxBtK,WAAC,CAACkE,UAAU,CAACvD,UAAU,CAACb,IAAI,CAACwK,EAAE,CAAC/C,IAAI,CAAC,GACrC5G,UAAU,CAACG,KAAK,CAACuK,qBAAqB,CAAC,OAAO,CAAC;MACnDrK,SAAS,EAAEL,UAAU,CAACb,IAAI,CAAC4M,UAAU;MACrCxL,SAAS,EAAE,CAAC,CAACP,UAAU,CAACb,IAAI,CAAC4M,UAAU;MACvC7M,eAAe,EAAEG,WAAC,CAAC+D,cAAc,CAAC,EAAE;IACtC,CAAC,CAAC;IAEFzB,QAAQ,CAAC;MACPnB,aAAa,EACXnB,WAAC,CAACqH,YAAY,CAAC1G,UAAU,CAACK,SAAS,CAAC,IACpCT,cAAc,CAACiK,GAAG,CAAC7J,UAAU,CAACK,SAAS,CAACuG,IAAI,CAAC,IAC7C,CAAC5G,UAAU,CAACG,KAAK,CAAC6L,UAAU,CAC1BhM,UAAU,CAACK,SAAS,CAACuG,IAAI,EACT,IAClB;IACJ,CAAC,CAAC;IAEF,MAAM;MAAE3H,QAAQ;MAAEE,IAAI;MAAED;IAAgB,CAAC,GAAGc,UAAU;IAEtD2B,QAAQ,CAAC;MACPlB,SAAS,EAAEzB,gBAAgB,CAACC,QAAQ,EAAEC,eAAe,EAAEC,IAAI;IAC7D,CAAC,CAAC;IAEFiM,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAEvK;IAAK,CAAC,GAAGb,UAAU;IAC3B,MAAM;MAAEyL,aAAa;MAAEC;IAAY,CAAC,GAAGF,sBAAsB,CAAC,CAAC;IAE/DhI,SAAS,CAAC,CAAC;IAGX,IAAI,CAAC1D,WAAW,CAACmM,YAAY,EAAE;MAC7B/M,eAAe,CAAC2B,IAAI,CAAC0F,OAAO,CAC1BlH,WAAC,CAAC0G,mBAAmB,CACnB1G,WAAC,CAACmD,cAAc,CAACxC,UAAU,CAACL,IAAI,CAAC8C,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC5DpD,WAAC,CAACmH,cAAc,CAAC,CAAC,EAClBnH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CACF,CAAC;IACH;IAEA,MAAMiN,QAAQ,GAAGxM,IAAI,CAACyM,cAAc,CAAC,CAAC;IACtC,IAAIC,eAAe,GAAGpM,UAAU,CAACI,OAAO,IAAIS,IAAI,CAACqE,MAAM,KAAK,CAAC;IAC7D,IAAIkH,eAAe,IAAI,CAACF,QAAQ,EAAE;MAChC,KAAK,MAAML,KAAK,IAAI7L,UAAU,CAACS,SAAS,CAACsC,MAAM,EAAE;QAI/C,IAAI,CAAC1D,WAAC,CAACqH,YAAY,CAACmF,KAAK,CAAC,EAAE;UAC1BO,eAAe,GAAG,KAAK;UACvB;QACF;MACF;IACF;IAEA,MAAMpB,UAAU,GAAGoB,eAAe,GAC7BvL,IAAI,CAAC,CAAC,CAAC,CAAkDA,IAAI,CAC3DmK,UAAU,GACb,EAAE;IACN,IAAI,CAACkB,QAAQ,EAAE;MACblB,UAAU,CAAC3I,IAAI,CAAChD,WAAC,CAACgN,SAAS,CAAChN,WAAC,CAACiN,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE;IAEA,IAAIF,eAAe,EAAE;MAEnB,MAAM5B,IAAI,GAAGnL,WAAC,CAACkK,YAAY,CACzB1I,IAAI,CAAC,CAAC,CACR,CAAC;MACD,OAAOb,UAAU,CAACH,OAAO,GAAG2K,IAAI,GAAGlI,iBAAiB,CAAC,CAACkI,IAAI,CAAC,CAAC;IAC9D;IAEA,IAAIlC,SAAuB,GAAGjJ,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC;IAC9D,IAAI,CAACe,UAAU,CAACiB,iBAAiB,IAAI,CAACjB,UAAU,CAACH,OAAO,EAAE;MACxDyI,SAAS,GAAGhG,iBAAiB,CAAC,CAACgG,SAAS,CAAC,CAAC;IAC5C;IAEAzH,IAAI,CAACwB,IAAI,CAAChD,WAAC,CAACmI,eAAe,CAACc,SAAS,CAAC,CAAC;IACvC,MAAMjB,SAAS,GAAGhI,WAAC,CAACkN,uBAAuB,CACzCd,aAAa,EACbpM,WAAC,CAAC+D,cAAc,CAACvC,IAAI,EAAEmK,UAAU,CACnC,CAAC;IACD,OAAO3L,WAAC,CAACmD,cAAc,CAAC6E,SAAS,EAAEqE,WAAW,CAAC;EACjD;EAEA,OAAOI,gBAAgB,CAACpM,IAAI,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;AAC9D"}