<template>
  <view class="container">
    <!-- 头部标题 -->
    <view class="header">
      <view class="back-icon" @click="goBack">
        <text class="iconfont icon-left"></text>
      </view>
      <view class="title">导入店员用户</view>
    </view>

    <!-- 店员总数 -->
    <view class="staff-count">
      <text>店员总计{{ staffList.length }}人</text>
    </view>

    <!-- 店员列表 -->
    <scroll-view scroll-y="true" class="staff-list">
      <!-- 店铺信息 -->
      <view class="shop-info">
        <view class="shop-title">
          <text>{{ shopInfo.shopName || '完美情怀店铺' }}</text>
        </view>
      </view>
      
      <!-- 店员列表项 -->
      <view v-for="(staff, index) in staffList" :key="index" 
            :class="['staff-item', {'staff-item-selected': selectedStaffId === staff.staff_id}]" 
            @click="selectStaff(staff)">
        <text class="staff-code">{{ staff.staff_code || 'A0' + (index + 1) }}</text>
        <text class="staff-name">{{ staff.staff_name }}</text>
        <text class="staff-count">[{{ staff.assigned_user_count || staff.userCount || 0 }}]</text>
      </view>
      
      <!-- 空数据提示 -->
      <view v-if="staffList.length === 0" class="empty-data">
        <text>暂无店员数据</text>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="cancel-btn" @click="goBack">取消</button>
      <button class="import-btn" @click="importStaffUsers" :disabled="!selectedStaffId">导入</button>
    </view>
  </view>
</template>

<script>
import { getShopStaffList, getStaffAssignedUsers } from '@/api/work/retrun_part.js'

export default {
  data() {
    return {
      shopId: '', // 店铺ID
      regionId: '', // 区域ID
      staffList: [], // 店员列表
      selectedStaffId: null, // 选中的店员ID
      shopInfo: {}, // 店铺信息
    }
  },
  onLoad(options) {
    if (options.shopId) {
      this.shopId = options.shopId;
    }
    if (options.regionId) {
      this.regionId = options.regionId;
    }
    
    // 加载店员列表（首次加载时强制刷新）
    this.loadStaffList(true);
  },
  // 页面重新获得焦点时刷新
  onReady() {
    // uni-app页面准备完成
    console.log('页面准备完成');
  },
  // 页面从后台恢复
  onShow() {
    // 如果已经加载过(shopId存在)，则强制刷新店员列表
    if (this.shopId) {
      console.log('shopassigondev页面显示，强制刷新店员列表');
      
      // 清空选择状态
      this.selectedStaffId = null;
      
      // 强制刷新数据
      this.loadStaffList(true);
      
      // 添加延迟刷新，确保数据更新
      setTimeout(() => {
        console.log('执行延时二次刷新，确保获取最新数据');
        this.loadStaffList(true);
      }, 1000);
    }
  },
  // 页面隐藏
  onHide() {
    console.log('页面隐藏');
  },
  // 页面销毁前清理
  onUnload() {
    console.log('页面卸载');
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 加载店员列表
    loadStaffList(forceRefresh = false) {
      console.log('开始加载店员列表, 强制刷新:', forceRefresh);
      
      uni.showLoading({
        title: '加载中...'
      });
      
      // 构建请求参数
      const params = {
        shopId: this.shopId
      };
      
      // 如果强制刷新，添加时间戳和随机数防止缓存
      if (forceRefresh) {
        params.timestamp = Date.now();
        params.random = Math.random();
        console.log('添加防缓存参数:', params);
      }
      
      // 由于API不支持请求选项，直接传递数据参数
      getShopStaffList(params).then(res => {
        uni.hideLoading();
        
        if (res.code === 200 && res.data) {
          console.log('成功获取店员列表数据, 店员数量:', (res.data.staffList || []).length);
          this.staffList = res.data.staffList || [];
          this.shopInfo = res.data.shopInfo || {};
          
          console.log('店员列表详情:', this.staffList.map(s => 
            `${s.staff_name}(${s.assigned_user_count || 0})`).join(', '));
        } else {
          console.error('获取店员列表失败:', res);
          uni.showToast({
            title: res.msg || '获取店员列表失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.hideLoading();
        console.error('获取店员列表异常:', err);
        uni.showToast({
          title: '获取店员列表异常',
          icon: 'none'
        });
      });
    },
    
    // 选择店员
    selectStaff(staff) {
      this.selectedStaffId = staff.staff_id;
    },
    
    // 导入店员用户
    importStaffUsers() {
      if (!this.selectedStaffId) {
        uni.showToast({
          title: '请先选择店员',
          icon: 'none'
        });
        return;
      }
      
      // 导航到userassigondev页面，传递店员ID
      uni.navigateTo({
        url: './userassigondev?staffId=' + this.selectedStaffId + 
             '&shopId=' + this.shopId + 
             '&regionId=' + this.regionId
      });
    },
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.back-icon {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.staff-count {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-top: 2rpx;
  font-size: 28rpx;
  color: #666666;
}

.staff-list {
  flex: 1;
  background-color: #ffffff;
  margin-top: 2rpx;
  padding-bottom: 120rpx;
}

.shop-info {
  padding: 10rpx 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.shop-title {
  font-size: 30rpx;
  font-weight: 500;
  padding: 10rpx 0;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eeeeee;
  transition: all 0.3s;
}

.staff-item-selected {
  background-color: #e6f7ff;
  border-left: 4rpx solid #1890ff;
}

.staff-code {
  font-size: 28rpx;
  color: #333333;
  margin-right: 20rpx;
  width: 80rpx;
}

.staff-name {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
}

.staff-count {
  font-size: 28rpx;
  color: #999999;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999999;
  font-size: 28rpx;
}

.footer-buttons {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
  box-sizing: border-box;
}

.cancel-btn, .import-btn {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 32rpx;
  border: none;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666666;
}

.import-btn {
  background-color: #1890ff;
  color: #ffffff;
}

.import-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
  opacity: 0.7;
}
</style>
