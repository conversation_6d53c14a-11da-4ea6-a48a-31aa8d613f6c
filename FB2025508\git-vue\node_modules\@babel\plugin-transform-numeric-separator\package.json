{"_from": "@babel/plugin-transform-numeric-separator@^7.23.4", "_id": "@babel/plugin-transform-numeric-separator@7.23.4", "_inBundle": false, "_integrity": "sha512-mps6auzgwjRrwKEZA05cOwuDc9FAzoyFS4ZsG/8F43bTLf/TgkJg7QXOrPO1JO599iA3qgK9MXdMGOEC8O1h6Q==", "_location": "/@babel/plugin-transform-numeric-separator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-numeric-separator@^7.23.4", "name": "@babel/plugin-transform-numeric-separator", "escapedName": "@babel%2fplugin-transform-numeric-separator", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.23.4.tgz", "_shasum": "03d08e3691e405804ecdd19dd278a40cca531f29", "_spec": "@babel/plugin-transform-numeric-separator@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "deprecated": false, "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.4", "@babel/types": "^7.23.4"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-numeric-separator", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "type": "commonjs", "version": "7.23.4"}