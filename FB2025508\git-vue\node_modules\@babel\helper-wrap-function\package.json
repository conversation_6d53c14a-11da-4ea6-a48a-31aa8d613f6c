{"_from": "@babel/helper-wrap-function@^7.22.20", "_id": "@babel/helper-wrap-function@7.22.20", "_inBundle": false, "_integrity": "sha512-pms/UwkOpnQe/PDAEdV/d7dVCoBbB+R4FvYoHGZz+4VPcg7RtYy2KP7S2lbuWM6FCSgob5wshfGESbC/hzNXZw==", "_location": "/@babel/helper-wrap-function", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-wrap-function@^7.22.20", "name": "@babel/helper-wrap-function", "escapedName": "@babel%2fhelper-wrap-function", "scope": "@babel", "rawSpec": "^7.22.20", "saveSpec": null, "fetchSpec": "^7.22.20"}, "_requiredBy": ["/@babel/helper-remap-async-to-generator"], "_resolved": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.20.tgz", "_shasum": "15352b0b9bfb10fc9c76f79f6342c00e3411a569", "_spec": "@babel/helper-wrap-function@^7.22.20", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-remap-async-to-generator", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-function-name": "^7.22.5", "@babel/template": "^7.22.15", "@babel/types": "^7.22.19"}, "deprecated": false, "description": "Helper to wrap functions inside a function call.", "devDependencies": {"@babel/traverse": "^7.22.20"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-wrap-function", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-wrap-function"}, "type": "commonjs", "version": "7.22.20"}