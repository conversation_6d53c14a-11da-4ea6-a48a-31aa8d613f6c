<template>
	<view class="user-details-container">
		<view class="header">
			<view class="back-button" @click="goBack">
				<text class="iconfont icon-back"></text>
				<text>返回</text>
			</view>
			<view class="title">用户详情</view>
		</view>
		
		<scroll-view class="content" scroll-y="true">
			<view class="loading-text" v-if="loading">
				<text>正在加载用户数据...</text>
			</view>
			<view class="error-text" v-else-if="error">
				<text>{{error}}</text>
			</view>
			<view class="user-info-section" v-else-if="userData">
				<!-- 基本信息 -->
				<view class="info-section">
					<view class="info-row">
						<view class="info-label">姓名</view>
						<view class="info-value">{{userData.user_name || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">性别</view>
						<view class="info-value">{{getGenderText(userData.gender)}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">出生年月</view>
						<view class="info-value">{{formatDate(userData.birth_date)}} ({{calculateAge(userData.birth_date)}}岁)</view>
					</view>
					<view class="info-row">
						<view class="info-label">身高</view>
						<view class="info-value">{{userData.user_height || ''}}cm</view>
					</view>
					<view class="info-row">
						<view class="info-label">体重</view>
						<view class="info-value">{{userData.user_weight || ''}}kg</view>
					</view>
					<view class="info-row">
						<view class="info-label">毕业院校</view>
						<view class="info-value">{{userData.almamater || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">所学专业</view>
						<view class="info-value">{{userData.major || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">工作单位</view>
						<view class="info-value">{{userData.workunit || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">收入</view>
						<view class="info-value">{{userData.income || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">爱好</view>
						<view class="info-value">{{userData.hobby || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">对女生的希望</view>
						<view class="info-value">{{userData.hopegirls || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">联系电话</view>
						<view class="info-value">{{userData.phone || ''}}</view>
					</view>
					<view class="info-row">
						<view class="info-label">微信号</view>
						<view class="info-value">{{userData.wchat || ''}}</view>
					</view>
				</view>
				
				<!-- 家庭信息 -->
				<view class="info-section">
					<view class="section-title">父母家庭情况</view>
					<view class="info-content">
						<text class="family-info">{{userData.familyinfo || '暂无家庭信息'}}</text>
					</view>
				</view>
				
				<!-- 补充信息 -->
				<view class="info-section" v-if="userData && userData.ishavaorder === '1'">
					<view class="section-title">补充信息</view>
					<view class="info-content">
						<textarea 
							class="add-info-textarea" 
							v-model="addInfo" 
							placeholder="请输入补充信息" 
							maxlength="255"
						></textarea>
					</view>
					<view class="button-row">
						<button class="save-btn" @click="saveAddInfo">保存补充信息</button>
					</view>
				</view>
			</view>
			<view class="no-data" v-else>
				<text>未找到用户信息</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { getUserDetail, updateUserAddInfo } from '@/api/work/centerorder.js'
	
	export default {
		data() {
			return {
				userId: null,
				userData: null,
				addInfo: '',
				loading: false,
				error: null
			}
		},
		onLoad(options) {
			// 从路由参数中获取userId
			if (options && options.userId) {
				this.userId = options.userId;
				// 加载用户详情数据
				this.loadUserData();
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 加载用户详情数据
			loadUserData() {
				this.loading = true;
				this.error = null;
				
				getUserDetail({ user_id: this.userId })
					.then(res => {
						if (res.code === 200) {
							this.userData = res.data;
							this.addInfo = res.data.addinfo || '';
						} else {
							throw new Error(res.msg || '获取用户详情失败');
						}
					})
					.catch(err => {
						this.error = err.message || '获取用户详情失败';
						uni.showToast({
							title: this.error,
							icon: 'none'
						});
					})
					.finally(() => {
						this.loading = false;
					});
			},
			
			// 保存补充信息
			saveAddInfo() {
				if (this.userId) {
					uni.showLoading({
						title: '保存中...'
					});
					
					updateUserAddInfo({
						user_id: this.userId,
						addinfo: this.addInfo
					})
					.then(res => {
						if (res.code === 200) {
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							});
							// 更新本地数据
							if (this.userData) {
								this.userData.addinfo = this.addInfo;
							}
						} else {
							throw new Error(res.msg || '保存失败');
						}
					})
					.catch(err => {
						uni.showToast({
							title: err.message || '保存失败',
							icon: 'none'
						});
					})
					.finally(() => {
						uni.hideLoading();
					});
				}
			},
			
			// 获取性别文本
			getGenderText(gender) {
				switch(gender) {
					case '0': return '男';
					case '1': return '女';
					default: return '未知';
				}
			},
			
			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return '';
				
				try {
					const date = new Date(dateString);
					return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
				} catch (e) {
					console.error('日期格式化错误:', e);
					return dateString;
				}
			},
			
			// 计算年龄
			calculateAge(birthDate) {
				if (!birthDate) return '';
				
				try {
					const birth = new Date(birthDate);
					const now = new Date();
					
					// 检查日期是否有效
					if (isNaN(birth.getTime())) return '';
					
					let age = now.getFullYear() - birth.getFullYear();
					
					// 检查是否已过生日
					if (now.getMonth() < birth.getMonth() || 
						(now.getMonth() === birth.getMonth() && now.getDate() < birth.getDate())) {
						age--;
					}
					
					return age > 0 ? age : 0; // 确保年龄不为负数
				} catch (e) {
					console.error('计算年龄出错:', e);
					return '';
				}
			}
		}
	}
</script>

<style>
	.user-details-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f7fa;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
	}
	
	.header {
		padding: 12px 16px;
		background-color: #fff;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #eaeef3;
		box-shadow: 0 1px 3px rgba(0,0,0,0.05);
		position: relative;
	}
	
	.back-button {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #007aff;
	}
	
	.icon-back {
		margin-right: 4px;
	}
	
	.title {
		position: absolute;
		left: 0;
		right: 0;
		text-align: center;
		font-size: 16px;
		font-weight: 600;
		color: #333;
		pointer-events: none;
	}
	
	.content {
		flex: 1;
		padding: 0;
		height: calc(100vh - 45px);
	}
	
	.user-info-section {
		padding: 0 0 20px 0;
	}
	
	.info-section {
		background-color: #fff;
		margin-bottom: 10px;
		padding: 15px;
	}
	
	.info-row {
		display: flex;
		padding: 8px 0;
		border-bottom: 1px solid #f0f2f5;
	}
	
	.info-row:last-child {
		border-bottom: none;
	}
	
	.info-label {
		width: 100px;
		color: #666;
		font-size: 14px;
	}
	
	.info-value {
		flex: 1;
		color: #333;
		font-size: 14px;
	}
	
	.section-title {
		font-size: 15px;
		font-weight: 600;
		color: #333;
		margin-bottom: 10px;
		border-left: 3px solid #007aff;
		padding-left: 10px;
	}
	
	.info-content {
		padding: 5px 0;
	}
	
	.family-info {
		font-size: 14px;
		color: #333;
		line-height: 1.5;
	}
	
	.add-info-textarea {
		width: 100%;
		height: 120px;
		border: 1px solid #dbe0e8;
		border-radius: 6px;
		padding: 10px;
		font-size: 14px;
		color: #333;
		background-color: #f9fafc;
	}
	
	.button-row {
		display: flex;
		justify-content: center;
		margin-top: 15px;
	}
	
	.save-btn {
		background-color: #007aff;
		color: #fff;
		font-size: 14px;
		padding: 8px 20px;
		border-radius: 6px;
		border: none;
	}
	
	.loading-text, .error-text, .no-data {
		padding: 40px 0;
		text-align: center;
		color: #999;
		font-size: 14px;
	}
	
	.error-text {
		color: #ff3b30;
	}
</style>
