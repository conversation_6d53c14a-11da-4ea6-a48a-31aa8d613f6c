<!-- 挂件校验 -->
<template>
	<view class="part-container">
		<!-- 操作信息 class="insertColor" -->
		<view class="insertView" >
			<uni-forms ref="baseForm" :modelValue="formData" style="padding: 10px;">
				<uni-forms-item label="计划单号" label-width="140" >
					<uni-easyinput
						id="intput-orderNum"
						class="uni-input bar-code-css1" 
						placeholder="请进行扫码" 
						trim="all"
						v-model="formData.orderNum" 
						@focus="barCodeFocus" 
						@blur="barCodeBlur" 
						@confirm="orderNumChange(formData.orderNum, true)" focus/>
				</uni-forms-item>
				<uni-forms-item label="器具条码" label-width="140">
					<uni-easyinput
						id="intput-deviceCode"
						class="uni-input bar-code-css1" 
						placeholder="请进行扫码" 
						trim="all"
						v-model="formData.deviceCode" 
						@focus="barCodeFocus" 
						@blur="barCodeBlur" 
						@confirm="deviceCodeChange"
						:disabled="deviceCodeBool" />
				</uni-forms-item>
				<uni-forms-item label="位置条码" label-width="140"  v-if="insertType != 1">
					<uni-easyinput
						id="intput-positionCode"
						class="uni-input bar-code-css1" 
						placeholder="请进行扫码" 
						trim="all"
						v-model="formData.positionCode" 
						@focus="barCodeFocus" 
						@blur="barCodeBlur" 
						@confirm="positionCodeChange" :focus="positionCodeFocus" />
				</uni-forms-item>
				<uni-forms-item label="条码" >
					<uni-easyinput 
						id="intput-barCode"
						class="uni-input bar-code-css1" 
						placeholder="请进行扫码" 
						trim="all"
						v-model="formData.barCode" 
						@focus="barCodeFocus" 
						@blur="barCodeBlur" 
						@confirm="barCodeChange" />
				</uni-forms-item>
			</uni-forms>
		</view>
		
		
		<!-- 列表 -->
		<uni-table ref="table" :loading="loading" border stripe emptyText="暂无更多数据" @selection-change="selectionChange">
			<uni-tr>
				<uni-th width="50" align="center">序号{{insertType}}</uni-th>
				<uni-th align="center" v-if="insertType != 1">位置码</uni-th>
				<uni-th align="center">描述</uni-th>
				<uni-th align="center">客户零件号</uni-th>
				<uni-th align="center">底盘号</uni-th>
				<uni-th align="center">ERP号</uni-th>
				<uni-th align="center">条码</uni-th>
			</uni-tr>
			<uni-tr v-for="(item, index) in mainList" :key="index" :data-id="item.ID" 
				:style="{ background: item.toivCount == 0 ? '#ffffff' : '#33f533' }">
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ index+1 }}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }" v-if="insertType != 1">{{ formData.deviceCode ? formData.deviceCode + '-0' + (index*1 + 1) : ''}}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.erpRemark }}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.customerPartNum }}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.Vin }}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.AssemblyPartCode }}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.Barcode }}</uni-td>
				
			</uni-tr>
		</uni-table>
		
		
		
		<!-- 遮罩 -->
		<uni-popup ref="loading" type="center" :animation="false" :mask-click="false"></uni-popup>
		<!-- 消息 -->
		<uni-popup ref="popup"><view class="popup-content"><text class="text" :duration="1000">上报成功</text></view></uni-popup>
		<uni-popup ref="popup1"><view class="popup1-content"><text class="text" :duration="1000">{{errorMsg}}</text></view></uni-popup>
		
		<!-- <view>
			<input v-model="text" placeholder="输入一段文字" />
			<button @click="speak">播放文本</button>
		</view> -->
	</view>
</template>

<script>
  import { findErpNumByBarCode } from '@/api/work/retrun_part.js'
  import { findOrderNumByBarCode, saveVerification, findBarCode, findVerificationList } from '@/api/work/order_instrument_verification.js'
  import { getPlan, planFindAll, saveDeliveryPlan, savePdaSub } from '@/api/work/delivery_board.js'
  
import { reactive } from "vue"
	export default {
	  data() {
	    return {
			loading: false,
			//列表的显示
			listShow: true,
			// text: '',
			errorMsg: '',
			formData: {
				orderNum: '',
				deviceList: [],
			},
			formTem1: { barCode: '', erpNum: '', remark: '', num: '', },
			formTem2: {deviceCode: '', assemblyList: [{ barCode: '', erpNum: '', remark: '', num: '', }] },
			mainList: [],
			//库位是否禁用
			deviceCodeBool: false,
			//条码
			barCodeRes:'',
			// 位置码是否触发焦点
			positionCodeFocus: false,
			// 插单标识-0:正常,1:插单
			insertType: 0,
	    }
	  },
	  created(){
		  this.focusClick("intput-orderNum")
	  },
	  methods: {
		  // 器具位置扫描事件 
		  positionCodeChange(){
			  if(this.formData.deviceCode == this.formData.positionCode.split('-')[0]+'-'+this.formData.positionCode.split('-')[1]){
				  if(!isNaN(this.formData.positionCode.split('-')[2]*1) 
					  && this.mainList.length >= this.formData.positionCode.split('-')[2]*1 
					  && 0 < this.formData.positionCode.split('-')[2]*1){
					  this.focusClick("intput-barCode")  
				  }else{					
					  this.formData.positionCode = ''
					  setTimeout(()=>{
						  this.focusClick("intput-positionCode")
					  },500);
					  this.errorMsg= '位置条码不存在！'
					  this.speak(this.errorMsg)
					  this.msg('popup1')  
				  }
			  }else{
				  this.formData.positionCode = ''
				  setTimeout(()=>{
					  this.focusClick("intput-positionCode")
				  },500);
				  this.errorMsg= '位置条码与器具条码不相符！'
				  this.speak(this.errorMsg)
				  this.msg('popup1')
			  }
		  },
		  // 器具扫描事件 
		  deviceCodeChange(){
			  setTimeout(()=>{
				  //如果是插单不存在位置码，直接扫条码
				  if(this.insertType == 1){
					this.focusClick("intput-barCode")
				  }else{
					this.focusClick("intput-positionCode")
				  }
			  },500);
		  },
		  // 订单号扫描事件 通过条码找单号
		  orderNumChange(barCode, bool){
			  document.getElementsByClassName('insertView')[0].classList.remove('insertColor');
			  this.insertType = 0
			  findOrderNumByBarCode({barCode: barCode}).then(r=>{
				  if(r && r.planCode){
					this.insertType = r.insertType
					// 插单标识-0:正常,1:插单
					if(r.insertType == 1){
						document.getElementsByClassName('insertView')[0].classList.add('insertColor');
					}
					this.formData.orderNum= r.planCode
					this.mainList= r.list
					// 查询器具号
					findVerificationList({'orderNum': r.planCode}).then(d=>{
						if(d.rows.length > 0){
							this.formData.deviceCode = d.rows[0].deviceCode
							this.deviceCodeBool = true
							setTimeout(()=>{
								//如果是插单不存在位置码，直接扫条码
								if(r.insertType == 1){
									this.focusClick("intput-barCode")
								}else{
									this.focusClick("intput-positionCode")
								}
							},500);
						}else{
							setTimeout(()=>{this.focusClick("intput-deviceCode")},500);
						}
					}).catch(e=>{
						setTimeout(()=>{this.focusClick("intput-deviceCode")},500);
					})
				  }else{
					this.errorMsg= '通过条码未获取到订单号'
					this.speak(this.errorMsg)
					this.msg('popup1')
					this.formData.orderNum= ''
					this.mainList= []
					setTimeout(()=>{this.focusClick("intput-orderNum")},500);
				  }
				  if(bool){
					  this.formData.deviceCode = ''
					  this.deviceCodeBool = false
				  }
			  }).catch(e=>{
					this.speak(e)
					this.msg('popup1')
					this.formData.orderNum= ''
					this.mainList= []
					setTimeout(()=>{this.focusClick("intput-orderNum")},500);
					
				    this.formData.deviceCode = ''
					this.deviceCodeBool = false
			  })
		  },
		  // 条码事件
		  barCodeChange(){
			  this.$refs.loading.open()
			  if(!this.formData.orderNum){
				this.errorMsg= '计划单号不能为空'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return
			  }
			  if(!this.formData.deviceCode){
				this.errorMsg= '器具条码不能为空'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return
			  }
			  
			  //如果是插单不存在位置码，直接扫条码
			  if(!this.formData.positionCode && this.insertType != 1){
				this.errorMsg= '位置条码不能为空'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return
			  }
			  
			  findBarCode({barCode: this.formData.barCode}).then(rrr=>{
				  if(rrr && rrr.barCodeRes){
					const barcodeC= rrr.barCodeRes
					// 上报数据
					var res;
					// 是否终止上报
					var saveOrUpdate = true
					//this.insertType == 1 为插单处理
					if(this.insertType == 1){
						var end = true
						//判断订单中是否有当前条码
						const bool = this.mainList.filter((f)=> f.Barcode == barcodeC )
						if(bool && bool.length > 0){
							//判断订单中是否有当前条码，是否是通过的
							const bool1 = this.mainList.filter((f)=> f.Barcode == barcodeC ).filter((f)=> f.toivCount == 0 )
							if(bool1){
								res = bool1
								end = false
							}else{
								// 订单已经上报过了，需要进行修改
								this.errorMsg= '此条码已经通过'
							}
						}else{
							this.errorMsg= '订单中不存在此条码'
						}
						if(end){
						  this.formData.barCode = ''
						  this.formData.positionCode = ''
						  this.deviceCodeBool = true
						  setTimeout(()=>{
							  	//如果是插单不存在位置码，直接扫条码
							  	if(this.insertType == 1){
							  		this.focusClick("intput-barCode")
							  	}else{
							  		this.focusClick("intput-positionCode")
							  	}
						  },500);
						  this.speak(this.errorMsg)
						  this.msg('popup1')
						  return
						}
					}else{
						// 判断扫码的数据中，未扫码的，是否存在当前条码
						const r = this.mainList.filter((f)=> f.toivCount == 0).filter((f)=> f.Barcode == barcodeC )
						// 弟N个位置的条码是否相同
						const positionCodeN = this.formData.positionCode.split('-')[2] * 1 - 1
						console.log('positionCodeN', positionCodeN, this.mainList)
						res = this.mainList.filter((f, i)=> i == positionCodeN && f.toivCount == 0 && f.Barcode == barcodeC)
						console.log('res', res)
						if(!res || res.length == 0){
							  if(r && r.length > 0){
								  // this.errorMsg= '扫码顺序不对'
								  this.errorMsg= '当前条码跟位置码不相符'
							  }else{
								  const bool = this.mainList.filter((f)=> f.Barcode == barcodeC )
								  // console.log(this.mainList, barcodeC)
								  if(bool && bool.length > 0){
									  // 订单已经上报过了，需要进行修改
									  this.errorMsg= '此条码已经通过'
									  // saveOrUpdate= false
								  }else{
									  this.errorMsg= '订单中不存在此条码'
								  }
							  }
							  this.formData.barCode = ''
							  this.formData.positionCode = ''
							  this.deviceCodeBool = true
							  setTimeout(()=>{
								//如果是插单不存在位置码，直接扫条码
								if(this.insertType == 1){
									this.focusClick("intput-barCode")
								}else{
									this.focusClick("intput-positionCode")
								}
							  },500);
							  this.speak(this.errorMsg)
							  this.msg('popup1')
							  return
						}
					}
					
					// 2024-12-13修改 不能进行器具条码修改
					saveVerification({ saveOrUpdate, orderNum: this.formData.orderNum, 
							deviceCode: this.formData.deviceCode, barCode: this.formData.barCode, erpNum: res[0].AssemblyPartCode, 
							chassisNum: res[0].Vin, customerPartNum: res[0].customerPartNum, erpRemark: res[0].erpRemark, 
							 positionCode: this.formData.positionCode}).then(r=>{
						this.orderNumChange(this.formData.barCode, false)
						this.speak("上报成功")
						this.msg('popup')
						this.formData.barCode = ''
						this.formData.positionCode = ''
						this.deviceCodeBool = true
						setTimeout(()=>{
							  //如果是插单不存在位置码，直接扫条码
							  if(this.insertType == 1){
								this.focusClick("intput-barCode")
							  }else{
								this.focusClick("intput-positionCode")
							  }
						  },500);
					}).catch(e=>{
						this.errorMsg= '上报失败'
						this.formData.barCode = ''
						this.formData.positionCode = ''
						this.deviceCodeBool = true
						setTimeout(()=>{
							  //如果是插单不存在位置码，直接扫条码
							  if(this.insertType == 1){
								this.focusClick("intput-barCode")
							  }else{
								this.focusClick("intput-positionCode")
							  }
						  },500);
						this.speak(this.errorMsg)
						this.msg('popup1')
						return
					})
				  }else{
					this.errorMsg= '未获取到长条吗'
					this.speak(this.errorMsg)
					this.msg('popup1')
					return
				  }
			  })
		  },
		  //焦点事件
		  focusClick(id){
			const e = document.getElementById(id)
			console.log(1111, e)
			if(e){
				e.getElementsByTagName('input')[0].focus()
			}
		  },
		  //触发器具条码焦点事件
		  deviceCodeClick(){
			const e = document.getElementById("inputFocus1")
			if(e){
				e.getElementsByTagName('input')[0].focus()
			}
		  },
		  //触发条码焦点事件
		  barCodeClick(id){
			const arr = id.split('-')
			//等于2，当前为器具码
			if(arr.length == 2){
				const e1 = document.getElementById(`${arr[0]}-${arr[1]*1+1}`);
				if(e1){
					e1.getElementsByTagName('input')[0].focus()
				}else{
					const e2 = document.getElementById(`${arr[0]}-${arr[1]*1+1}-1`);
					if(e2){
						e2.getElementsByTagName('input')[0].focus()
					}else{
						//没有下一个，不进行跳转
					}
				}
			}else if(arr.length == 3){// 等于3，当前为条码
					const e1 = document.getElementById(`${arr[0]}-${arr[1]}-${arr[2] * 1 + 1}`);
					if(e1){
						e1.getElementsByTagName('input')[0].focus()
					}else{
						const e2 = document.getElementById(`${arr[0]}-${arr[1]*1 + 1}`);
						if(e2){
							e2.getElementsByTagName('input')[0].focus()
						}else{
							//没有下一个，不进行跳转
						}
					}
			}
		  },
		  //条码触发焦点回调事件
		  barCodeFocus(){
			  //border-color: rgb(41, 121, 255);
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = 'rgb(41, 121, 255)';
			  }
		  },
		  //条码离开焦点回调事件
		  barCodeBlur(){
			  //去除边框样式
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = '';
			  }
		  },
		  barCodeBlur1(){
			  //去除边框样式
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = '';
			  }
			  
			  const e = document.getElementById('intput-positionCode')
			  console.log(1111, e, e.getElementsByTagName('input')[0])
			  if(e){
			  	e.getElementsByTagName('input')[0].focus()
			  }
		  },
		  //重置
		  reset(){
			  this.formData.deviceList = []
			  this.formData= JSON.parse(JSON.stringify(this.formData))
			  this.tableList()
			  this.listShow= true
			  this.deviceCodeClick();
		  },
		  //消息提示
		  msg(type){
			this.$refs[type].open('center')
			setTimeout(() => {
				this.$refs[type].close()
			}, 3000);
			this.$refs.loading.close()
		  },
		  speak(text) {
			  var music = null;
			  music = uni.createInnerAudioContext(); //创建播放器对象
			  music.src = `../../../static/video/msg/${text.includes("成功") ? 'czcg.wav' : 'czsb.mp3'}`;
			  music.volume = 1;
			  music.play(); //执行播放
			  music.onEnded(() => {
				  //播放结束
				  music = null;
			  });
		  },
	  },
	}
</script>

<style scoped lang="scss">
.part-container{
	background-color: #ffffff;
}
.bar-code-css{
	height: 35px;
	text-indent: 10px;
	display: flex;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	color: #000;
	font-size: 14px;
}
.bar-code-css .uni-input-placeholder.input-placeholder{
	color: #999;
}
.bar-code-css-view{
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	/* border-color: rgb(41, 121, 255); */
	background-color: rgb(255, 255, 255);
}
.popup-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #09bb07;
	background-color: #e1f3d8;
}
.popup1-content{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #f56c6c;
	background-color: #fde2e2;
}

.view-border{
	margin: 10px;
	padding: 10px;
	border: 1px solid #ccc;
	border-radius: 6px;
}
.insertColor{
	background-color: yellow;
}
	
// ::v-deep .view-border .section-css uni-view.uni-section-header__slot-right{
// 	width: 70%!important;
// }
</style>