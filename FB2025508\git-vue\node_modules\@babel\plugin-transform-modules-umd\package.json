{"_from": "@babel/plugin-transform-modules-umd@^7.23.3", "_id": "@babel/plugin-transform-modules-umd@7.23.3", "_inBundle": false, "_integrity": "sha512-zHsy9iXX2nIsCBFPud3jKn1IRPWg3Ing1qOZgeKV39m1ZgIdpJqvlWVeiHBZC6ITRG0MfskhYe9cLgntfSFPIg==", "_location": "/@babel/plugin-transform-modules-umd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-modules-umd@^7.23.3", "name": "@babel/plugin-transform-modules-umd", "escapedName": "@babel%2fplugin-transform-modules-umd", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.23.3.tgz", "_shasum": "5d4395fccd071dfefe6585a4411aa7d6b7d769e9", "_spec": "@babel/plugin-transform-modules-umd@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-transforms": "^7.23.3", "@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "This plugin transforms ES2015 modules to UMD", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-external-helpers": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-umd", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-modules-umd", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-umd"}, "type": "commonjs", "version": "7.23.3"}