{"_from": "@babel/plugin-syntax-export-namespace-from@^7.8.3", "_id": "@babel/plugin-syntax-export-namespace-from@7.8.3", "_inBundle": false, "_integrity": "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==", "_location": "/@babel/plugin-syntax-export-namespace-from", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-export-namespace-from@^7.8.3", "name": "@babel/plugin-syntax-export-namespace-from", "escapedName": "@babel%2fplugin-syntax-export-namespace-from", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@babel/plugin-transform-export-namespace-from", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz", "_shasum": "028964a9ba80dbc094c915c487ad7c4e7a66465a", "_spec": "@babel/plugin-syntax-export-namespace-from@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "deprecated": false, "description": "Allow parsing of export namespace from", "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-export-namespace-from", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-export-namespace-from"}, "version": "7.8.3"}