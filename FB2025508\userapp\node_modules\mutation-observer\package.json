{"name": "mutation-observer", "description": "Exposes the `MutationObserver` API, or a polyfill based on mutation events for IE 9-10.", "tags": ["polyfill", "webmodule", "mutation", "observer", "browser"], "version": "1.0.3", "dependencies": {}, "component": {"scripts": {"mutation-observer/index.js": "index.js"}}, "repository": {"type": "git", "url": "git://github.com/webmodules/mutation-observer.git"}}