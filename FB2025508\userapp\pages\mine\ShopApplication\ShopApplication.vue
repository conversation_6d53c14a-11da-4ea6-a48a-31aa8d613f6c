<template>
	<view class="container">
		<!-- 头部标题栏 -->
		<view class="header">
			<view class="back-icon" @click="goBack">
				<text class="iconfont icon-left"></text>
			</view>
			<view class="title">加盟申请</view>
		</view>
		
		<!-- 申请表单 -->
		<view class="form-container">
			<form @submit.prevent="submitForm">
				<!-- 门店名称 -->
				<view class="form-item">
					<view class="label">
						<text>门店名称</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						v-model="formData.shopName" 
						placeholder="请输入门店名称" 
						:disabled="formSubmitted && !isEditing"
						class="input-field"
					/>
				</view>
				
				<!-- 所属城市 -->
				<view class="form-item">
					<view class="label">
						<text>所属城市</text>
						<text class="required">*</text>
					</view>
					<picker 
						@change="onCityChange" 
						:value="cityIndex" 
						:range="cities" 
						range-key="region_name"
						:disabled="formSubmitted && !isEditing"
					>
						<view class="picker-view">
							<text v-if="formData.city" class="picker-text">{{ formData.city }}</text>
							<text v-else class="picker-placeholder">请选择所属城市</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
				
				<!-- 联系人姓名 -->
				<view class="form-item">
					<view class="label">
						<text>联系人姓名</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						v-model="formData.contactName" 
						placeholder="请输入联系人姓名" 
						:disabled="formSubmitted && !isEditing"
						class="input-field"
					/>
				</view>
				
				<!-- 联系电话 -->
				<view class="form-item">
					<view class="label">
						<text>联系电话</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						v-model="formData.contactPhone" 
						placeholder="请输入联系电话" 
						:disabled="formSubmitted && !isEditing"
						class="input-field"
					/>
				</view>
				
				<!-- 门店简介 -->
				<view class="form-item description-item">
					<view class="label">
						<text>门店简介</text>
						<text class="required">*</text>
						<text class="limit">(限100字)</text>
					</view>
					<textarea 
						v-model="formData.shopDesc" 
						placeholder="请介绍门店成立时间、现有员工、现有业务等" 
						:disabled="formSubmitted && !isEditing"
						maxlength="100"
						class="textarea-field"
					></textarea>
					<view class="char-count">{{formData.shopDesc.length}}/100</view>
				</view>
				
				<!-- 日期时间 -->
				<view class="form-item">
					<view class="label">
						<text>日期时间</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						:value="formData.applyTime" 
						disabled 
						class="datetime input-field"
					/>
				</view>
				
				<!-- 提交按钮区域 -->
				<view class="button-group">
					<button 
						type="default" 
						class="reset-btn" 
						@click="resetForm"
					>重置</button>
					<button 
						type="default" 
						class="modify-btn" 
						@click="toggleEdit" 
						v-if="formSubmitted"
					>修改</button>
					<button 
						type="primary" 
						class="submit-btn" 
						@click="submitForm" 
						:disabled="formSubmitted && !isEditing"
					>提交</button>
				</view>
			</form>
		</view>
	</view>
</template>

<script>
import { submitFranchiseApplication, updateFranchiseApplication, getAllRegions } from '@/api/work/retrun_part.js'

export default {
	data() {
		return {
			formData: {
				shopName: '',
				city: '',
				cityId: null, // 新增城市ID字段
				contactName: '',
				contactPhone: '',
				shopDesc: '',
				applyTime: this.formatDateTime(new Date()),
				applicationId: null
			},
			formSubmitted: false,  // 是否已提交
			isEditing: false,      // 是否处于编辑状态
			originalFormData: null, // 保存原始表单数据用于比较
			cities: [], // 城市列表
			cityIndex: 0, // 城市选择器索引
		}
	},
	onLoad() {
		// 页面加载时生成当前日期时间
		this.formData.applyTime = this.formatDateTime(new Date());
		// 获取城市列表
		this.loadCities();
	},
	methods: {
		// 加载城市列表
		loadCities() {
			uni.showLoading({
				title: '加载中...'
			});
			
			getAllRegions().then(res => {
				uni.hideLoading();
				if (res.code === 200 && res.data) {
					this.cities = res.data;
					
					// 如果有已保存的城市，则找到对应索引
					if (this.formData.city && this.cities.length > 0) {
						const index = this.cities.findIndex(city => city.region_name === this.formData.city);
						if (index !== -1) {
							this.cityIndex = index;
						}
					}
				} else {
					uni.showToast({
						title: '获取城市列表失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				uni.hideLoading();
				console.error('获取城市列表失败:', err);
				uni.showToast({
					title: '网络错误，请稍后再试',
					icon: 'none'
				});
			});
		},
		
		// 城市选择变化
		onCityChange(e) {
			const index = e.detail.value;
			this.cityIndex = index;
			
			if (this.cities[index]) {
				this.formData.city = this.cities[index].region_name;
				this.formData.cityId = this.cities[index].region_id;
			}
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 提交表单
		submitForm() {
			// 表单验证
			if (!this.validateForm()) {
				return;
			}
			
			// 格式化日期时间
			this.formData.applyTime = this.formatDateTime(new Date());
			
			// 根据是否有ID决定是新增还是更新
			const submitMethod = this.formData.applicationId 
				? updateFranchiseApplication
				: submitFranchiseApplication;
				
			// 发送请求
			submitMethod(this.formData).then(res => {
				if (res.code === 200) {
					// 如果是首次提交
					if (!this.formData.applicationId && res.data) {
						this.formData.applicationId = res.data;
					}
					
					uni.showToast({
						title: this.formData.applicationId ? '修改成功' : '提交成功',
						icon: 'success'
					});
					
					// 提交后更新状态，禁用表单
					this.formSubmitted = true;
					this.isEditing = false;
					
					// 保存原始数据
					this.originalFormData = JSON.parse(JSON.stringify(this.formData));
				} else {
					uni.showToast({
						title: res.msg || '提交失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				console.error('提交失败:', err);
				uni.showToast({
					title: '网络错误，请稍后再试',
					icon: 'none'
				});
			});
		},
		
		// 表单验证
		validateForm() {
			if (!this.formData.shopName.trim()) {
				uni.showToast({
					title: '请输入门店名称',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.city.trim()) {
				uni.showToast({
					title: '请选择所属城市',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.contactName.trim()) {
				uni.showToast({
					title: '请输入联系人姓名',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.contactPhone.trim()) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return false;
			}
			
			// 验证手机号格式
			const phoneReg = /^1[3-9]\d{9}$/;
			if (!phoneReg.test(this.formData.contactPhone)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.shopDesc.trim()) {
				uni.showToast({
					title: '请输入门店简介',
					icon: 'none'
				});
				return false;
			}
			
			return true;
		},
		
		// 重置表单
		resetForm() {
			uni.showModal({
				title: '提示',
				content: '确定要重置表单吗？',
				success: res => {
					if (res.confirm) {
						this.formData = {
							shopName: '',
							city: '',
							cityId: null,
							contactName: '',
							contactPhone: '',
							shopDesc: '',
							applyTime: this.formatDateTime(new Date()),
							applicationId: null
						};
						this.cityIndex = 0;
						this.formSubmitted = false;
						this.isEditing = false;
					}
				}
			});
		},
		
		// 切换编辑状态
		toggleEdit() {
			this.isEditing = !this.isEditing;
			if (!this.isEditing && this.originalFormData) {
				// 如果退出编辑状态，但未提交，恢复原始数据
				this.formData = JSON.parse(JSON.stringify(this.originalFormData));
				
				// 恢复城市索引
				if (this.formData.city && this.cities.length > 0) {
					const index = this.cities.findIndex(city => city.region_name === this.formData.city);
					if (index !== -1) {
						this.cityIndex = index;
					}
				}
			}
		},
		
		// 格式化日期时间
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		}
	}
}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 90rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #eeeeee;
}

.back-icon {
	position: absolute;
	left: 30rpx;
	font-size: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: 500;
}

.form-container {
	padding: 20rpx;
	margin-bottom: 40rpx;
}

.form-item {
	position: relative;
	margin-bottom: 30rpx; /* 增加表单项之间的间距 */
	background-color: #ffffff;
	border-radius: 8rpx;
	padding: 20rpx; /* 增加内边距 */
}

.description-item {
	min-height: 260rpx; /* 为门店简介增加更多高度 */
}

.label {
	font-size: 30rpx; /* 增加标签文字大小 */
	color: #333;
	margin-bottom: 15rpx; /* 增加标签与输入框的间距 */
	font-weight: 500;
}

.required {
	color: #ff0000;
	margin-left: 5rpx;
}

.limit {
	font-size: 24rpx;
	color: #999;
	margin-left: 10rpx;
}

/* 输入框样式优化 */
.input-field {
	width: 100%;
	font-size: 30rpx;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #eee;
	height: 70rpx; /* 增加输入框高度 */
	line-height: 1.5;
}

.textarea-field {
	width: 100%;
	font-size: 30rpx;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #eee;
	height: 180rpx; /* 增加文本区域高度 */
	line-height: 1.5;
}

/* picker组件样式 */
.picker-view {
	width: 100%;
	height: 70rpx; /* 与输入框高度一致 */
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #eee;
	padding: 15rpx 0;
}

.picker-text {
	font-size: 30rpx;
	color: #333;
}

.picker-placeholder {
	font-size: 30rpx;
	color: #999;
}

.picker-arrow {
	font-size: 24rpx;
	color: #999;
}

.datetime {
	color: #ff0000;
}

.char-count {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	font-size: 24rpx;
	color: #999;
}

.button-group {
	display: flex;
	justify-content: space-between;
	padding: 30rpx 0 60rpx; /* 增加底部按钮区域的下边距 */
}

button {
	flex: 1;
	margin: 0 10rpx;
	height: 90rpx; /* 增加按钮高度 */
	line-height: 90rpx;
	font-size: 32rpx; /* 增加按钮文字大小 */
	border-radius: 8rpx;
}

.reset-btn {
	background-color: #f2f2f2;
	color: #666;
}

.modify-btn {
	background-color: #e6f7ff;
	color: #1890ff;
}

.submit-btn {
	background-color: #1890ff;
	color: #ffffff;
}

.submit-btn[disabled] {
	background-color: #cccccc;
	color: #ffffff;
	opacity: 0.7;
}
</style>
