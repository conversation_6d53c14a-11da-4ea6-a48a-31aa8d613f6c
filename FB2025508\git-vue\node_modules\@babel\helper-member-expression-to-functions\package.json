{"_from": "@babel/helper-member-expression-to-functions@^7.23.0", "_id": "@babel/helper-member-expression-to-functions@7.23.0", "_inBundle": false, "_integrity": "sha512-6gfrPwh7OuT6gZyJZvd6WbTfrqAo7vm4xCzAXOusKqq/vWdKXphTpj5klHKNmRUU6/QRGlBsyU9mAIPaWHlqJA==", "_location": "/@babel/helper-member-expression-to-functions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-member-expression-to-functions@^7.23.0", "name": "@babel/helper-member-expression-to-functions", "escapedName": "@babel%2fhelper-member-expression-to-functions", "scope": "@babel", "rawSpec": "^7.23.0", "saveSpec": null, "fetchSpec": "^7.23.0"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-replace-supers"], "_resolved": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.23.0.tgz", "_shasum": "9263e88cc5e41d39ec18c9a3e0eced59a3e7d366", "_spec": "@babel/helper-member-expression-to-functions@^7.23.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.23.0"}, "deprecated": false, "description": "Helper function to replace certain member expressions with function calls", "devDependencies": {"@babel/traverse": "^7.23.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-member-expression-to-functions", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-member-expression-to-functions", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-member-expression-to-functions"}, "type": "commonjs", "version": "7.23.0"}