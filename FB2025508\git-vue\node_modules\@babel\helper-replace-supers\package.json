{"_from": "@babel/helper-replace-supers@^7.22.20", "_id": "@babel/helper-replace-supers@7.22.20", "_inBundle": false, "_integrity": "sha512-qsW0In3dbwQUbK8kejJ4R7IHVGwHJlV6lpG6UA7a9hSa2YEiAib+N1T2kr6PEeUT+Fl7najmSOS6SmAwCHK6Tw==", "_location": "/@babel/helper-replace-supers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-replace-supers@^7.22.20", "name": "@babel/helper-replace-supers", "escapedName": "@babel%2fhelper-replace-supers", "scope": "@babel", "rawSpec": "^7.22.20", "saveSpec": null, "fetchSpec": "^7.22.20"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-object-super"], "_resolved": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.22.20.tgz", "_shasum": "e37d367123ca98fe455a9887734ed2e16eb7a793", "_spec": "@babel/helper-replace-supers@^7.22.20", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-member-expression-to-functions": "^7.22.15", "@babel/helper-optimise-call-expression": "^7.22.5"}, "deprecated": false, "description": "Helper function to replace supers", "devDependencies": {"@babel/core": "^7.22.20"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-replace-supers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-replace-supers", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-replace-supers"}, "type": "commonjs", "version": "7.22.20"}