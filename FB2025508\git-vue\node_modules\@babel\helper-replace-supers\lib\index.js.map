{"version": 3, "names": ["_helperEnvironmentVisitor", "require", "_helperMemberExpressionToFunctions", "_helperOptimiseCallExpression", "_core", "assignmentExpression", "booleanLiteral", "callExpression", "cloneNode", "identifier", "memberExpression", "sequenceExpression", "stringLiteral", "thisExpression", "t", "ns", "exports", "environmentVisitor", "default", "skipAllButComputedKey", "getPrototypeOfExpression", "objectRef", "isStatic", "file", "isPrivateMethod", "targetRef", "addHelper", "visitor", "traverse", "visitors", "merge", "Super", "path", "state", "node", "parentPath", "isMemberExpression", "object", "handle", "unshadowSuperBindingVisitor", "Scopable", "refName", "binding", "scope", "getOwnBinding", "name", "rename", "specHandlers", "memoise", "superMember", "count", "computed", "property", "memo", "maybeGenerateMemoised", "memoiser", "set", "prop", "has", "get", "_get", "_getThisRefs", "thisRefs", "proto", "getObjectRef", "this", "isDerivedConstructor", "thisRef", "generateDeclaredUidIdentifier", "value", "isInStrictMode", "destructureSet", "buildCodeFrameError", "call", "args", "optimiseCall", "optionalCall", "delete", "template", "expression", "ast", "looseHandlers", "Object", "assign", "getSuperRef", "_getSuper<PERSON>ef", "_getSuperRef2", "ReplaceSupers", "constructor", "opts", "_opts$constantSuper", "methodPath", "isClassMethod", "kind", "superRef", "isObjectMethod", "static", "isStaticBlock", "isPrivate", "isMethod", "constant<PERSON>uper", "isLoose", "replace", "refToPreserve", "handler", "memberExpressionToFunctions", "bind", "boundGet"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { File } from \"@babel/core\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport memberExpressionToFunctions from \"@babel/helper-member-expression-to-functions\";\nimport type { HandlerState } from \"@babel/helper-member-expression-to-functions\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport { traverse, template, types as t } from \"@babel/core\";\nimport type { NodePath, Scope } from \"@babel/traverse\";\nconst {\n  assignmentExpression,\n  booleanLiteral,\n  callExpression,\n  cloneNode,\n  identifier,\n  memberExpression,\n  sequenceExpression,\n  stringLiteral,\n  thisExpression,\n} = t;\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  // eslint-disable-next-line no-restricted-globals\n  const ns = require(\"@babel/helper-environment-visitor\");\n  // eslint-disable-next-line no-restricted-globals\n  exports.environmentVisitor = ns.default;\n  // eslint-disable-next-line no-restricted-globals\n  exports.skipAllButComputedKey = ns.skipAllButComputedKey;\n}\n\ntype ThisRef =\n  | {\n      memo: t.AssignmentExpression;\n      this: t.Identifier;\n    }\n  | { this: t.ThisExpression };\n/**\n * Creates an expression which result is the proto of objectRef.\n *\n * @example <caption>isStatic === true</caption>\n *\n *   helpers.getPrototypeOf(CLASS)\n *\n * @example <caption>isStatic === false</caption>\n *\n *   helpers.getPrototypeOf(CLASS.prototype)\n */\nfunction getPrototypeOfExpression(\n  objectRef: t.Identifier,\n  isStatic: boolean,\n  file: File,\n  isPrivateMethod: boolean,\n) {\n  objectRef = cloneNode(objectRef);\n  const targetRef =\n    isStatic || isPrivateMethod\n      ? objectRef\n      : memberExpression(objectRef, identifier(\"prototype\"));\n\n  return callExpression(file.addHelper(\"getPrototypeOf\"), [targetRef]);\n}\n\nconst visitor = traverse.visitors.merge<\n  HandlerState<ReplaceState> & ReplaceState\n>([\n  environmentVisitor,\n  {\n    Super(path, state) {\n      const { node, parentPath } = path;\n      if (!parentPath.isMemberExpression({ object: node })) return;\n      state.handle(parentPath);\n    },\n  },\n]);\n\nconst unshadowSuperBindingVisitor = traverse.visitors.merge<{\n  refName: string;\n}>([\n  environmentVisitor,\n  {\n    Scopable(path, { refName }) {\n      // https://github.com/Zzzen/babel/pull/1#pullrequestreview-564833183\n      const binding = path.scope.getOwnBinding(refName);\n      if (binding && binding.identifier.name === refName) {\n        path.scope.rename(refName);\n      }\n    },\n  },\n]);\n\ntype SharedState = {\n  file: File;\n  scope: Scope;\n  isDerivedConstructor: boolean;\n  isStatic: boolean;\n  isPrivateMethod: boolean;\n  getObjectRef: () => t.Identifier;\n  getSuperRef: () => t.Identifier;\n  // we dont need boundGet here, but memberExpressionToFunctions handler needs it.\n  boundGet: HandlerState[\"get\"];\n};\n\ntype Handler = HandlerState<SharedState> & SharedState;\ntype SuperMember = NodePath<\n  t.MemberExpression & {\n    object: t.Super;\n    property: Exclude<t.MemberExpression[\"property\"], t.PrivateName>;\n  }\n>;\n\ninterface SpecHandler\n  extends Pick<\n    Handler,\n    | \"memoise\"\n    | \"get\"\n    | \"set\"\n    | \"destructureSet\"\n    | \"call\"\n    | \"optionalCall\"\n    | \"delete\"\n  > {\n  _get(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    thisRefs: ThisRef,\n  ): t.CallExpression;\n  _getThisRefs(): ThisRef;\n  prop(this: Handler & SpecHandler, superMember: SuperMember): t.Expression;\n}\n\nconst specHandlers: SpecHandler = {\n  memoise(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    count: number,\n  ) {\n    const { scope, node } = superMember;\n    const { computed, property } = node;\n    if (!computed) {\n      return;\n    }\n\n    const memo = scope.maybeGenerateMemoised(property);\n    if (!memo) {\n      return;\n    }\n\n    this.memoiser.set(property, memo, count);\n  },\n\n  prop(this: Handler & SpecHandler, superMember: SuperMember) {\n    const { computed, property } = superMember.node;\n    if (this.memoiser.has(property)) {\n      return cloneNode(this.memoiser.get(property));\n    }\n\n    if (computed) {\n      return cloneNode(property);\n    }\n\n    return stringLiteral((property as t.Identifier).name);\n  },\n\n  get(this: Handler & SpecHandler, superMember: SuperMember) {\n    return this._get(superMember, this._getThisRefs());\n  },\n\n  _get(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    thisRefs: ThisRef,\n  ) {\n    const proto = getPrototypeOfExpression(\n      this.getObjectRef(),\n      this.isStatic,\n      this.file,\n      this.isPrivateMethod,\n    );\n    return callExpression(this.file.addHelper(\"get\"), [\n      // @ts-expect-error memo does not exist when this.isDerivedConstructor is false\n      thisRefs.memo ? sequenceExpression([thisRefs.memo, proto]) : proto,\n      this.prop(superMember),\n      thisRefs.this,\n    ]);\n  },\n\n  _getThisRefs(this: Handler & SpecHandler): ThisRef {\n    if (!this.isDerivedConstructor) {\n      return { this: thisExpression() };\n    }\n    const thisRef = this.scope.generateDeclaredUidIdentifier(\"thisSuper\");\n    return {\n      memo: assignmentExpression(\"=\", thisRef, thisExpression()),\n      this: cloneNode(thisRef),\n    };\n  },\n\n  set(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    value: t.Expression,\n  ) {\n    const thisRefs = this._getThisRefs();\n    const proto = getPrototypeOfExpression(\n      this.getObjectRef(),\n      this.isStatic,\n      this.file,\n      this.isPrivateMethod,\n    );\n    return callExpression(this.file.addHelper(\"set\"), [\n      // @ts-expect-error memo does not exist when this.isDerivedConstructor is false\n      thisRefs.memo ? sequenceExpression([thisRefs.memo, proto]) : proto,\n      this.prop(superMember),\n      value,\n      thisRefs.this,\n      booleanLiteral(superMember.isInStrictMode()),\n    ]);\n  },\n\n  destructureSet(this: Handler & SpecHandler, superMember: SuperMember) {\n    throw superMember.buildCodeFrameError(\n      `Destructuring to a super field is not supported yet.`,\n    );\n  },\n\n  call(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    const thisRefs = this._getThisRefs();\n    return optimiseCall(\n      this._get(superMember, thisRefs),\n      cloneNode(thisRefs.this),\n      args,\n      false,\n    );\n  },\n\n  optionalCall(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    const thisRefs = this._getThisRefs();\n    return optimiseCall(\n      this._get(superMember, thisRefs),\n      cloneNode(thisRefs.this),\n      args,\n      true,\n    );\n  },\n\n  delete(this: Handler & SpecHandler, superMember: SuperMember) {\n    if (superMember.node.computed) {\n      return sequenceExpression([\n        callExpression(this.file.addHelper(\"toPropertyKey\"), [\n          cloneNode(superMember.node.property),\n        ]),\n        template.expression.ast`\n          function () { throw new ReferenceError(\"'delete super[expr]' is invalid\"); }()\n        `,\n      ]);\n    } else {\n      return template.expression.ast`\n        function () { throw new ReferenceError(\"'delete super.prop' is invalid\"); }()\n      `;\n    }\n  },\n};\n\nconst looseHandlers = {\n  ...specHandlers,\n\n  prop(this: Handler & typeof specHandlers, superMember: SuperMember) {\n    const { property } = superMember.node;\n    if (this.memoiser.has(property)) {\n      return cloneNode(this.memoiser.get(property));\n    }\n\n    return cloneNode(property);\n  },\n\n  get(this: Handler & typeof specHandlers, superMember: SuperMember) {\n    const { isStatic, getSuperRef } = this;\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    let object;\n    if (isStatic) {\n      object =\n        getSuperRef() ??\n        memberExpression(identifier(\"Function\"), identifier(\"prototype\"));\n    } else {\n      object = memberExpression(\n        getSuperRef() ?? identifier(\"Object\"),\n        identifier(\"prototype\"),\n      );\n    }\n\n    return memberExpression(object, prop, computed);\n  },\n\n  set(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    value: t.Expression,\n  ) {\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    return assignmentExpression(\n      \"=\",\n      memberExpression(thisExpression(), prop, computed),\n      value,\n    );\n  },\n\n  destructureSet(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n  ) {\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    return memberExpression(thisExpression(), prop, computed);\n  },\n\n  call(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(this.get(superMember), thisExpression(), args, false);\n  },\n\n  optionalCall(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(this.get(superMember), thisExpression(), args, true);\n  },\n};\n\ntype ReplaceSupersOptionsBase = {\n  methodPath: NodePath<\n    | t.ClassMethod\n    | t.ClassProperty\n    | t.ObjectMethod\n    | t.ClassPrivateMethod\n    | t.ClassPrivateProperty\n    | t.StaticBlock\n  >;\n  constantSuper?: boolean;\n  file: File;\n  // objectRef might have been shadowed in child scopes,\n  // in that case, we need to rename related variables.\n  refToPreserve?: t.Identifier;\n};\n\ntype ReplaceSupersOptions = ReplaceSupersOptionsBase &\n  (\n    | { objectRef?: undefined; getObjectRef: () => t.Node }\n    | { objectRef: t.Node; getObjectRef?: undefined }\n  ) &\n  (\n    | { superRef?: undefined; getSuperRef: () => t.Node }\n    | { superRef: t.Node; getSuperRef?: undefined }\n  );\n\ninterface ReplaceState {\n  file: File;\n  scope: Scope;\n  isDerivedConstructor: boolean;\n  isStatic: boolean;\n  isPrivateMethod: boolean;\n  getObjectRef: ReplaceSupers[\"getObjectRef\"];\n  getSuperRef: ReplaceSupers[\"getSuperRef\"];\n}\n\nexport default class ReplaceSupers {\n  constructor(opts: ReplaceSupersOptions) {\n    const path = opts.methodPath;\n\n    this.methodPath = path;\n    this.isDerivedConstructor =\n      path.isClassMethod({ kind: \"constructor\" }) && !!opts.superRef;\n    this.isStatic =\n      path.isObjectMethod() ||\n      // @ts-expect-error static is not in ClassPrivateMethod\n      path.node.static ||\n      path.isStaticBlock?.();\n    this.isPrivateMethod = path.isPrivate() && path.isMethod();\n\n    this.file = opts.file;\n    this.constantSuper = process.env.BABEL_8_BREAKING\n      ? opts.constantSuper\n      : // Fallback to isLoose for backward compatibility\n        opts.constantSuper ?? (opts as any).isLoose;\n    this.opts = opts;\n  }\n\n  declare file: File;\n  declare isDerivedConstructor: boolean;\n  declare constantSuper: boolean;\n  declare isPrivateMethod: boolean;\n  declare isStatic: boolean;\n  declare methodPath: NodePath;\n  declare opts: ReplaceSupersOptions;\n\n  getObjectRef() {\n    return cloneNode(this.opts.objectRef || this.opts.getObjectRef());\n  }\n\n  getSuperRef() {\n    if (this.opts.superRef) return cloneNode(this.opts.superRef);\n    if (this.opts.getSuperRef) {\n      return cloneNode(this.opts.getSuperRef());\n    }\n  }\n\n  replace() {\n    // https://github.com/babel/babel/issues/11994\n    if (this.opts.refToPreserve) {\n      this.methodPath.traverse(unshadowSuperBindingVisitor, {\n        refName: this.opts.refToPreserve.name,\n      });\n    }\n\n    const handler = this.constantSuper ? looseHandlers : specHandlers;\n\n    memberExpressionToFunctions<ReplaceState>(this.methodPath, visitor, {\n      file: this.file,\n      scope: this.methodPath.scope,\n      isDerivedConstructor: this.isDerivedConstructor,\n      isStatic: this.isStatic,\n      isPrivateMethod: this.isPrivateMethod,\n      getObjectRef: this.getObjectRef.bind(this),\n      getSuperRef: this.getSuperRef.bind(this),\n      // we dont need boundGet here, but memberExpressionToFunctions handler needs it.\n      boundGet: handler.get,\n      ...handler,\n    });\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,kCAAA,GAAAD,OAAA;AAEA,IAAAE,6BAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAEA,MAAM;EACJI,oBAAoB;EACpBC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,UAAU;EACVC,gBAAgB;EAChBC,kBAAkB;EAClBC,aAAa;EACbC;AACF,CAAC,GAAGC,WAAC;AAE4D;EAE/D,MAAMC,EAAE,GAAGd,OAAO,CAAC,mCAAmC,CAAC;EAEvDe,OAAO,CAACC,kBAAkB,GAAGF,EAAE,CAACG,OAAO;EAEvCF,OAAO,CAACG,qBAAqB,GAAGJ,EAAE,CAACI,qBAAqB;AAC1D;AAmBA,SAASC,wBAAwBA,CAC/BC,SAAuB,EACvBC,QAAiB,EACjBC,IAAU,EACVC,eAAwB,EACxB;EACAH,SAAS,GAAGb,SAAS,CAACa,SAAS,CAAC;EAChC,MAAMI,SAAS,GACbH,QAAQ,IAAIE,eAAe,GACvBH,SAAS,GACTX,gBAAgB,CAACW,SAAS,EAAEZ,UAAU,CAAC,WAAW,CAAC,CAAC;EAE1D,OAAOF,cAAc,CAACgB,IAAI,CAACG,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAACD,SAAS,CAAC,CAAC;AACtE;AAEA,MAAME,OAAO,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAErC,CACAb,iCAAkB,EAClB;EACEc,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACjB,MAAM;MAAEC,IAAI;MAAEC;IAAW,CAAC,GAAGH,IAAI;IACjC,IAAI,CAACG,UAAU,CAACC,kBAAkB,CAAC;MAAEC,MAAM,EAAEH;IAAK,CAAC,CAAC,EAAE;IACtDD,KAAK,CAACK,MAAM,CAACH,UAAU,CAAC;EAC1B;AACF,CAAC,CACF,CAAC;AAEF,MAAMI,2BAA2B,GAAGX,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAExD,CACDb,iCAAkB,EAClB;EACEuB,QAAQA,CAACR,IAAI,EAAE;IAAES;EAAQ,CAAC,EAAE;IAE1B,MAAMC,OAAO,GAAGV,IAAI,CAACW,KAAK,CAACC,aAAa,CAACH,OAAO,CAAC;IACjD,IAAIC,OAAO,IAAIA,OAAO,CAACjC,UAAU,CAACoC,IAAI,KAAKJ,OAAO,EAAE;MAClDT,IAAI,CAACW,KAAK,CAACG,MAAM,CAACL,OAAO,CAAC;IAC5B;EACF;AACF,CAAC,CACF,CAAC;AA0CF,MAAMM,YAAyB,GAAG;EAChCC,OAAOA,CAELC,WAAwB,EACxBC,KAAa,EACb;IACA,MAAM;MAAEP,KAAK;MAAET;IAAK,CAAC,GAAGe,WAAW;IACnC,MAAM;MAAEE,QAAQ;MAAEC;IAAS,CAAC,GAAGlB,IAAI;IACnC,IAAI,CAACiB,QAAQ,EAAE;MACb;IACF;IAEA,MAAME,IAAI,GAAGV,KAAK,CAACW,qBAAqB,CAACF,QAAQ,CAAC;IAClD,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IAEA,IAAI,CAACE,QAAQ,CAACC,GAAG,CAACJ,QAAQ,EAAEC,IAAI,EAAEH,KAAK,CAAC;EAC1C,CAAC;EAEDO,IAAIA,CAA8BR,WAAwB,EAAE;IAC1D,MAAM;MAAEE,QAAQ;MAAEC;IAAS,CAAC,GAAGH,WAAW,CAACf,IAAI;IAC/C,IAAI,IAAI,CAACqB,QAAQ,CAACG,GAAG,CAACN,QAAQ,CAAC,EAAE;MAC/B,OAAO5C,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAACI,GAAG,CAACP,QAAQ,CAAC,CAAC;IAC/C;IAEA,IAAID,QAAQ,EAAE;MACZ,OAAO3C,SAAS,CAAC4C,QAAQ,CAAC;IAC5B;IAEA,OAAOxC,aAAa,CAAEwC,QAAQ,CAAkBP,IAAI,CAAC;EACvD,CAAC;EAEDc,GAAGA,CAA8BV,WAAwB,EAAE;IACzD,OAAO,IAAI,CAACW,IAAI,CAACX,WAAW,EAAE,IAAI,CAACY,YAAY,CAAC,CAAC,CAAC;EACpD,CAAC;EAEDD,IAAIA,CAEFX,WAAwB,EACxBa,QAAiB,EACjB;IACA,MAAMC,KAAK,GAAG3C,wBAAwB,CACpC,IAAI,CAAC4C,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC1C,QAAQ,EACb,IAAI,CAACC,IAAI,EACT,IAAI,CAACC,eACP,CAAC;IACD,OAAOjB,cAAc,CAAC,IAAI,CAACgB,IAAI,CAACG,SAAS,CAAC,KAAK,CAAC,EAAE,CAEhDoC,QAAQ,CAACT,IAAI,GAAG1C,kBAAkB,CAAC,CAACmD,QAAQ,CAACT,IAAI,EAAEU,KAAK,CAAC,CAAC,GAAGA,KAAK,EAClE,IAAI,CAACN,IAAI,CAACR,WAAW,CAAC,EACtBa,QAAQ,CAACG,IAAI,CACd,CAAC;EACJ,CAAC;EAEDJ,YAAYA,CAAA,EAAuC;IACjD,IAAI,CAAC,IAAI,CAACK,oBAAoB,EAAE;MAC9B,OAAO;QAAED,IAAI,EAAEpD,cAAc,CAAC;MAAE,CAAC;IACnC;IACA,MAAMsD,OAAO,GAAG,IAAI,CAACxB,KAAK,CAACyB,6BAA6B,CAAC,WAAW,CAAC;IACrE,OAAO;MACLf,IAAI,EAAEhD,oBAAoB,CAAC,GAAG,EAAE8D,OAAO,EAAEtD,cAAc,CAAC,CAAC,CAAC;MAC1DoD,IAAI,EAAEzD,SAAS,CAAC2D,OAAO;IACzB,CAAC;EACH,CAAC;EAEDX,GAAGA,CAEDP,WAAwB,EACxBoB,KAAmB,EACnB;IACA,MAAMP,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACpC,MAAME,KAAK,GAAG3C,wBAAwB,CACpC,IAAI,CAAC4C,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC1C,QAAQ,EACb,IAAI,CAACC,IAAI,EACT,IAAI,CAACC,eACP,CAAC;IACD,OAAOjB,cAAc,CAAC,IAAI,CAACgB,IAAI,CAACG,SAAS,CAAC,KAAK,CAAC,EAAE,CAEhDoC,QAAQ,CAACT,IAAI,GAAG1C,kBAAkB,CAAC,CAACmD,QAAQ,CAACT,IAAI,EAAEU,KAAK,CAAC,CAAC,GAAGA,KAAK,EAClE,IAAI,CAACN,IAAI,CAACR,WAAW,CAAC,EACtBoB,KAAK,EACLP,QAAQ,CAACG,IAAI,EACb3D,cAAc,CAAC2C,WAAW,CAACqB,cAAc,CAAC,CAAC,CAAC,CAC7C,CAAC;EACJ,CAAC;EAEDC,cAAcA,CAA8BtB,WAAwB,EAAE;IACpE,MAAMA,WAAW,CAACuB,mBAAmB,CAClC,sDACH,CAAC;EACH,CAAC;EAEDC,IAAIA,CAEFxB,WAAwB,EACxByB,IAAmC,EACnC;IACA,MAAMZ,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACpC,OAAO,IAAAc,qCAAY,EACjB,IAAI,CAACf,IAAI,CAACX,WAAW,EAAEa,QAAQ,CAAC,EAChCtD,SAAS,CAACsD,QAAQ,CAACG,IAAI,CAAC,EACxBS,IAAI,EACJ,KACF,CAAC;EACH,CAAC;EAEDE,YAAYA,CAEV3B,WAAwB,EACxByB,IAAmC,EACnC;IACA,MAAMZ,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACpC,OAAO,IAAAc,qCAAY,EACjB,IAAI,CAACf,IAAI,CAACX,WAAW,EAAEa,QAAQ,CAAC,EAChCtD,SAAS,CAACsD,QAAQ,CAACG,IAAI,CAAC,EACxBS,IAAI,EACJ,IACF,CAAC;EACH,CAAC;EAEDG,MAAMA,CAA8B5B,WAAwB,EAAE;IAC5D,IAAIA,WAAW,CAACf,IAAI,CAACiB,QAAQ,EAAE;MAC7B,OAAOxC,kBAAkB,CAAC,CACxBJ,cAAc,CAAC,IAAI,CAACgB,IAAI,CAACG,SAAS,CAAC,eAAe,CAAC,EAAE,CACnDlB,SAAS,CAACyC,WAAW,CAACf,IAAI,CAACkB,QAAQ,CAAC,CACrC,CAAC,EACF0B,cAAQ,CAACC,UAAU,CAACC,GAAI;AAChC;AACA,SAAS,CACF,CAAC;IACJ,CAAC,MAAM;MACL,OAAOF,cAAQ,CAACC,UAAU,CAACC,GAAI;AACrC;AACA,OAAO;IACH;EACF;AACF,CAAC;AAED,MAAMC,aAAa,GAAAC,MAAA,CAAAC,MAAA,KACdpC,YAAY;EAEfU,IAAIA,CAAsCR,WAAwB,EAAE;IAClE,MAAM;MAAEG;IAAS,CAAC,GAAGH,WAAW,CAACf,IAAI;IACrC,IAAI,IAAI,CAACqB,QAAQ,CAACG,GAAG,CAACN,QAAQ,CAAC,EAAE;MAC/B,OAAO5C,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAACI,GAAG,CAACP,QAAQ,CAAC,CAAC;IAC/C;IAEA,OAAO5C,SAAS,CAAC4C,QAAQ,CAAC;EAC5B,CAAC;EAEDO,GAAGA,CAAsCV,WAAwB,EAAE;IACjE,MAAM;MAAE3B,QAAQ;MAAE8D;IAAY,CAAC,GAAG,IAAI;IACtC,MAAM;MAAEjC;IAAS,CAAC,GAAGF,WAAW,CAACf,IAAI;IACrC,MAAMuB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACR,WAAW,CAAC;IAEnC,IAAIZ,MAAM;IACV,IAAIf,QAAQ,EAAE;MAAA,IAAA+D,YAAA;MACZhD,MAAM,IAAAgD,YAAA,GACJD,WAAW,CAAC,CAAC,YAAAC,YAAA,GACb3E,gBAAgB,CAACD,UAAU,CAAC,UAAU,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC;IACrE,CAAC,MAAM;MAAA,IAAA6E,aAAA;MACLjD,MAAM,GAAG3B,gBAAgB,EAAA4E,aAAA,GACvBF,WAAW,CAAC,CAAC,YAAAE,aAAA,GAAI7E,UAAU,CAAC,QAAQ,CAAC,EACrCA,UAAU,CAAC,WAAW,CACxB,CAAC;IACH;IAEA,OAAOC,gBAAgB,CAAC2B,MAAM,EAAEoB,IAAI,EAAEN,QAAQ,CAAC;EACjD,CAAC;EAEDK,GAAGA,CAEDP,WAAwB,EACxBoB,KAAmB,EACnB;IACA,MAAM;MAAElB;IAAS,CAAC,GAAGF,WAAW,CAACf,IAAI;IACrC,MAAMuB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACR,WAAW,CAAC;IAEnC,OAAO5C,oBAAoB,CACzB,GAAG,EACHK,gBAAgB,CAACG,cAAc,CAAC,CAAC,EAAE4C,IAAI,EAAEN,QAAQ,CAAC,EAClDkB,KACF,CAAC;EACH,CAAC;EAEDE,cAAcA,CAEZtB,WAAwB,EACxB;IACA,MAAM;MAAEE;IAAS,CAAC,GAAGF,WAAW,CAACf,IAAI;IACrC,MAAMuB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACR,WAAW,CAAC;IAEnC,OAAOvC,gBAAgB,CAACG,cAAc,CAAC,CAAC,EAAE4C,IAAI,EAAEN,QAAQ,CAAC;EAC3D,CAAC;EAEDsB,IAAIA,CAEFxB,WAAwB,EACxByB,IAAmC,EACnC;IACA,OAAO,IAAAC,qCAAY,EAAC,IAAI,CAAChB,GAAG,CAACV,WAAW,CAAC,EAAEpC,cAAc,CAAC,CAAC,EAAE6D,IAAI,EAAE,KAAK,CAAC;EAC3E,CAAC;EAEDE,YAAYA,CAEV3B,WAAwB,EACxByB,IAAmC,EACnC;IACA,OAAO,IAAAC,qCAAY,EAAC,IAAI,CAAChB,GAAG,CAACV,WAAW,CAAC,EAAEpC,cAAc,CAAC,CAAC,EAAE6D,IAAI,EAAE,IAAI,CAAC;EAC1E;AAAC,EACF;AAsCc,MAAMa,aAAa,CAAC;EACjCC,WAAWA,CAACC,IAA0B,EAAE;IAAA,IAAAC,mBAAA;IACtC,MAAM1D,IAAI,GAAGyD,IAAI,CAACE,UAAU;IAE5B,IAAI,CAACA,UAAU,GAAG3D,IAAI;IACtB,IAAI,CAACkC,oBAAoB,GACvBlC,IAAI,CAAC4D,aAAa,CAAC;MAAEC,IAAI,EAAE;IAAc,CAAC,CAAC,IAAI,CAAC,CAACJ,IAAI,CAACK,QAAQ;IAChE,IAAI,CAACxE,QAAQ,GACXU,IAAI,CAAC+D,cAAc,CAAC,CAAC,IAErB/D,IAAI,CAACE,IAAI,CAAC8D,MAAM,KAChBhE,IAAI,CAACiE,aAAa,oBAAlBjE,IAAI,CAACiE,aAAa,CAAG,CAAC;IACxB,IAAI,CAACzE,eAAe,GAAGQ,IAAI,CAACkE,SAAS,CAAC,CAAC,IAAIlE,IAAI,CAACmE,QAAQ,CAAC,CAAC;IAE1D,IAAI,CAAC5E,IAAI,GAAGkE,IAAI,CAAClE,IAAI;IACrB,IAAI,CAAC6E,aAAa,IAAAV,mBAAA,GAGdD,IAAI,CAACW,aAAa,YAAAV,mBAAA,GAAKD,IAAI,CAASY,OAAO;IAC/C,IAAI,CAACZ,IAAI,GAAGA,IAAI;EAClB;EAUAzB,YAAYA,CAAA,EAAG;IACb,OAAOxD,SAAS,CAAC,IAAI,CAACiF,IAAI,CAACpE,SAAS,IAAI,IAAI,CAACoE,IAAI,CAACzB,YAAY,CAAC,CAAC,CAAC;EACnE;EAEAoB,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACK,IAAI,CAACK,QAAQ,EAAE,OAAOtF,SAAS,CAAC,IAAI,CAACiF,IAAI,CAACK,QAAQ,CAAC;IAC5D,IAAI,IAAI,CAACL,IAAI,CAACL,WAAW,EAAE;MACzB,OAAO5E,SAAS,CAAC,IAAI,CAACiF,IAAI,CAACL,WAAW,CAAC,CAAC,CAAC;IAC3C;EACF;EAEAkB,OAAOA,CAAA,EAAG;IAER,IAAI,IAAI,CAACb,IAAI,CAACc,aAAa,EAAE;MAC3B,IAAI,CAACZ,UAAU,CAAC/D,QAAQ,CAACW,2BAA2B,EAAE;QACpDE,OAAO,EAAE,IAAI,CAACgD,IAAI,CAACc,aAAa,CAAC1D;MACnC,CAAC,CAAC;IACJ;IAEA,MAAM2D,OAAO,GAAG,IAAI,CAACJ,aAAa,GAAGnB,aAAa,GAAGlC,YAAY;IAEjE,IAAA0D,0CAA2B,EAAe,IAAI,CAACd,UAAU,EAAEhE,OAAO,EAAAuD,MAAA,CAAAC,MAAA;MAChE5D,IAAI,EAAE,IAAI,CAACA,IAAI;MACfoB,KAAK,EAAE,IAAI,CAACgD,UAAU,CAAChD,KAAK;MAC5BuB,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/C5C,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBE,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCwC,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC0C,IAAI,CAAC,IAAI,CAAC;MAC1CtB,WAAW,EAAE,IAAI,CAACA,WAAW,CAACsB,IAAI,CAAC,IAAI,CAAC;MAExCC,QAAQ,EAAEH,OAAO,CAAC7C;IAAG,GAClB6C,OAAO,CACX,CAAC;EACJ;AACF;AAACxF,OAAA,CAAAE,OAAA,GAAAqE,aAAA"}