{"_from": "@babel/plugin-transform-runtime@^7.11.0", "_id": "@babel/plugin-transform-runtime@7.24.0", "_inBundle": false, "_integrity": "sha512-zc0GA5IitLKJrSfXlXmp8KDqLrnGECK7YRfQBmEKg1NmBOQ7e+KuclBEKJgzifQeUYLdNiAw4B4bjyvzWVLiSA==", "_location": "/@babel/plugin-transform-runtime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-runtime@^7.11.0", "name": "@babel/plugin-transform-runtime", "escapedName": "@babel%2fplugin-transform-runtime", "scope": "@babel", "rawSpec": "^7.11.0", "saveSpec": null, "fetchSpec": "^7.11.0"}, "_requiredBy": ["/@vue/babel-preset-app"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.24.0.tgz", "_shasum": "e308fe27d08b74027d42547081eefaf4f2ffbcc9", "_spec": "@babel/plugin-transform-runtime@^7.11.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@vue\\babel-preset-app", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "browser": {"./lib/get-runtime-path/index.js": "./lib/get-runtime-path/browser.js", "./src/get-runtime-path/index.ts": "./src/get-runtime-path/browser.ts"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-imports": "^7.22.15", "@babel/helper-plugin-utils": "^7.24.0", "babel-plugin-polyfill-corejs2": "^0.4.8", "babel-plugin-polyfill-corejs3": "^0.9.0", "babel-plugin-polyfill-regenerator": "^0.5.5", "semver": "^6.3.1"}, "deprecated": false, "description": "Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals", "devDependencies": {"@babel/core": "^7.24.0", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/helpers": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/runtime": "^7.24.0", "@babel/runtime-corejs3": "^7.24.0", "make-dir": "^2.1.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-runtime", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-runtime", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-runtime"}, "type": "commonjs", "version": "7.24.0"}