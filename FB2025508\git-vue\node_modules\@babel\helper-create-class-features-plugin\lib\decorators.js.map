{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "_helperSplitExportDeclaration", "_helperSkipTransparentExpressionWrappers", "_fields", "incrementId", "id", "idx", "length", "unshift", "current", "createPrivateUidGeneratorForClass", "classPath", "currentPrivateId", "privateNames", "Set", "traverse", "PrivateName", "path", "add", "node", "name", "reifiedId", "String", "fromCharCode", "has", "t", "privateName", "identifier", "createLazyPrivateUidGeneratorForClass", "generator", "replaceClassWithVar", "className", "type", "varId", "scope", "generateUidIdentifierBasedOnNode", "classId", "rename", "get", "replaceWith", "cloneNode", "parent", "generateDeclaredUidIdentifier", "newClassExpr", "classExpression", "superClass", "body", "newPath", "sequenceExpression", "generateClassProperty", "key", "value", "isStatic", "classPrivateProperty", "undefined", "classProperty", "addProxyAccessorsFor", "element", "original<PERSON>ey", "<PERSON><PERSON><PERSON>", "version", "isComputed", "thisArg", "thisExpression", "getterBody", "blockStatement", "returnStatement", "memberExpression", "setterBody", "expressionStatement", "assignmentExpression", "getter", "setter", "classPrivateMethod", "classMethod", "insertAfter", "extractProxyAccessorsFor", "template", "expression", "ast", "prependExpressionsToFieldInitializer", "expressions", "fieldPath", "initializer", "push", "unaryExpression", "maybeSequenceExpression", "prependExpressionsToStaticBlock", "blockPath", "unshiftContainer", "prependExpressionsToConstructor", "constructorPath", "isProtoInitCallExpression", "protoInitCall", "isCallExpression", "isIdentifier", "callee", "optimizeSuperCallAndExpressions", "protoInitLocal", "mergedSuperCall", "callExpression", "splice", "isThisExpression", "insertExpressionsAfterSuperCallAndOptimize", "CallExpression", "exit", "is<PERSON><PERSON><PERSON>", "newNodes", "map", "expr", "isCompletionRecord", "skip", "ClassMethod", "kind", "createConstructorFromExpressions", "isDerivedClass", "super", "spreadElement", "restElement", "createStaticBlockFromExpressions", "staticBlock", "FIELD", "ACCESSOR", "METHOD", "GETTER", "SETTER", "STATIC_OLD_VERSION", "STATIC", "DECORATORS_HAVE_THIS", "getElementKind", "toSortedDecoratorInfo", "info", "filter", "el", "generateDecorationList", "decorators", "decoratorsThis", "decsCount", "haveOneThis", "some", "Boolean", "decs", "i", "numericLiteral", "haveThis", "generateDecorationExprs", "decorationInfo", "arrayExpression", "flag", "decoratorsHaveThis", "decoratorsArray", "privateMethods", "extractElementLocalAssignments", "localIds", "locals", "Array", "isArray", "addCallAccessorsFor", "getId", "setId", "movePrivateAccessor", "methodLocalVar", "params", "block", "isClassDecoratableElementPath", "staticBlockToIIFE", "arrowFunctionExpression", "exprs", "createFunctionExpressionFromPrivateMethod", "isGenerator", "async", "isAsync", "functionExpression", "createSetFunctionNameCall", "state", "addHelper", "createToPropertyKeyCall", "propertyKey", "createPrivateBrandCheckClosure", "brandName", "binaryExpression", "usesFunctionContext", "traverseFast", "isMetaProperty", "meta", "_unused", "usesPrivateField", "isPrivateName", "_unused2", "checkPrivateMethodUpdateError", "decoratedPrivateMethods", "privateNameVisitor", "privateNameVisitorFactory", "privateNamesMap", "parentPath", "parentParentPath", "left", "buildCodeFrameError", "Map", "set", "transformClass", "constant<PERSON>uper", "propertyVisitor", "_classDecorationsId", "classDecorators", "hasElementDecorators", "hasComputedKeysSideEffects", "elemDecsUseFnContext", "generateClassPrivateUid", "assignments", "scopeParent", "memoiseExpression", "hint", "localEvaluatedId", "staticInitLocal", "isDecorated", "ClassProperty", "ClassPrivateProperty", "ClassAccessorProperty", "static", "_staticInitLocal", "_protoInitLocal", "computed", "newId", "newField", "keyP<PERSON>", "isConstantExpression", "insertBefore", "crawl", "elementDecoratorInfo", "classInitLocal", "classIdLocal", "decoratorReceiverId", "handleDecoratorExpressions", "hasSideEffects", "usesFnContext", "object", "isMemberExpression", "_decoratorReceiverId", "willExtractSomeElemDecs", "needsDeclaraionForClassBinding", "classDecorationsFlag", "classDecorations", "classDecorationsId", "isClassDeclaration", "decoratorExpressions", "classDecsUsePrivateName", "generateUidIdentifier", "lastInstancePrivateName", "needsInstancePrivateBrandCheck", "fieldInitializerExpressions", "staticFieldInitializerExpressions", "isStaticBlock", "hasDecorators", "d", "isPrivate", "isClassPrivateProperty", "isClassMethod", "newFieldInitId", "newValue", "initId", "valuePath", "args", "callId", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "file", "refToPreserve", "replace", "remove", "nameExpr", "stringLiteral", "initExtraId", "initExtraCall", "sortedElementDecoratorInfo", "elementDecorations", "elementLocals", "classLocals", "classInitInjected", "classInitCall", "originalClass", "statics", "for<PERSON>ach", "isProperty", "isClassProperty", "isClassPrivateMethod", "staticsClass", "toStatement", "constructorBody", "newExpr", "newExpression", "arguments", "maybeGenerateMemoised", "createLocalsAssignment", "variableDeclaration", "variableDeclarator", "size", "maybePrivateBrandName", "setClassName", "lhs", "rhs", "availableHelper", "arrayPattern", "objectPattern", "objectProperty", "isProtoKey", "shouldTransformElement", "shouldTransformClass", "NamedEvaluationVisitoryFactory", "isAnonymous", "visitor", "handleComputedProperty", "propertyPath", "keyValue", "ref", "VariableDeclarator", "skipTransparentExprWrappers", "AssignmentExpression", "operator", "AssignmentPattern", "ObjectExpression", "isDecoratedAnonymousClassExpression", "isClassExpression", "_default", "assertVersion", "assumption", "loose", "inherits", "_assumption", "VISITED", "WeakSet", "namedEvaluationVisitor", "visitClass", "_className", "_node$id", "Object", "assign", "ExportDefaultDeclaration", "declaration", "updatedVarDeclarationPath", "splitExportDeclaration", "ExportNamedDeclaration", "Class"], "sources": ["../src/decorators.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Visitor } from \"@babel/traverse\";\nimport { types as t, template } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport * as charCodes from \"charcodes\";\nimport type { PluginAPI, PluginObject, PluginPass } from \"@babel/core\";\nimport { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport {\n  privateNameVisitorFactory,\n  type PrivateNameVisitorState,\n} from \"./fields.ts\";\n\ninterface Options {\n  /** @deprecated use `constantSuper` assumption instead. Only supported in 2021-12 version. */\n  loose?: boolean;\n}\n\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty;\n\ntype ClassElement =\n  | ClassDecoratableElement\n  | t.TSDeclareMethod\n  | t.TSIndexSignature\n  | t.<PERSON>;\n\n// TODO(Babel 8): Only keep 2023-11\nexport type DecoratorVersionKind =\n  | \"2023-11\"\n  | \"2023-05\"\n  | \"2023-01\"\n  | \"2022-03\"\n  | \"2021-12\";\n\nfunction incrementId(id: number[], idx = id.length - 1): void {\n  // If index is -1, id needs an additional character, unshift A\n  if (idx === -1) {\n    id.unshift(charCodes.uppercaseA);\n    return;\n  }\n\n  const current = id[idx];\n\n  if (current === charCodes.uppercaseZ) {\n    // if current is Z, skip to a\n    id[idx] = charCodes.lowercaseA;\n  } else if (current === charCodes.lowercaseZ) {\n    // if current is z, reset to A and carry the 1\n    id[idx] = charCodes.uppercaseA;\n    incrementId(id, idx - 1);\n  } else {\n    // else, increment by one\n    id[idx] = current + 1;\n  }\n}\n\n/**\n * Generates a new private name that is unique to the given class. This can be\n * used to create extra class fields and methods for the implementation, while\n * keeping the length of those names as small as possible. This is important for\n * minification purposes (though private names can generally be minified,\n * transpilations and polyfills cannot yet).\n */\nfunction createPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  const currentPrivateId: number[] = [];\n  const privateNames = new Set<string>();\n\n  classPath.traverse({\n    PrivateName(path) {\n      privateNames.add(path.node.id.name);\n    },\n  });\n\n  return (): t.PrivateName => {\n    let reifiedId;\n    do {\n      incrementId(currentPrivateId);\n      reifiedId = String.fromCharCode(...currentPrivateId);\n    } while (privateNames.has(reifiedId));\n\n    return t.privateName(t.identifier(reifiedId));\n  };\n}\n\n/**\n * Wraps the above generator function so that it's run lazily the first time\n * it's actually required. Several types of decoration do not require this, so it\n * saves iterating the class elements an additional time and allocating the space\n * for the Sets of element names.\n */\nfunction createLazyPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  let generator: () => t.PrivateName;\n\n  return (): t.PrivateName => {\n    if (!generator) {\n      generator = createPrivateUidGeneratorForClass(classPath);\n    }\n\n    return generator();\n  };\n}\n\n/**\n * Takes a class definition and the desired class name if anonymous and\n * replaces it with an equivalent class declaration (path) which is then\n * assigned to a local variable (id). This allows us to reassign the local variable with the\n * decorated version of the class. The class definition retains its original\n * name so that `toString` is not affected, other references to the class\n * are renamed instead.\n */\nfunction replaceClassWithVar(\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n): {\n  id: t.Identifier;\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>;\n} {\n  if (path.type === \"ClassDeclaration\") {\n    const id = path.node.id;\n    const className = id.name;\n    const varId = path.scope.generateUidIdentifierBasedOnNode(id);\n    const classId = t.identifier(className);\n\n    path.scope.rename(className, varId.name);\n\n    path.get(\"id\").replaceWith(classId);\n\n    return { id: t.cloneNode(varId), path };\n  } else {\n    let varId: t.Identifier;\n\n    if (path.node.id) {\n      className = path.node.id.name;\n      varId = path.scope.parent.generateDeclaredUidIdentifier(className);\n      path.scope.rename(className, varId.name);\n    } else {\n      varId = path.scope.parent.generateDeclaredUidIdentifier(\n        typeof className === \"string\" ? className : \"decorated_class\",\n      );\n    }\n\n    const newClassExpr = t.classExpression(\n      typeof className === \"string\" ? t.identifier(className) : null,\n      path.node.superClass,\n      path.node.body,\n    );\n\n    const [newPath] = path.replaceWith(\n      t.sequenceExpression([newClassExpr, varId]),\n    );\n\n    return {\n      id: t.cloneNode(varId),\n      path: newPath.get(\"expressions.0\") as NodePath<t.ClassExpression>,\n    };\n  }\n}\n\nfunction generateClassProperty(\n  key: t.PrivateName | t.Identifier,\n  value: t.Expression | undefined,\n  isStatic: boolean,\n): t.ClassPrivateProperty | t.ClassProperty {\n  if (key.type === \"PrivateName\") {\n    return t.classPrivateProperty(key, value, undefined, isStatic);\n  } else {\n    return t.classProperty(key, value, undefined, undefined, isStatic);\n  }\n}\n\nfunction addProxyAccessorsFor(\n  className: t.Identifier,\n  element: NodePath<ClassDecoratableElement>,\n  originalKey: t.PrivateName | t.Expression,\n  targetKey: t.PrivateName,\n  version: DecoratorVersionKind,\n  isComputed: boolean,\n  isStatic: boolean,\n): void {\n  const thisArg =\n    (version === \"2023-11\" ||\n      (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n    isStatic\n      ? className\n      : t.thisExpression();\n\n  const getterBody = t.blockStatement([\n    t.returnStatement(\n      t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n    ),\n  ]);\n\n  const setterBody = t.blockStatement([\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n        t.identifier(\"v\"),\n      ),\n    ),\n  ]);\n\n  let getter: t.ClassMethod | t.ClassPrivateMethod,\n    setter: t.ClassMethod | t.ClassPrivateMethod;\n\n  if (originalKey.type === \"PrivateName\") {\n    getter = t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(originalKey),\n      [],\n      getterBody,\n      isStatic,\n    );\n    setter = t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(originalKey),\n      [t.identifier(\"v\")],\n      setterBody,\n      isStatic,\n    );\n  } else {\n    getter = t.classMethod(\n      \"get\",\n      t.cloneNode(originalKey),\n      [],\n      getterBody,\n      isComputed,\n      isStatic,\n    );\n    setter = t.classMethod(\n      \"set\",\n      t.cloneNode(originalKey),\n      [t.identifier(\"v\")],\n      setterBody,\n      isComputed,\n      isStatic,\n    );\n  }\n\n  element.insertAfter(setter);\n  element.insertAfter(getter);\n}\n\nfunction extractProxyAccessorsFor(\n  targetKey: t.PrivateName,\n  version: DecoratorVersionKind,\n): (t.FunctionExpression | t.ArrowFunctionExpression)[] {\n  if (version !== \"2023-11\" && version !== \"2023-05\" && version !== \"2023-01\") {\n    return [\n      template.expression.ast`\n        function () {\n          return this.${t.cloneNode(targetKey)};\n        }\n      ` as t.FunctionExpression,\n      template.expression.ast`\n        function (value) {\n          this.${t.cloneNode(targetKey)} = value;\n        }\n      ` as t.FunctionExpression,\n    ];\n  }\n  return [\n    template.expression.ast`\n      o => o.${t.cloneNode(targetKey)}\n    ` as t.ArrowFunctionExpression,\n    template.expression.ast`\n      (o, v) => o.${t.cloneNode(targetKey)} = v\n    ` as t.ArrowFunctionExpression,\n  ];\n}\n\n/**\n * Prepend expressions to the field initializer. If the initializer is not defined,\n * this function will wrap the last expression within a `void` unary expression.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction prependExpressionsToFieldInitializer(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n  >,\n) {\n  const initializer = fieldPath.get(\"value\");\n  if (initializer.node) {\n    expressions.push(initializer.node);\n  } else if (expressions.length > 0) {\n    expressions[expressions.length - 1] = t.unaryExpression(\n      \"void\",\n      expressions[expressions.length - 1],\n    );\n  }\n  initializer.replaceWith(maybeSequenceExpression(expressions));\n}\n\nfunction prependExpressionsToStaticBlock(\n  expressions: t.Expression[],\n  blockPath: NodePath<t.StaticBlock>,\n) {\n  blockPath.unshiftContainer(\n    \"body\",\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction prependExpressionsToConstructor(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n) {\n  constructorPath.node.body.body.unshift(\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction isProtoInitCallExpression(\n  expression: t.Expression,\n  protoInitCall: t.Identifier,\n) {\n  return (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee, { name: protoInitCall.name })\n  );\n}\n\n/**\n * Optimize super call and its following expressions\n *\n * @param {t.Expression[]} expressions Mutated by this function. The first element must by a super call\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns optimized expression\n */\nfunction optimizeSuperCallAndExpressions(\n  expressions: t.Expression[],\n  protoInitLocal: t.Identifier,\n) {\n  // Merge `super(), protoInit(this)` into `protoInit(super())`\n  if (\n    expressions.length >= 2 &&\n    isProtoInitCallExpression(expressions[1], protoInitLocal)\n  ) {\n    const mergedSuperCall = t.callExpression(t.cloneNode(protoInitLocal), [\n      expressions[0],\n    ]);\n    expressions.splice(0, 2, mergedSuperCall);\n  }\n  // Merge `protoInit(super()), this` into `protoInit(super())`\n  if (\n    expressions.length >= 2 &&\n    t.isThisExpression(expressions[expressions.length - 1]) &&\n    isProtoInitCallExpression(\n      expressions[expressions.length - 2],\n      protoInitLocal,\n    )\n  ) {\n    expressions.splice(expressions.length - 1, 1);\n  }\n  return maybeSequenceExpression(expressions);\n}\n\n/**\n * Insert expressions immediately after super() and optimize the output if possible.\n * This function will preserve the completion result using the trailing this expression.\n *\n * @param {t.Expression[]} expressions\n * @param {NodePath<t.ClassMethod>} constructorPath\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns\n */\nfunction insertExpressionsAfterSuperCallAndOptimize(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n  protoInitLocal: t.Identifier,\n) {\n  constructorPath.traverse({\n    CallExpression: {\n      exit(path) {\n        if (!path.get(\"callee\").isSuper()) return;\n        const newNodes = [\n          path.node,\n          ...expressions.map(expr => t.cloneNode(expr)),\n        ];\n        // preserve completion result if super() is in an RHS or a return statement\n        if (path.isCompletionRecord()) {\n          newNodes.push(t.thisExpression());\n        }\n        path.replaceWith(\n          optimizeSuperCallAndExpressions(newNodes, protoInitLocal),\n        );\n\n        path.skip();\n      },\n    },\n    ClassMethod(path) {\n      if (path.node.kind === \"constructor\") {\n        path.skip();\n      }\n    },\n  });\n}\n\n/**\n * Build a class constructor node from the given expressions. If the class is\n * derived, the constructor will call super() first to ensure that `this`\n * in the expressions work as expected.\n *\n * @param {t.Expression[]} expressions\n * @param {boolean} isDerivedClass\n * @returns The class constructor node\n */\nfunction createConstructorFromExpressions(\n  expressions: t.Expression[],\n  isDerivedClass: boolean,\n) {\n  const body: t.Statement[] = [\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ];\n  if (isDerivedClass) {\n    body.unshift(\n      t.expressionStatement(\n        t.callExpression(t.super(), [t.spreadElement(t.identifier(\"args\"))]),\n      ),\n    );\n  }\n  return t.classMethod(\n    \"constructor\",\n    t.identifier(\"constructor\"),\n    isDerivedClass ? [t.restElement(t.identifier(\"args\"))] : [],\n    t.blockStatement(body),\n  );\n}\n\nfunction createStaticBlockFromExpressions(expressions: t.Expression[]) {\n  return t.staticBlock([\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ]);\n}\n\n// 3 bits reserved to this (0-7)\nconst FIELD = 0;\nconst ACCESSOR = 1;\nconst METHOD = 2;\nconst GETTER = 3;\nconst SETTER = 4;\n\nconst STATIC_OLD_VERSION = 5; // Before 2023-05\nconst STATIC = 8; // 1 << 3\nconst DECORATORS_HAVE_THIS = 16; // 1 << 4\n\nfunction getElementKind(element: NodePath<ClassDecoratableElement>): number {\n  switch (element.node.type) {\n    case \"ClassProperty\":\n    case \"ClassPrivateProperty\":\n      return FIELD;\n    case \"ClassAccessorProperty\":\n      return ACCESSOR;\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n      if (element.node.kind === \"get\") {\n        return GETTER;\n      } else if (element.node.kind === \"set\") {\n        return SETTER;\n      } else {\n        return METHOD;\n      }\n  }\n}\n\n// Information about the decorators applied to an element\ninterface DecoratorInfo {\n  // An array of applied decorators or a memoised identifier\n  decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n  decoratorsHaveThis: boolean;\n\n  // The kind of the decorated value, matches the kind value passed to applyDecs\n  kind: number;\n\n  // whether or not the field is static\n  isStatic: boolean;\n\n  // The name of the decorator\n  name: t.StringLiteral | t.Expression;\n\n  privateMethods:\n    | (t.FunctionExpression | t.ArrowFunctionExpression)[]\n    | undefined;\n\n  // The names of local variables that will be used/returned from the decoration\n  locals: t.Identifier | t.Identifier[] | undefined;\n}\n\n/**\n * Sort decoration info in the application order:\n * - static non-fields\n * - instance non-fields\n * - static fields\n * - instance fields\n *\n * @param {DecoratorInfo[]} info\n * @returns {DecoratorInfo[]} Sorted decoration info\n */\nfunction toSortedDecoratorInfo(info: DecoratorInfo[]): DecoratorInfo[] {\n  return [\n    ...info.filter(\n      el => el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(\n      el => !el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(el => el.isStatic && el.kind === FIELD),\n    ...info.filter(el => !el.isStatic && el.kind === FIELD),\n  ];\n}\n\ntype GenerateDecorationListResult = {\n  // The zipped decorators array that will be passed to generateDecorationExprs\n  decs: t.Expression[];\n  // Whether there are non-empty decorator this values\n  haveThis: boolean;\n};\n/**\n * Zip decorators and decorator this values into an array\n *\n * @param {t.Expression[]} decorators\n * @param {((t.Expression | undefined)[])} decoratorsThis decorator this values\n * @param {DecoratorVersionKind} version\n * @returns {GenerateDecorationListResult}\n */\nfunction generateDecorationList(\n  decorators: t.Expression[],\n  decoratorsThis: (t.Expression | undefined)[],\n  version: DecoratorVersionKind,\n): GenerateDecorationListResult {\n  const decsCount = decorators.length;\n  const haveOneThis = decoratorsThis.some(Boolean);\n  const decs: t.Expression[] = [];\n  for (let i = 0; i < decsCount; i++) {\n    if (\n      (version === \"2023-11\" ||\n        (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n      haveOneThis\n    ) {\n      decs.push(\n        decoratorsThis[i] || t.unaryExpression(\"void\", t.numericLiteral(0)),\n      );\n    }\n    decs.push(decorators[i]);\n  }\n\n  return { haveThis: haveOneThis, decs };\n}\n\nfunction generateDecorationExprs(\n  decorationInfo: DecoratorInfo[],\n  version: DecoratorVersionKind,\n): t.ArrayExpression {\n  return t.arrayExpression(\n    decorationInfo.map(el => {\n      let flag = el.kind;\n      if (el.isStatic) {\n        flag +=\n          version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")\n            ? STATIC\n            : STATIC_OLD_VERSION;\n      }\n      if (el.decoratorsHaveThis) flag += DECORATORS_HAVE_THIS;\n\n      return t.arrayExpression([\n        el.decoratorsArray,\n        t.numericLiteral(flag),\n        el.name,\n        ...(el.privateMethods || []),\n      ]);\n    }),\n  );\n}\n\nfunction extractElementLocalAssignments(decorationInfo: DecoratorInfo[]) {\n  const localIds: t.Identifier[] = [];\n\n  for (const el of decorationInfo) {\n    const { locals } = el;\n\n    if (Array.isArray(locals)) {\n      localIds.push(...locals);\n    } else if (locals !== undefined) {\n      localIds.push(locals);\n    }\n  }\n\n  return localIds;\n}\n\nfunction addCallAccessorsFor(\n  version: DecoratorVersionKind,\n  element: NodePath,\n  key: t.PrivateName,\n  getId: t.Identifier,\n  setId: t.Identifier,\n  isStatic: boolean,\n) {\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(key),\n      [],\n      t.blockStatement([\n        t.returnStatement(\n          t.callExpression(\n            t.cloneNode(getId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(key),\n      [t.identifier(\"v\")],\n      t.blockStatement([\n        t.expressionStatement(\n          t.callExpression(\n            t.cloneNode(setId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? [t.identifier(\"v\")]\n              : [t.thisExpression(), t.identifier(\"v\")],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n}\n\nfunction movePrivateAccessor(\n  element: NodePath<t.ClassPrivateMethod>,\n  key: t.PrivateName,\n  methodLocalVar: t.Identifier,\n  isStatic: boolean,\n) {\n  let params: (t.Identifier | t.RestElement)[];\n  let block: t.Statement[];\n\n  if (element.node.kind === \"set\") {\n    params = [t.identifier(\"v\")];\n    block = [\n      t.expressionStatement(\n        t.callExpression(methodLocalVar, [\n          t.thisExpression(),\n          t.identifier(\"v\"),\n        ]),\n      ),\n    ];\n  } else {\n    params = [];\n    block = [\n      t.returnStatement(t.callExpression(methodLocalVar, [t.thisExpression()])),\n    ];\n  }\n\n  element.replaceWith(\n    t.classPrivateMethod(\n      element.node.kind,\n      t.cloneNode(key),\n      params,\n      t.blockStatement(block),\n      isStatic,\n    ),\n  );\n}\n\nfunction isClassDecoratableElementPath(\n  path: NodePath<ClassElement>,\n): path is NodePath<ClassDecoratableElement> {\n  const { type } = path;\n\n  return (\n    type !== \"TSDeclareMethod\" &&\n    type !== \"TSIndexSignature\" &&\n    type !== \"StaticBlock\"\n  );\n}\n\nfunction staticBlockToIIFE(block: t.StaticBlock) {\n  return t.callExpression(\n    t.arrowFunctionExpression([], t.blockStatement(block.body)),\n    [],\n  );\n}\n\nfunction maybeSequenceExpression(exprs: t.Expression[]) {\n  if (exprs.length === 0) return t.unaryExpression(\"void\", t.numericLiteral(0));\n  if (exprs.length === 1) return exprs[0];\n  return t.sequenceExpression(exprs);\n}\n\n/**\n * Create FunctionExpression from a ClassPrivateMethod.\n * The returned FunctionExpression node takes ownership of the private method's body and params.\n *\n * @param {t.ClassPrivateMethod} node\n * @returns\n */\nfunction createFunctionExpressionFromPrivateMethod(node: t.ClassPrivateMethod) {\n  const { params, body, generator: isGenerator, async: isAsync } = node;\n  return t.functionExpression(\n    undefined,\n    // @ts-expect-error todo: Improve typings: TSParameterProperty is only allowed in constructor\n    params,\n    body,\n    isGenerator,\n    isAsync,\n  );\n}\n\nfunction createSetFunctionNameCall(\n  state: PluginPass,\n  className: t.Identifier | t.StringLiteral,\n) {\n  return t.callExpression(state.addHelper(\"setFunctionName\"), [\n    t.thisExpression(),\n    className,\n  ]);\n}\n\nfunction createToPropertyKeyCall(state: PluginPass, propertyKey: t.Expression) {\n  return t.callExpression(state.addHelper(\"toPropertyKey\"), [propertyKey]);\n}\n\nfunction createPrivateBrandCheckClosure(brandName: t.PrivateName) {\n  return t.arrowFunctionExpression(\n    [t.identifier(\"_\")],\n    t.binaryExpression(\"in\", t.cloneNode(brandName), t.identifier(\"_\")),\n  );\n}\n\n// Check if the expression does not reference function-specific\n// context or the given identifier name.\n// `true` means \"maybe\" and `false` means \"no\".\nfunction usesFunctionContext(expression: t.Node) {\n  try {\n    t.traverseFast(expression, node => {\n      if (\n        t.isThisExpression(node) ||\n        t.isSuper(node) ||\n        t.isIdentifier(node, { name: \"arguments\" }) ||\n        (t.isMetaProperty(node) && node.meta.name !== \"import\")\n      ) {\n        // TODO: Add early return support to t.traverseFast\n        throw null;\n      }\n    });\n    return false;\n  } catch {\n    return true;\n  }\n}\n\nfunction usesPrivateField(expression: t.Node) {\n  try {\n    t.traverseFast(expression, node => {\n      if (t.isPrivateName(node)) {\n        // TODO: Add early return support to t.traverseFast\n        throw null;\n      }\n    });\n    return false;\n  } catch {\n    return true;\n  }\n}\n\nfunction checkPrivateMethodUpdateError(\n  path: NodePath<t.Class>,\n  decoratedPrivateMethods: Set<string>,\n) {\n  const privateNameVisitor = privateNameVisitorFactory<\n    PrivateNameVisitorState<null>,\n    null\n  >({\n    PrivateName(path, state) {\n      if (!state.privateNamesMap.has(path.node.id.name)) return;\n\n      const parentPath = path.parentPath;\n      const parentParentPath = parentPath.parentPath;\n\n      if (\n        // this.bar().#x = 123;\n        (parentParentPath.node.type === \"AssignmentExpression\" &&\n          parentParentPath.node.left === parentPath.node) ||\n        // this.#x++;\n        parentParentPath.node.type === \"UpdateExpression\" ||\n        // ([...this.#x] = foo);\n        parentParentPath.node.type === \"RestElement\" ||\n        // ([this.#x] = foo);\n        parentParentPath.node.type === \"ArrayPattern\" ||\n        // ({ a: this.#x } = bar);\n        (parentParentPath.node.type === \"ObjectProperty\" &&\n          parentParentPath.node.value === parentPath.node &&\n          parentParentPath.parentPath.type === \"ObjectPattern\") ||\n        // for (this.#x of []);\n        (parentParentPath.node.type === \"ForOfStatement\" &&\n          parentParentPath.node.left === parentPath.node)\n      ) {\n        throw path.buildCodeFrameError(\n          `Decorated private methods are read-only, but \"#${path.node.id.name}\" is updated via this expression.`,\n        );\n      }\n    },\n  });\n  const privateNamesMap = new Map<string, null>();\n  for (const name of decoratedPrivateMethods) {\n    privateNamesMap.set(name, null);\n  }\n  path.traverse(privateNameVisitor, {\n    privateNamesMap: privateNamesMap,\n  });\n}\n\nfunction transformClass(\n  path: NodePath<t.Class>,\n  state: PluginPass,\n  constantSuper: boolean,\n  version: DecoratorVersionKind,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n  propertyVisitor: Visitor<PluginPass>,\n): NodePath {\n  const body = path.get(\"body.body\");\n\n  const classDecorators = path.node.decorators;\n  let hasElementDecorators = false;\n  let hasComputedKeysSideEffects = false;\n  let elemDecsUseFnContext = false;\n\n  const generateClassPrivateUid = createLazyPrivateUidGeneratorForClass(path);\n\n  const assignments: t.AssignmentExpression[] = [];\n  const scopeParent: Scope = path.scope.parent;\n  const memoiseExpression = (expression: t.Expression, hint: string) => {\n    const localEvaluatedId = scopeParent.generateDeclaredUidIdentifier(hint);\n    assignments.push(t.assignmentExpression(\"=\", localEvaluatedId, expression));\n    return t.cloneNode(localEvaluatedId);\n  };\n\n  let protoInitLocal: t.Identifier;\n  let staticInitLocal: t.Identifier;\n  // Iterate over the class to see if we need to decorate it, and also to\n  // transform simple auto accessors which are not decorated, and handle inferred\n  // class name when the initializer of the class field is a class expression\n  for (const element of body) {\n    if (!isClassDecoratableElementPath(element)) {\n      continue;\n    }\n\n    if (isDecorated(element.node)) {\n      switch (element.node.type) {\n        case \"ClassProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassProperty should be callable. Improve typings.\n          propertyVisitor.ClassProperty(\n            element as NodePath<t.ClassProperty>,\n            state,\n          );\n          break;\n        case \"ClassPrivateProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassPrivateProperty should be callable. Improve typings.\n          propertyVisitor.ClassPrivateProperty(\n            element as NodePath<t.ClassPrivateProperty>,\n            state,\n          );\n          break;\n        case \"ClassAccessorProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n          propertyVisitor.ClassAccessorProperty(\n            element as NodePath<t.ClassAccessorProperty>,\n            state,\n          );\n          if (version === \"2023-11\") {\n            break;\n          }\n        /* fallthrough */\n        default:\n          if (element.node.static) {\n            staticInitLocal ??=\n              scopeParent.generateDeclaredUidIdentifier(\"initStatic\");\n          } else {\n            protoInitLocal ??=\n              scopeParent.generateDeclaredUidIdentifier(\"initProto\");\n          }\n          break;\n      }\n      hasElementDecorators = true;\n      elemDecsUseFnContext ||=\n        element.node.decorators.some(usesFunctionContext);\n    } else if (element.node.type === \"ClassAccessorProperty\") {\n      // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n      propertyVisitor.ClassAccessorProperty(\n        element as NodePath<t.ClassAccessorProperty>,\n        state,\n      );\n      const { key, value, static: isStatic, computed } = element.node;\n\n      const newId = generateClassPrivateUid();\n      const newField = generateClassProperty(newId, value, isStatic);\n      const keyPath = element.get(\"key\");\n      const [newPath] = element.replaceWith(newField);\n\n      addProxyAccessorsFor(\n        path.node.id,\n        newPath,\n        computed && !keyPath.isConstantExpression()\n          ? memoiseExpression(\n              createToPropertyKeyCall(state, key as t.Expression),\n              \"computedKey\",\n            )\n          : key,\n        newId,\n        version,\n        computed,\n        isStatic,\n      );\n    }\n\n    if (\"computed\" in element.node && element.node.computed) {\n      hasComputedKeysSideEffects ||= !scopeParent.isStatic(element.node.key);\n    }\n  }\n\n  if (!classDecorators && !hasElementDecorators) {\n    // If nothing is decorated but we have assignments, it must be the memoised\n    // computed keys of class accessors\n    if (assignments.length > 0) {\n      path.insertBefore(assignments.map(expr => t.expressionStatement(expr)));\n\n      // Recrawl the scope to make sure new identifiers are properly synced\n      path.scope.crawl();\n    }\n    // If nothing is decorated and no assignments inserted, return\n    return;\n  }\n\n  const elementDecoratorInfo: DecoratorInfo[] = [];\n\n  let constructorPath: NodePath<t.ClassMethod> | undefined;\n  const decoratedPrivateMethods = new Set<string>();\n\n  let classInitLocal: t.Identifier, classIdLocal: t.Identifier;\n  let decoratorReceiverId: t.Identifier | null = null;\n\n  // Memoise the this value `a.b` of decorator member expressions `@a.b.dec`,\n  type HandleDecoratorExpressionsResult = {\n    // whether the whole decorator list requires memoisation\n    hasSideEffects: boolean;\n    usesFnContext: boolean;\n    // the this value of each decorator if applicable\n    decoratorsThis: (t.Expression | undefined)[];\n  };\n  function handleDecoratorExpressions(\n    expressions: t.Expression[],\n  ): HandleDecoratorExpressionsResult {\n    let hasSideEffects = false;\n    let usesFnContext = false;\n    const decoratorsThis: (t.Expression | null)[] = [];\n    for (const expression of expressions) {\n      let object;\n      if (\n        (version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n        t.isMemberExpression(expression)\n      ) {\n        if (t.isSuper(expression.object)) {\n          object = t.thisExpression();\n        } else if (scopeParent.isStatic(expression.object)) {\n          object = t.cloneNode(expression.object);\n        } else {\n          decoratorReceiverId ??=\n            scopeParent.generateDeclaredUidIdentifier(\"obj\");\n          object = t.assignmentExpression(\n            \"=\",\n            t.cloneNode(decoratorReceiverId),\n            expression.object,\n          );\n          expression.object = t.cloneNode(decoratorReceiverId);\n        }\n      }\n      decoratorsThis.push(object);\n      hasSideEffects ||= !scopeParent.isStatic(expression);\n      usesFnContext ||= usesFunctionContext(expression);\n    }\n    return { hasSideEffects, usesFnContext, decoratorsThis };\n  }\n\n  const willExtractSomeElemDecs =\n    hasComputedKeysSideEffects ||\n    (process.env.BABEL_8_BREAKING\n      ? elemDecsUseFnContext\n      : elemDecsUseFnContext || version !== \"2023-11\");\n\n  let needsDeclaraionForClassBinding = false;\n  let classDecorationsFlag = 0;\n  let classDecorations: t.Expression[] = [];\n  let classDecorationsId: t.Identifier;\n  if (classDecorators) {\n    classInitLocal = scopeParent.generateDeclaredUidIdentifier(\"initClass\");\n    needsDeclaraionForClassBinding = path.isClassDeclaration();\n    ({ id: classIdLocal, path } = replaceClassWithVar(path, className));\n\n    path.node.decorators = null;\n\n    const decoratorExpressions = classDecorators.map(el => el.expression);\n    const classDecsUsePrivateName = decoratorExpressions.some(usesPrivateField);\n    const { hasSideEffects, decoratorsThis } =\n      handleDecoratorExpressions(decoratorExpressions);\n\n    const { haveThis, decs } = generateDecorationList(\n      decoratorExpressions,\n      decoratorsThis,\n      version,\n    );\n    classDecorationsFlag = haveThis ? 1 : 0;\n    classDecorations = decs;\n\n    if (\n      (hasSideEffects && willExtractSomeElemDecs) ||\n      classDecsUsePrivateName\n    ) {\n      classDecorationsId = memoiseExpression(\n        t.arrayExpression(classDecorations),\n        \"classDecs\",\n      );\n    }\n  } else {\n    if (!path.node.id) {\n      path.node.id = path.scope.generateUidIdentifier(\"Class\");\n    }\n    classIdLocal = t.cloneNode(path.node.id);\n  }\n\n  let lastInstancePrivateName: t.PrivateName;\n  let needsInstancePrivateBrandCheck = false;\n\n  let fieldInitializerExpressions = [];\n  let staticFieldInitializerExpressions: t.Expression[] = [];\n\n  if (hasElementDecorators) {\n    if (protoInitLocal) {\n      const protoInitCall = t.callExpression(t.cloneNode(protoInitLocal), [\n        t.thisExpression(),\n      ]);\n      fieldInitializerExpressions.push(protoInitCall);\n    }\n    for (const element of body) {\n      if (!isClassDecoratableElementPath(element)) {\n        if (\n          staticFieldInitializerExpressions.length > 0 &&\n          element.isStaticBlock()\n        ) {\n          prependExpressionsToStaticBlock(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n        continue;\n      }\n\n      const { node } = element;\n      const decorators = node.decorators;\n\n      const hasDecorators = !!decorators?.length;\n\n      const isComputed = \"computed\" in node && node.computed;\n\n      let name = \"computedKey\";\n\n      if (node.key.type === \"PrivateName\") {\n        name = node.key.id.name;\n      } else if (!isComputed && node.key.type === \"Identifier\") {\n        name = node.key.name;\n      }\n      let decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n      let decoratorsHaveThis;\n\n      if (hasDecorators) {\n        const decoratorExpressions = decorators.map(d => d.expression);\n        const { hasSideEffects, usesFnContext, decoratorsThis } =\n          handleDecoratorExpressions(decoratorExpressions);\n        const { decs, haveThis } = generateDecorationList(\n          decoratorExpressions,\n          decoratorsThis,\n          version,\n        );\n        decoratorsHaveThis = haveThis;\n        decoratorsArray = decs.length === 1 ? decs[0] : t.arrayExpression(decs);\n        if (usesFnContext || (hasSideEffects && willExtractSomeElemDecs)) {\n          decoratorsArray = memoiseExpression(decoratorsArray, name + \"Decs\");\n        }\n      }\n\n      if (isComputed) {\n        if (!element.get(\"key\").isConstantExpression()) {\n          node.key = memoiseExpression(\n            createToPropertyKeyCall(state, node.key as t.Expression),\n            \"computedKey\",\n          );\n        }\n      }\n\n      const { key, static: isStatic } = node;\n\n      const isPrivate = key.type === \"PrivateName\";\n\n      const kind = getElementKind(element);\n\n      if (isPrivate && !isStatic) {\n        if (hasDecorators) {\n          needsInstancePrivateBrandCheck = true;\n        }\n        if (t.isClassPrivateProperty(node) || !lastInstancePrivateName) {\n          lastInstancePrivateName = key;\n        }\n      }\n\n      if (element.isClassMethod({ kind: \"constructor\" })) {\n        constructorPath = element;\n      }\n\n      let locals: t.Identifier[];\n      if (hasDecorators) {\n        let privateMethods: Array<\n          t.FunctionExpression | t.ArrowFunctionExpression\n        >;\n\n        if (kind === ACCESSOR) {\n          const { value } = element.node as t.ClassAccessorProperty;\n\n          const params: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n\n          if (value) {\n            params.push(t.cloneNode(value));\n          }\n\n          const newId = generateClassPrivateUid();\n          const newFieldInitId =\n            element.scope.parent.generateDeclaredUidIdentifier(`init_${name}`);\n          const newValue = t.callExpression(\n            t.cloneNode(newFieldInitId),\n            params,\n          );\n\n          const newField = generateClassProperty(newId, newValue, isStatic);\n          const [newPath] = element.replaceWith(newField);\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(newId, version);\n\n            const getId = newPath.scope.parent.generateDeclaredUidIdentifier(\n              `get_${name}`,\n            );\n            const setId = newPath.scope.parent.generateDeclaredUidIdentifier(\n              `set_${name}`,\n            );\n\n            addCallAccessorsFor(version, newPath, key, getId, setId, isStatic);\n\n            locals = [newFieldInitId, getId, setId];\n          } else {\n            addProxyAccessorsFor(\n              path.node.id,\n              newPath,\n              key,\n              newId,\n              version,\n              isComputed,\n              isStatic,\n            );\n            locals = [newFieldInitId];\n          }\n        } else if (kind === FIELD) {\n          const initId = element.scope.parent.generateDeclaredUidIdentifier(\n            `init_${name}`,\n          );\n          const valuePath = (\n            element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n          ).get(\"value\");\n\n          const args: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n          if (valuePath.node) args.push(valuePath.node);\n\n          valuePath.replaceWith(t.callExpression(t.cloneNode(initId), args));\n\n          locals = [initId];\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(key, version);\n          }\n        } else if (isPrivate) {\n          const callId = element.scope.parent.generateDeclaredUidIdentifier(\n            `call_${name}`,\n          );\n          locals = [callId];\n\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element as NodePath<t.ClassPrivateMethod>,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          privateMethods = [\n            createFunctionExpressionFromPrivateMethod(\n              element.node as t.ClassPrivateMethod,\n            ),\n          ];\n\n          if (kind === GETTER || kind === SETTER) {\n            movePrivateAccessor(\n              element as NodePath<t.ClassPrivateMethod>,\n              t.cloneNode(key),\n              t.cloneNode(callId),\n              isStatic,\n            );\n          } else {\n            const node = element.node as t.ClassPrivateMethod;\n\n            // Unshift\n            path.node.body.body.unshift(\n              t.classPrivateProperty(key, t.cloneNode(callId), [], node.static),\n            );\n\n            decoratedPrivateMethods.add(key.id.name);\n\n            element.remove();\n          }\n        }\n\n        let nameExpr: t.Expression;\n\n        if (isComputed) {\n          nameExpr = t.cloneNode(key as t.Expression);\n        } else if (key.type === \"PrivateName\") {\n          nameExpr = t.stringLiteral(key.id.name);\n        } else if (key.type === \"Identifier\") {\n          nameExpr = t.stringLiteral(key.name);\n        } else {\n          nameExpr = t.cloneNode(key as t.Expression);\n        }\n\n        elementDecoratorInfo.push({\n          kind,\n          decoratorsArray,\n          decoratorsHaveThis,\n          name: nameExpr,\n          isStatic,\n          privateMethods,\n          locals,\n        });\n\n        if (element.node) {\n          element.node.decorators = null;\n        }\n      }\n\n      if (\n        fieldInitializerExpressions.length > 0 &&\n        !isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          fieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        fieldInitializerExpressions = [];\n      }\n\n      if (\n        staticFieldInitializerExpressions.length > 0 &&\n        isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          staticFieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        staticFieldInitializerExpressions = [];\n      }\n\n      if (hasDecorators && version === \"2023-11\") {\n        if (kind === FIELD || kind === ACCESSOR) {\n          const initExtraId = scopeParent.generateDeclaredUidIdentifier(\n            `init_extra_${name}`,\n          );\n          locals.push(initExtraId);\n          const initExtraCall = t.callExpression(\n            t.cloneNode(initExtraId),\n            isStatic ? [] : [t.thisExpression()],\n          );\n          if (!isStatic) {\n            fieldInitializerExpressions.push(initExtraCall);\n          } else {\n            staticFieldInitializerExpressions.push(initExtraCall);\n          }\n        }\n      }\n    }\n  }\n\n  if (fieldInitializerExpressions.length > 0) {\n    const isDerivedClass = !!path.node.superClass;\n    if (constructorPath) {\n      if (isDerivedClass) {\n        insertExpressionsAfterSuperCallAndOptimize(\n          fieldInitializerExpressions,\n          constructorPath,\n          protoInitLocal,\n        );\n      } else {\n        prependExpressionsToConstructor(\n          fieldInitializerExpressions,\n          constructorPath,\n        );\n      }\n    } else {\n      path.node.body.body.unshift(\n        createConstructorFromExpressions(\n          fieldInitializerExpressions,\n          isDerivedClass,\n        ),\n      );\n    }\n    fieldInitializerExpressions = [];\n  }\n\n  if (staticFieldInitializerExpressions.length > 0) {\n    path.node.body.body.push(\n      createStaticBlockFromExpressions(staticFieldInitializerExpressions),\n    );\n    staticFieldInitializerExpressions = [];\n  }\n\n  const sortedElementDecoratorInfo =\n    toSortedDecoratorInfo(elementDecoratorInfo);\n\n  const elementDecorations = generateDecorationExprs(\n    process.env.BABEL_8_BREAKING || version === \"2023-11\"\n      ? elementDecoratorInfo\n      : sortedElementDecoratorInfo,\n    version,\n  );\n\n  const elementLocals: t.Identifier[] = extractElementLocalAssignments(\n    sortedElementDecoratorInfo,\n  );\n\n  if (protoInitLocal) {\n    elementLocals.push(protoInitLocal);\n  }\n\n  if (staticInitLocal) {\n    elementLocals.push(staticInitLocal);\n  }\n\n  const classLocals: t.Identifier[] = [];\n  let classInitInjected = false;\n  const classInitCall =\n    classInitLocal && t.callExpression(t.cloneNode(classInitLocal), []);\n\n  const originalClass = path.node;\n\n  if (classDecorators) {\n    classLocals.push(classIdLocal, classInitLocal);\n    const statics: (\n      | t.ClassProperty\n      | t.ClassPrivateProperty\n      | t.ClassPrivateMethod\n    )[] = [];\n    path.get(\"body.body\").forEach(element => {\n      // Static blocks cannot be compiled to \"instance blocks\", but we can inline\n      // them as IIFEs in the next property.\n      if (element.isStaticBlock()) {\n        staticFieldInitializerExpressions.push(staticBlockToIIFE(element.node));\n        element.remove();\n        return;\n      }\n\n      const isProperty =\n        element.isClassProperty() || element.isClassPrivateProperty();\n\n      if (\n        (isProperty || element.isClassPrivateMethod()) &&\n        element.node.static\n      ) {\n        if (isProperty && staticFieldInitializerExpressions.length > 0) {\n          prependExpressionsToFieldInitializer(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      }\n    });\n\n    if (statics.length > 0 || staticFieldInitializerExpressions.length > 0) {\n      const staticsClass = template.expression.ast`\n        class extends ${state.addHelper(\"identity\")} {}\n      ` as t.ClassExpression;\n      staticsClass.body.body = [\n        t.staticBlock([\n          t.toStatement(originalClass, true) ||\n            // If toStatement returns false, originalClass must be an anonymous ClassExpression,\n            // because `export default @dec ...` has been handled in the export visitor before.\n            t.expressionStatement(originalClass as t.ClassExpression),\n        ]),\n        ...statics,\n      ];\n\n      const constructorBody: t.Expression[] = [];\n\n      const newExpr = t.newExpression(staticsClass, []);\n\n      if (staticFieldInitializerExpressions.length > 0) {\n        constructorBody.push(...staticFieldInitializerExpressions);\n      }\n      if (classInitCall) {\n        classInitInjected = true;\n        constructorBody.push(classInitCall);\n      }\n      if (constructorBody.length > 0) {\n        constructorBody.unshift(\n          t.callExpression(t.super(), [t.cloneNode(classIdLocal)]),\n        );\n\n        // set isDerivedClass to false as we have already prepended super call\n        staticsClass.body.body.push(\n          createConstructorFromExpressions(\n            constructorBody,\n            /* isDerivedClass */ false,\n          ),\n        );\n      } else {\n        newExpr.arguments.push(t.cloneNode(classIdLocal));\n      }\n\n      path.replaceWith(newExpr);\n    }\n  }\n  if (!classInitInjected && classInitCall) {\n    path.node.body.body.push(\n      t.staticBlock([t.expressionStatement(classInitCall)]),\n    );\n  }\n\n  let { superClass } = originalClass;\n  if (\n    superClass &&\n    (process.env.BABEL_8_BREAKING ||\n      version === \"2023-11\" ||\n      version === \"2023-05\")\n  ) {\n    const id = path.scope.maybeGenerateMemoised(superClass);\n    if (id) {\n      originalClass.superClass = t.assignmentExpression(\"=\", id, superClass);\n      superClass = id;\n    }\n  }\n  originalClass.body.body.unshift(\n    t.staticBlock(\n      [\n        t.expressionStatement(\n          createLocalsAssignment(\n            elementLocals,\n            classLocals,\n            elementDecorations,\n            classDecorationsId ?? t.arrayExpression(classDecorations),\n            t.numericLiteral(classDecorationsFlag),\n            needsInstancePrivateBrandCheck ? lastInstancePrivateName : null,\n            typeof className === \"object\" ? className : undefined,\n            t.cloneNode(superClass),\n            state,\n            version,\n          ),\n        ),\n        staticInitLocal &&\n          t.expressionStatement(\n            t.callExpression(t.cloneNode(staticInitLocal), [\n              t.thisExpression(),\n            ]),\n          ),\n      ].filter(Boolean),\n    ),\n  );\n\n  // When path is a ClassExpression, path.insertBefore will convert `path`\n  // into a SequenceExpression\n  path.insertBefore(assignments.map(expr => t.expressionStatement(expr)));\n\n  if (needsDeclaraionForClassBinding) {\n    path.insertBefore(\n      t.variableDeclaration(\"let\", [\n        t.variableDeclarator(t.cloneNode(classIdLocal)),\n      ]),\n    );\n  }\n\n  if (decoratedPrivateMethods.size > 0) {\n    checkPrivateMethodUpdateError(path, decoratedPrivateMethods);\n  }\n\n  // Recrawl the scope to make sure new identifiers are properly synced\n  path.scope.crawl();\n\n  return path;\n}\n\nfunction createLocalsAssignment(\n  elementLocals: t.Identifier[],\n  classLocals: t.Identifier[],\n  elementDecorations: t.ArrayExpression | t.Identifier,\n  classDecorations: t.ArrayExpression | t.Identifier,\n  classDecorationsFlag: t.NumericLiteral,\n  maybePrivateBrandName: t.PrivateName | null,\n  setClassName: t.Identifier | t.StringLiteral | undefined,\n  superClass: null | t.Expression,\n  state: PluginPass,\n  version: DecoratorVersionKind,\n) {\n  let lhs, rhs;\n  const args: t.Expression[] = [\n    setClassName\n      ? createSetFunctionNameCall(state, setClassName)\n      : t.thisExpression(),\n    classDecorations,\n    elementDecorations,\n  ];\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (version !== \"2023-11\") {\n      args.splice(1, 2, elementDecorations, classDecorations);\n    }\n    if (\n      version === \"2021-12\" ||\n      (version === \"2022-03\" && !state.availableHelper(\"applyDecs2203R\"))\n    ) {\n      lhs = t.arrayPattern([...elementLocals, ...classLocals]);\n      rhs = t.callExpression(\n        state.addHelper(version === \"2021-12\" ? \"applyDecs\" : \"applyDecs2203\"),\n        args,\n      );\n      return t.assignmentExpression(\"=\", lhs, rhs);\n    } else if (version === \"2022-03\") {\n      rhs = t.callExpression(state.addHelper(\"applyDecs2203R\"), args);\n    } else if (version === \"2023-01\") {\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      }\n      rhs = t.callExpression(state.addHelper(\"applyDecs2301\"), args);\n    } else if (version === \"2023-05\") {\n      if (\n        maybePrivateBrandName ||\n        superClass ||\n        classDecorationsFlag.value !== 0\n      ) {\n        args.push(classDecorationsFlag);\n      }\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      } else if (superClass) {\n        args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n      }\n      if (superClass) args.push(superClass);\n      rhs = t.callExpression(state.addHelper(\"applyDecs2305\"), args);\n    }\n  }\n  if (process.env.BABEL_8_BREAKING || version === \"2023-11\") {\n    if (\n      maybePrivateBrandName ||\n      superClass ||\n      classDecorationsFlag.value !== 0\n    ) {\n      args.push(classDecorationsFlag);\n    }\n    if (maybePrivateBrandName) {\n      args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n    } else if (superClass) {\n      args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n    }\n    if (superClass) args.push(superClass);\n    rhs = t.callExpression(state.addHelper(\"applyDecs2311\"), args);\n  }\n\n  // optimize `{ c: [classLocals] } = applyDecsHelper(...)` to\n  // `[classLocals] = applyDecsHelper(...).c`\n  if (elementLocals.length > 0) {\n    if (classLocals.length > 0) {\n      lhs = t.objectPattern([\n        t.objectProperty(t.identifier(\"e\"), t.arrayPattern(elementLocals)),\n        t.objectProperty(t.identifier(\"c\"), t.arrayPattern(classLocals)),\n      ]);\n    } else {\n      lhs = t.arrayPattern(elementLocals);\n      rhs = t.memberExpression(rhs, t.identifier(\"e\"), false, false);\n    }\n  } else {\n    // invariant: classLocals.length > 0\n    lhs = t.arrayPattern(classLocals);\n    rhs = t.memberExpression(rhs, t.identifier(\"c\"), false, false);\n  }\n\n  return t.assignmentExpression(\"=\", lhs, rhs);\n}\n\nfunction isProtoKey(\n  node: t.Identifier | t.StringLiteral | t.BigIntLiteral | t.NumericLiteral,\n) {\n  return node.type === \"Identifier\"\n    ? node.name === \"__proto__\"\n    : node.value === \"__proto__\";\n}\n\nfunction isDecorated(node: t.Class | ClassDecoratableElement) {\n  return node.decorators && node.decorators.length > 0;\n}\n\nfunction shouldTransformElement(node: ClassElement) {\n  switch (node.type) {\n    case \"ClassAccessorProperty\":\n      return true;\n    case \"ClassMethod\":\n    case \"ClassProperty\":\n    case \"ClassPrivateMethod\":\n    case \"ClassPrivateProperty\":\n      return isDecorated(node);\n    default:\n      return false;\n  }\n}\n\nfunction shouldTransformClass(node: t.Class) {\n  return isDecorated(node) || node.body.body.some(shouldTransformElement);\n}\n\n// Todo: unify name references logic with helper-function-name\nfunction NamedEvaluationVisitoryFactory(\n  isAnonymous: (path: NodePath) => boolean,\n  visitor: (\n    path: NodePath,\n    state: PluginPass,\n    name:\n      | string\n      | t.Identifier\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral,\n  ) => void,\n) {\n  function handleComputedProperty(\n    propertyPath: NodePath<\n      t.ObjectProperty | t.ClassProperty | t.ClassAccessorProperty\n    >,\n    key: t.Expression,\n    state: PluginPass,\n  ): t.StringLiteral | t.Identifier {\n    switch (key.type) {\n      case \"StringLiteral\":\n        return t.stringLiteral(key.value);\n      case \"NumericLiteral\":\n      case \"BigIntLiteral\": {\n        const keyValue = key.value + \"\";\n        propertyPath.get(\"key\").replaceWith(t.stringLiteral(keyValue));\n        return t.stringLiteral(keyValue);\n      }\n      default: {\n        const ref = propertyPath.scope.maybeGenerateMemoised(key);\n        propertyPath\n          .get(\"key\")\n          .replaceWith(\n            t.assignmentExpression(\n              \"=\",\n              ref,\n              createToPropertyKeyCall(state, key),\n            ),\n          );\n        return t.cloneNode(ref);\n      }\n    }\n  }\n  return {\n    VariableDeclarator(path, state) {\n      const id = path.node.id;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"init\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    AssignmentExpression(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          switch (path.node.operator) {\n            case \"=\":\n            case \"&&=\":\n            case \"||=\":\n            case \"??=\":\n              visitor(initializer, state, id.name);\n          }\n        }\n      }\n    },\n    AssignmentPattern(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    // We listen on ObjectExpression so that we don't have to visit\n    // the object properties under object patterns\n    ObjectExpression(path, state) {\n      for (const propertyPath of path.get(\"properties\")) {\n        const { node } = propertyPath;\n        if (node.type !== \"ObjectProperty\") continue;\n        const id = node.key;\n        const initializer = skipTransparentExprWrappers(\n          propertyPath.get(\"value\"),\n        );\n        if (isAnonymous(initializer)) {\n          if (!node.computed) {\n            // ******** RS: PropertyDefinitionEvaluation\n            if (!isProtoKey(id as t.StringLiteral | t.Identifier)) {\n              if (id.type === \"Identifier\") {\n                visitor(initializer, state, id.name);\n              } else {\n                const className = t.stringLiteral(\n                  (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                    .value + \"\",\n                );\n                visitor(initializer, state, className);\n              }\n            }\n          } else {\n            const ref = handleComputedProperty(\n              propertyPath as NodePath<t.ObjectProperty>,\n              // The key of a computed object property must not be a private name\n              id as t.Expression,\n              state,\n            );\n            visitor(initializer, state, ref);\n          }\n        }\n      }\n    },\n    ClassPrivateProperty(path, state) {\n      const { node } = path;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        const className = t.stringLiteral(\"#\" + node.key.id.name);\n        visitor(initializer, state, className);\n      }\n    },\n    ClassAccessorProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else if (id.type === \"PrivateName\") {\n            const className = t.stringLiteral(\"#\" + id.id.name);\n            visitor(initializer, state, className);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(\n            path,\n            // The key of a computed accessor property must not be a private name\n            id as t.Expression,\n            state,\n          );\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n    ClassProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(path, id, state);\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n  } satisfies Visitor<PluginPass>;\n}\n\nfunction isDecoratedAnonymousClassExpression(path: NodePath) {\n  return (\n    path.isClassExpression({ id: null }) && shouldTransformClass(path.node)\n  );\n}\n\nexport default function (\n  { assertVersion, assumption }: PluginAPI,\n  { loose }: Options,\n  version: DecoratorVersionKind,\n  inherits: PluginObject[\"inherits\"],\n): PluginObject {\n  if (process.env.BABEL_8_BREAKING) {\n    assertVersion(process.env.IS_PUBLISH ? PACKAGE_JSON.version : \"^7.21.0\");\n  } else {\n    if (\n      version === \"2023-11\" ||\n      version === \"2023-05\" ||\n      version === \"2023-01\"\n    ) {\n      assertVersion(\"^7.21.0\");\n    } else if (version === \"2021-12\") {\n      assertVersion(\"^7.16.0\");\n    } else {\n      assertVersion(\"^7.19.0\");\n    }\n  }\n\n  const VISITED = new WeakSet<NodePath>();\n  const constantSuper = assumption(\"constantSuper\") ?? loose;\n\n  const namedEvaluationVisitor: Visitor<PluginPass> =\n    NamedEvaluationVisitoryFactory(\n      isDecoratedAnonymousClassExpression,\n      visitClass,\n    );\n\n  function visitClass(\n    path: NodePath<t.Class>,\n    state: PluginPass,\n    className: string | t.Identifier | t.StringLiteral | undefined,\n  ) {\n    if (VISITED.has(path)) return;\n    const { node } = path;\n    className ??= node.id?.name;\n    const newPath = transformClass(\n      path,\n      state,\n      constantSuper,\n      version,\n      className,\n      namedEvaluationVisitor,\n    );\n    if (newPath) {\n      VISITED.add(newPath);\n      return;\n    }\n    VISITED.add(path);\n  }\n\n  return {\n    name: \"proposal-decorators\",\n    inherits: inherits,\n\n    visitor: {\n      ExportDefaultDeclaration(path, state) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          const isAnonymous = !declaration.id;\n          const updatedVarDeclarationPath = splitExportDeclaration(\n            path,\n          ) as unknown as NodePath<t.ClassDeclaration>;\n          if (isAnonymous) {\n            visitClass(\n              updatedVarDeclarationPath,\n              state,\n              t.stringLiteral(\"default\"),\n            );\n          }\n        }\n      },\n      ExportNamedDeclaration(path) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          splitExportDeclaration(path);\n        }\n      },\n\n      Class(path, state) {\n        visitClass(path, state, undefined);\n      },\n\n      ...namedEvaluationVisitor,\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,6BAAA,GAAAF,OAAA;AAGA,IAAAG,wCAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AA+BA,SAASK,WAAWA,CAACC,EAAY,EAAEC,GAAG,GAAGD,EAAE,CAACE,MAAM,GAAG,CAAC,EAAQ;EAE5D,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE;IACdD,EAAE,CAACG,OAAO,GAAqB,CAAC;IAChC;EACF;EAEA,MAAMC,OAAO,GAAGJ,EAAE,CAACC,GAAG,CAAC;EAEvB,IAAIG,OAAO,OAAyB,EAAE;IAEpCJ,EAAE,CAACC,GAAG,CAAC,KAAuB;EAChC,CAAC,MAAM,IAAIG,OAAO,QAAyB,EAAE;IAE3CJ,EAAE,CAACC,GAAG,CAAC,KAAuB;IAC9BF,WAAW,CAACC,EAAE,EAAEC,GAAG,GAAG,CAAC,CAAC;EAC1B,CAAC,MAAM;IAELD,EAAE,CAACC,GAAG,CAAC,GAAGG,OAAO,GAAG,CAAC;EACvB;AACF;AASA,SAASC,iCAAiCA,CACxCC,SAA2D,EACtC;EACrB,MAAMC,gBAA0B,GAAG,EAAE;EACrC,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;EAEtCH,SAAS,CAACI,QAAQ,CAAC;IACjBC,WAAWA,CAACC,IAAI,EAAE;MAChBJ,YAAY,CAACK,GAAG,CAACD,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,OAAO,MAAqB;IAC1B,IAAIC,SAAS;IACb,GAAG;MACDjB,WAAW,CAACQ,gBAAgB,CAAC;MAC7BS,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAGX,gBAAgB,CAAC;IACtD,CAAC,QAAQC,YAAY,CAACW,GAAG,CAACH,SAAS,CAAC;IAEpC,OAAOI,WAAC,CAACC,WAAW,CAACD,WAAC,CAACE,UAAU,CAACN,SAAS,CAAC,CAAC;EAC/C,CAAC;AACH;AAQA,SAASO,qCAAqCA,CAC5CjB,SAA2D,EACtC;EACrB,IAAIkB,SAA8B;EAElC,OAAO,MAAqB;IAC1B,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAGnB,iCAAiC,CAACC,SAAS,CAAC;IAC1D;IAEA,OAAOkB,SAAS,CAAC,CAAC;EACpB,CAAC;AACH;AAUA,SAASC,mBAAmBA,CAC1Bb,IAAsD,EACtDc,SAA8D,EAI9D;EACA,IAAId,IAAI,CAACe,IAAI,KAAK,kBAAkB,EAAE;IACpC,MAAM3B,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACd,EAAE;IACvB,MAAM0B,SAAS,GAAG1B,EAAE,CAACe,IAAI;IACzB,MAAMa,KAAK,GAAGhB,IAAI,CAACiB,KAAK,CAACC,gCAAgC,CAAC9B,EAAE,CAAC;IAC7D,MAAM+B,OAAO,GAAGX,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC;IAEvCd,IAAI,CAACiB,KAAK,CAACG,MAAM,CAACN,SAAS,EAAEE,KAAK,CAACb,IAAI,CAAC;IAExCH,IAAI,CAACqB,GAAG,CAAC,IAAI,CAAC,CAACC,WAAW,CAACH,OAAO,CAAC;IAEnC,OAAO;MAAE/B,EAAE,EAAEoB,WAAC,CAACe,SAAS,CAACP,KAAK,CAAC;MAAEhB;IAAK,CAAC;EACzC,CAAC,MAAM;IACL,IAAIgB,KAAmB;IAEvB,IAAIhB,IAAI,CAACE,IAAI,CAACd,EAAE,EAAE;MAChB0B,SAAS,GAAGd,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI;MAC7Ba,KAAK,GAAGhB,IAAI,CAACiB,KAAK,CAACO,MAAM,CAACC,6BAA6B,CAACX,SAAS,CAAC;MAClEd,IAAI,CAACiB,KAAK,CAACG,MAAM,CAACN,SAAS,EAAEE,KAAK,CAACb,IAAI,CAAC;IAC1C,CAAC,MAAM;MACLa,KAAK,GAAGhB,IAAI,CAACiB,KAAK,CAACO,MAAM,CAACC,6BAA6B,CACrD,OAAOX,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,iBAC9C,CAAC;IACH;IAEA,MAAMY,YAAY,GAAGlB,WAAC,CAACmB,eAAe,CACpC,OAAOb,SAAS,KAAK,QAAQ,GAAGN,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC,GAAG,IAAI,EAC9Dd,IAAI,CAACE,IAAI,CAAC0B,UAAU,EACpB5B,IAAI,CAACE,IAAI,CAAC2B,IACZ,CAAC;IAED,MAAM,CAACC,OAAO,CAAC,GAAG9B,IAAI,CAACsB,WAAW,CAChCd,WAAC,CAACuB,kBAAkB,CAAC,CAACL,YAAY,EAAEV,KAAK,CAAC,CAC5C,CAAC;IAED,OAAO;MACL5B,EAAE,EAAEoB,WAAC,CAACe,SAAS,CAACP,KAAK,CAAC;MACtBhB,IAAI,EAAE8B,OAAO,CAACT,GAAG,CAAC,eAAe;IACnC,CAAC;EACH;AACF;AAEA,SAASW,qBAAqBA,CAC5BC,GAAiC,EACjCC,KAA+B,EAC/BC,QAAiB,EACyB;EAC1C,IAAIF,GAAG,CAAClB,IAAI,KAAK,aAAa,EAAE;IAC9B,OAAOP,WAAC,CAAC4B,oBAAoB,CAACH,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEF,QAAQ,CAAC;EAChE,CAAC,MAAM;IACL,OAAO3B,WAAC,CAAC8B,aAAa,CAACL,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEA,SAAS,EAAEF,QAAQ,CAAC;EACpE;AACF;AAEA,SAASI,oBAAoBA,CAC3BzB,SAAuB,EACvB0B,OAA0C,EAC1CC,WAAyC,EACzCC,SAAwB,EACxBC,OAA6B,EAC7BC,UAAmB,EACnBT,QAAiB,EACX;EACN,MAAMU,OAAO,GACX,CAACF,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDR,QAAQ,GACJrB,SAAS,GACTN,WAAC,CAACsC,cAAc,CAAC,CAAC;EAExB,MAAMC,UAAU,GAAGvC,WAAC,CAACwC,cAAc,CAAC,CAClCxC,WAAC,CAACyC,eAAe,CACfzC,WAAC,CAAC0C,gBAAgB,CAAC1C,WAAC,CAACe,SAAS,CAACsB,OAAO,CAAC,EAAErC,WAAC,CAACe,SAAS,CAACmB,SAAS,CAAC,CACjE,CAAC,CACF,CAAC;EAEF,MAAMS,UAAU,GAAG3C,WAAC,CAACwC,cAAc,CAAC,CAClCxC,WAAC,CAAC4C,mBAAmB,CACnB5C,WAAC,CAAC6C,oBAAoB,CACpB,GAAG,EACH7C,WAAC,CAAC0C,gBAAgB,CAAC1C,WAAC,CAACe,SAAS,CAACsB,OAAO,CAAC,EAAErC,WAAC,CAACe,SAAS,CAACmB,SAAS,CAAC,CAAC,EAChElC,WAAC,CAACE,UAAU,CAAC,GAAG,CAClB,CACF,CAAC,CACF,CAAC;EAEF,IAAI4C,MAA4C,EAC9CC,MAA4C;EAE9C,IAAId,WAAW,CAAC1B,IAAI,KAAK,aAAa,EAAE;IACtCuC,MAAM,GAAG9C,WAAC,CAACgD,kBAAkB,CAC3B,KAAK,EACLhD,WAAC,CAACe,SAAS,CAACkB,WAAW,CAAC,EACxB,EAAE,EACFM,UAAU,EACVZ,QACF,CAAC;IACDoB,MAAM,GAAG/C,WAAC,CAACgD,kBAAkB,CAC3B,KAAK,EACLhD,WAAC,CAACe,SAAS,CAACkB,WAAW,CAAC,EACxB,CAACjC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnByC,UAAU,EACVhB,QACF,CAAC;EACH,CAAC,MAAM;IACLmB,MAAM,GAAG9C,WAAC,CAACiD,WAAW,CACpB,KAAK,EACLjD,WAAC,CAACe,SAAS,CAACkB,WAAW,CAAC,EACxB,EAAE,EACFM,UAAU,EACVH,UAAU,EACVT,QACF,CAAC;IACDoB,MAAM,GAAG/C,WAAC,CAACiD,WAAW,CACpB,KAAK,EACLjD,WAAC,CAACe,SAAS,CAACkB,WAAW,CAAC,EACxB,CAACjC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnByC,UAAU,EACVP,UAAU,EACVT,QACF,CAAC;EACH;EAEAK,OAAO,CAACkB,WAAW,CAACH,MAAM,CAAC;EAC3Bf,OAAO,CAACkB,WAAW,CAACJ,MAAM,CAAC;AAC7B;AAEA,SAASK,wBAAwBA,CAC/BjB,SAAwB,EACxBC,OAA6B,EACyB;EACtD,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,EAAE;IAC3E,OAAO,CACLiB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC9B;AACA,wBAAwBtD,WAAC,CAACe,SAAS,CAACmB,SAAS,CAAE;AAC/C;AACA,OAAO,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC9B;AACA,iBAAiBtD,WAAC,CAACe,SAAS,CAACmB,SAAS,CAAE;AACxC;AACA,OAAO,CACF;EACH;EACA,OAAO,CACLkB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC5B,eAAetD,WAAC,CAACe,SAAS,CAACmB,SAAS,CAAE;AACtC,KAAK,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC5B,oBAAoBtD,WAAC,CAACe,SAAS,CAACmB,SAAS,CAAE;AAC3C,KAAK,CACF;AACH;AAWA,SAASqB,oCAAoCA,CAC3CC,WAA2B,EAC3BC,SAEC,EACD;EACA,MAAMC,WAAW,GAAGD,SAAS,CAAC5C,GAAG,CAAC,OAAO,CAAC;EAC1C,IAAI6C,WAAW,CAAChE,IAAI,EAAE;IACpB8D,WAAW,CAACG,IAAI,CAACD,WAAW,CAAChE,IAAI,CAAC;EACpC,CAAC,MAAM,IAAI8D,WAAW,CAAC1E,MAAM,GAAG,CAAC,EAAE;IACjC0E,WAAW,CAACA,WAAW,CAAC1E,MAAM,GAAG,CAAC,CAAC,GAAGkB,WAAC,CAAC4D,eAAe,CACrD,MAAM,EACNJ,WAAW,CAACA,WAAW,CAAC1E,MAAM,GAAG,CAAC,CACpC,CAAC;EACH;EACA4E,WAAW,CAAC5C,WAAW,CAAC+C,uBAAuB,CAACL,WAAW,CAAC,CAAC;AAC/D;AAEA,SAASM,+BAA+BA,CACtCN,WAA2B,EAC3BO,SAAkC,EAClC;EACAA,SAAS,CAACC,gBAAgB,CACxB,MAAM,EACNhE,WAAC,CAAC4C,mBAAmB,CAACiB,uBAAuB,CAACL,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAASS,+BAA+BA,CACtCT,WAA2B,EAC3BU,eAAwC,EACxC;EACAA,eAAe,CAACxE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACpCiB,WAAC,CAAC4C,mBAAmB,CAACiB,uBAAuB,CAACL,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAASW,yBAAyBA,CAChCd,UAAwB,EACxBe,aAA2B,EAC3B;EACA,OACEpE,WAAC,CAACqE,gBAAgB,CAAChB,UAAU,CAAC,IAC9BrD,WAAC,CAACsE,YAAY,CAACjB,UAAU,CAACkB,MAAM,EAAE;IAAE5E,IAAI,EAAEyE,aAAa,CAACzE;EAAK,CAAC,CAAC;AAEnE;AASA,SAAS6E,+BAA+BA,CACtChB,WAA2B,EAC3BiB,cAA4B,EAC5B;EAEA,IACEjB,WAAW,CAAC1E,MAAM,IAAI,CAAC,IACvBqF,yBAAyB,CAACX,WAAW,CAAC,CAAC,CAAC,EAAEiB,cAAc,CAAC,EACzD;IACA,MAAMC,eAAe,GAAG1E,WAAC,CAAC2E,cAAc,CAAC3E,WAAC,CAACe,SAAS,CAAC0D,cAAc,CAAC,EAAE,CACpEjB,WAAW,CAAC,CAAC,CAAC,CACf,CAAC;IACFA,WAAW,CAACoB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEF,eAAe,CAAC;EAC3C;EAEA,IACElB,WAAW,CAAC1E,MAAM,IAAI,CAAC,IACvBkB,WAAC,CAAC6E,gBAAgB,CAACrB,WAAW,CAACA,WAAW,CAAC1E,MAAM,GAAG,CAAC,CAAC,CAAC,IACvDqF,yBAAyB,CACvBX,WAAW,CAACA,WAAW,CAAC1E,MAAM,GAAG,CAAC,CAAC,EACnC2F,cACF,CAAC,EACD;IACAjB,WAAW,CAACoB,MAAM,CAACpB,WAAW,CAAC1E,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;EAC/C;EACA,OAAO+E,uBAAuB,CAACL,WAAW,CAAC;AAC7C;AAWA,SAASsB,0CAA0CA,CACjDtB,WAA2B,EAC3BU,eAAwC,EACxCO,cAA4B,EAC5B;EACAP,eAAe,CAAC5E,QAAQ,CAAC;IACvByF,cAAc,EAAE;MACdC,IAAIA,CAACxF,IAAI,EAAE;QACT,IAAI,CAACA,IAAI,CAACqB,GAAG,CAAC,QAAQ,CAAC,CAACoE,OAAO,CAAC,CAAC,EAAE;QACnC,MAAMC,QAAQ,GAAG,CACf1F,IAAI,CAACE,IAAI,EACT,GAAG8D,WAAW,CAAC2B,GAAG,CAACC,IAAI,IAAIpF,WAAC,CAACe,SAAS,CAACqE,IAAI,CAAC,CAAC,CAC9C;QAED,IAAI5F,IAAI,CAAC6F,kBAAkB,CAAC,CAAC,EAAE;UAC7BH,QAAQ,CAACvB,IAAI,CAAC3D,WAAC,CAACsC,cAAc,CAAC,CAAC,CAAC;QACnC;QACA9C,IAAI,CAACsB,WAAW,CACd0D,+BAA+B,CAACU,QAAQ,EAAET,cAAc,CAC1D,CAAC;QAEDjF,IAAI,CAAC8F,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IACDC,WAAWA,CAAC/F,IAAI,EAAE;MAChB,IAAIA,IAAI,CAACE,IAAI,CAAC8F,IAAI,KAAK,aAAa,EAAE;QACpChG,IAAI,CAAC8F,IAAI,CAAC,CAAC;MACb;IACF;EACF,CAAC,CAAC;AACJ;AAWA,SAASG,gCAAgCA,CACvCjC,WAA2B,EAC3BkC,cAAuB,EACvB;EACA,MAAMrE,IAAmB,GAAG,CAC1BrB,WAAC,CAAC4C,mBAAmB,CAACiB,uBAAuB,CAACL,WAAW,CAAC,CAAC,CAC5D;EACD,IAAIkC,cAAc,EAAE;IAClBrE,IAAI,CAACtC,OAAO,CACViB,WAAC,CAAC4C,mBAAmB,CACnB5C,WAAC,CAAC2E,cAAc,CAAC3E,WAAC,CAAC2F,KAAK,CAAC,CAAC,EAAE,CAAC3F,WAAC,CAAC4F,aAAa,CAAC5F,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CACrE,CACF,CAAC;EACH;EACA,OAAOF,WAAC,CAACiD,WAAW,CAClB,aAAa,EACbjD,WAAC,CAACE,UAAU,CAAC,aAAa,CAAC,EAC3BwF,cAAc,GAAG,CAAC1F,WAAC,CAAC6F,WAAW,CAAC7F,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAC3DF,WAAC,CAACwC,cAAc,CAACnB,IAAI,CACvB,CAAC;AACH;AAEA,SAASyE,gCAAgCA,CAACtC,WAA2B,EAAE;EACrE,OAAOxD,WAAC,CAAC+F,WAAW,CAAC,CACnB/F,WAAC,CAAC4C,mBAAmB,CAACiB,uBAAuB,CAACL,WAAW,CAAC,CAAC,CAC5D,CAAC;AACJ;AAGA,MAAMwC,KAAK,GAAG,CAAC;AACf,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAEhB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,oBAAoB,GAAG,EAAE;AAE/B,SAASC,cAAcA,CAACxE,OAA0C,EAAU;EAC1E,QAAQA,OAAO,CAACtC,IAAI,CAACa,IAAI;IACvB,KAAK,eAAe;IACpB,KAAK,sBAAsB;MACzB,OAAOyF,KAAK;IACd,KAAK,uBAAuB;MAC1B,OAAOC,QAAQ;IACjB,KAAK,aAAa;IAClB,KAAK,oBAAoB;MACvB,IAAIjE,OAAO,CAACtC,IAAI,CAAC8F,IAAI,KAAK,KAAK,EAAE;QAC/B,OAAOW,MAAM;MACf,CAAC,MAAM,IAAInE,OAAO,CAACtC,IAAI,CAAC8F,IAAI,KAAK,KAAK,EAAE;QACtC,OAAOY,MAAM;MACf,CAAC,MAAM;QACL,OAAOF,MAAM;MACf;EACJ;AACF;AAmCA,SAASO,qBAAqBA,CAACC,IAAqB,EAAmB;EACrE,OAAO,CACL,GAAGA,IAAI,CAACC,MAAM,CACZC,EAAE,IAAIA,EAAE,CAACjF,QAAQ,IAAIiF,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MACzD,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CACZC,EAAE,IAAI,CAACA,EAAE,CAACjF,QAAQ,IAAIiF,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MAC1D,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACjF,QAAQ,IAAIiF,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,EACtD,GAAGU,IAAI,CAACC,MAAM,CAACC,EAAE,IAAI,CAACA,EAAE,CAACjF,QAAQ,IAAIiF,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,CACxD;AACH;AAgBA,SAASa,sBAAsBA,CAC7BC,UAA0B,EAC1BC,cAA4C,EAC5C5E,OAA6B,EACC;EAC9B,MAAM6E,SAAS,GAAGF,UAAU,CAAChI,MAAM;EACnC,MAAMmI,WAAW,GAAGF,cAAc,CAACG,IAAI,CAACC,OAAO,CAAC;EAChD,MAAMC,IAAoB,GAAG,EAAE;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;IAClC,KACGlF,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzD8E,WAAW,EACX;MACAG,IAAI,CAACzD,IAAI,CACPoD,cAAc,CAACM,CAAC,CAAC,IAAIrH,WAAC,CAAC4D,eAAe,CAAC,MAAM,EAAE5D,WAAC,CAACsH,cAAc,CAAC,CAAC,CAAC,CACpE,CAAC;IACH;IACAF,IAAI,CAACzD,IAAI,CAACmD,UAAU,CAACO,CAAC,CAAC,CAAC;EAC1B;EAEA,OAAO;IAAEE,QAAQ,EAAEN,WAAW;IAAEG;EAAK,CAAC;AACxC;AAEA,SAASI,uBAAuBA,CAC9BC,cAA+B,EAC/BtF,OAA6B,EACV;EACnB,OAAOnC,WAAC,CAAC0H,eAAe,CACtBD,cAAc,CAACtC,GAAG,CAACyB,EAAE,IAAI;IACvB,IAAIe,IAAI,GAAGf,EAAE,CAACpB,IAAI;IAClB,IAAIoB,EAAE,CAACjF,QAAQ,EAAE;MACfgG,IAAI,IACFxF,OAAO,KAAK,SAAS,IACaA,OAAO,KAAK,SAAS,GACnDmE,MAAM,GACND,kBAAkB;IAC1B;IACA,IAAIO,EAAE,CAACgB,kBAAkB,EAAED,IAAI,IAAIpB,oBAAoB;IAEvD,OAAOvG,WAAC,CAAC0H,eAAe,CAAC,CACvBd,EAAE,CAACiB,eAAe,EAClB7H,WAAC,CAACsH,cAAc,CAACK,IAAI,CAAC,EACtBf,EAAE,CAACjH,IAAI,EACP,IAAIiH,EAAE,CAACkB,cAAc,IAAI,EAAE,CAAC,CAC7B,CAAC;EACJ,CAAC,CACH,CAAC;AACH;AAEA,SAASC,8BAA8BA,CAACN,cAA+B,EAAE;EACvE,MAAMO,QAAwB,GAAG,EAAE;EAEnC,KAAK,MAAMpB,EAAE,IAAIa,cAAc,EAAE;IAC/B,MAAM;MAAEQ;IAAO,CAAC,GAAGrB,EAAE;IAErB,IAAIsB,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzBD,QAAQ,CAACrE,IAAI,CAAC,GAAGsE,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAIA,MAAM,KAAKpG,SAAS,EAAE;MAC/BmG,QAAQ,CAACrE,IAAI,CAACsE,MAAM,CAAC;IACvB;EACF;EAEA,OAAOD,QAAQ;AACjB;AAEA,SAASI,mBAAmBA,CAC1BjG,OAA6B,EAC7BH,OAAiB,EACjBP,GAAkB,EAClB4G,KAAmB,EACnBC,KAAmB,EACnB3G,QAAiB,EACjB;EACAK,OAAO,CAACkB,WAAW,CACjBlD,WAAC,CAACgD,kBAAkB,CAClB,KAAK,EACLhD,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChB,EAAE,EACFzB,WAAC,CAACwC,cAAc,CAAC,CACfxC,WAAC,CAACyC,eAAe,CACfzC,WAAC,CAAC2E,cAAc,CACd3E,WAAC,CAACe,SAAS,CAACsH,KAAK,CAAC,EACelG,OAAO,KAAK,SAAS,IAAKR,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACsC,cAAc,CAAC,CAAC,CACzB,CACF,CAAC,CACF,CAAC,EACFX,QACF,CACF,CAAC;EAEDK,OAAO,CAACkB,WAAW,CACjBlD,WAAC,CAACgD,kBAAkB,CAClB,KAAK,EACLhD,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChB,CAACzB,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAACwC,cAAc,CAAC,CACfxC,WAAC,CAAC4C,mBAAmB,CACnB5C,WAAC,CAAC2E,cAAc,CACd3E,WAAC,CAACe,SAAS,CAACuH,KAAK,CAAC,EACenG,OAAO,KAAK,SAAS,IAAKR,QAAQ,GAC/D,CAAC3B,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,GACnB,CAACF,WAAC,CAACsC,cAAc,CAAC,CAAC,EAAEtC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAC5C,CACF,CAAC,CACF,CAAC,EACFyB,QACF,CACF,CAAC;AACH;AAEA,SAAS4G,mBAAmBA,CAC1BvG,OAAuC,EACvCP,GAAkB,EAClB+G,cAA4B,EAC5B7G,QAAiB,EACjB;EACA,IAAI8G,MAAwC;EAC5C,IAAIC,KAAoB;EAExB,IAAI1G,OAAO,CAACtC,IAAI,CAAC8F,IAAI,KAAK,KAAK,EAAE;IAC/BiD,MAAM,GAAG,CAACzI,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5BwI,KAAK,GAAG,CACN1I,WAAC,CAAC4C,mBAAmB,CACnB5C,WAAC,CAAC2E,cAAc,CAAC6D,cAAc,EAAE,CAC/BxI,WAAC,CAACsC,cAAc,CAAC,CAAC,EAClBtC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAClB,CACH,CAAC,CACF;EACH,CAAC,MAAM;IACLuI,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,CACN1I,WAAC,CAACyC,eAAe,CAACzC,WAAC,CAAC2E,cAAc,CAAC6D,cAAc,EAAE,CAACxI,WAAC,CAACsC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1E;EACH;EAEAN,OAAO,CAAClB,WAAW,CACjBd,WAAC,CAACgD,kBAAkB,CAClBhB,OAAO,CAACtC,IAAI,CAAC8F,IAAI,EACjBxF,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBgH,MAAM,EACNzI,WAAC,CAACwC,cAAc,CAACkG,KAAK,CAAC,EACvB/G,QACF,CACF,CAAC;AACH;AAEA,SAASgH,6BAA6BA,CACpCnJ,IAA4B,EACe;EAC3C,MAAM;IAAEe;EAAK,CAAC,GAAGf,IAAI;EAErB,OACEe,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,kBAAkB,IAC3BA,IAAI,KAAK,aAAa;AAE1B;AAEA,SAASqI,iBAAiBA,CAACF,KAAoB,EAAE;EAC/C,OAAO1I,WAAC,CAAC2E,cAAc,CACrB3E,WAAC,CAAC6I,uBAAuB,CAAC,EAAE,EAAE7I,WAAC,CAACwC,cAAc,CAACkG,KAAK,CAACrH,IAAI,CAAC,CAAC,EAC3D,EACF,CAAC;AACH;AAEA,SAASwC,uBAAuBA,CAACiF,KAAqB,EAAE;EACtD,IAAIA,KAAK,CAAChK,MAAM,KAAK,CAAC,EAAE,OAAOkB,WAAC,CAAC4D,eAAe,CAAC,MAAM,EAAE5D,WAAC,CAACsH,cAAc,CAAC,CAAC,CAAC,CAAC;EAC7E,IAAIwB,KAAK,CAAChK,MAAM,KAAK,CAAC,EAAE,OAAOgK,KAAK,CAAC,CAAC,CAAC;EACvC,OAAO9I,WAAC,CAACuB,kBAAkB,CAACuH,KAAK,CAAC;AACpC;AASA,SAASC,yCAAyCA,CAACrJ,IAA0B,EAAE;EAC7E,MAAM;IAAE+I,MAAM;IAAEpH,IAAI;IAAEjB,SAAS,EAAE4I,WAAW;IAAEC,KAAK,EAAEC;EAAQ,CAAC,GAAGxJ,IAAI;EACrE,OAAOM,WAAC,CAACmJ,kBAAkB,CACzBtH,SAAS,EAET4G,MAAM,EACNpH,IAAI,EACJ2H,WAAW,EACXE,OACF,CAAC;AACH;AAEA,SAASE,yBAAyBA,CAChCC,KAAiB,EACjB/I,SAAyC,EACzC;EACA,OAAON,WAAC,CAAC2E,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAC1DtJ,WAAC,CAACsC,cAAc,CAAC,CAAC,EAClBhC,SAAS,CACV,CAAC;AACJ;AAEA,SAASiJ,uBAAuBA,CAACF,KAAiB,EAAEG,WAAyB,EAAE;EAC7E,OAAOxJ,WAAC,CAAC2E,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CAACE,WAAW,CAAC,CAAC;AAC1E;AAEA,SAASC,8BAA8BA,CAACC,SAAwB,EAAE;EAChE,OAAO1J,WAAC,CAAC6I,uBAAuB,CAC9B,CAAC7I,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAAC2J,gBAAgB,CAAC,IAAI,EAAE3J,WAAC,CAACe,SAAS,CAAC2I,SAAS,CAAC,EAAE1J,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CACpE,CAAC;AACH;AAKA,SAAS0J,mBAAmBA,CAACvG,UAAkB,EAAE;EAC/C,IAAI;IACFrD,WAAC,CAAC6J,YAAY,CAACxG,UAAU,EAAE3D,IAAI,IAAI;MACjC,IACEM,WAAC,CAAC6E,gBAAgB,CAACnF,IAAI,CAAC,IACxBM,WAAC,CAACiF,OAAO,CAACvF,IAAI,CAAC,IACfM,WAAC,CAACsE,YAAY,CAAC5E,IAAI,EAAE;QAAEC,IAAI,EAAE;MAAY,CAAC,CAAC,IAC1CK,WAAC,CAAC8J,cAAc,CAACpK,IAAI,CAAC,IAAIA,IAAI,CAACqK,IAAI,CAACpK,IAAI,KAAK,QAAS,EACvD;QAEA,MAAM,IAAI;MACZ;IACF,CAAC,CAAC;IACF,OAAO,KAAK;EACd,CAAC,CAAC,OAAAqK,OAAA,EAAM;IACN,OAAO,IAAI;EACb;AACF;AAEA,SAASC,gBAAgBA,CAAC5G,UAAkB,EAAE;EAC5C,IAAI;IACFrD,WAAC,CAAC6J,YAAY,CAACxG,UAAU,EAAE3D,IAAI,IAAI;MACjC,IAAIM,WAAC,CAACkK,aAAa,CAACxK,IAAI,CAAC,EAAE;QAEzB,MAAM,IAAI;MACZ;IACF,CAAC,CAAC;IACF,OAAO,KAAK;EACd,CAAC,CAAC,OAAAyK,QAAA,EAAM;IACN,OAAO,IAAI;EACb;AACF;AAEA,SAASC,6BAA6BA,CACpC5K,IAAuB,EACvB6K,uBAAoC,EACpC;EACA,MAAMC,kBAAkB,GAAG,IAAAC,iCAAyB,EAGlD;IACAhL,WAAWA,CAACC,IAAI,EAAE6J,KAAK,EAAE;MACvB,IAAI,CAACA,KAAK,CAACmB,eAAe,CAACzK,GAAG,CAACP,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC,EAAE;MAEnD,MAAM8K,UAAU,GAAGjL,IAAI,CAACiL,UAAU;MAClC,MAAMC,gBAAgB,GAAGD,UAAU,CAACA,UAAU;MAE9C,IAEGC,gBAAgB,CAAChL,IAAI,CAACa,IAAI,KAAK,sBAAsB,IACpDmK,gBAAgB,CAAChL,IAAI,CAACiL,IAAI,KAAKF,UAAU,CAAC/K,IAAI,IAEhDgL,gBAAgB,CAAChL,IAAI,CAACa,IAAI,KAAK,kBAAkB,IAEjDmK,gBAAgB,CAAChL,IAAI,CAACa,IAAI,KAAK,aAAa,IAE5CmK,gBAAgB,CAAChL,IAAI,CAACa,IAAI,KAAK,cAAc,IAE5CmK,gBAAgB,CAAChL,IAAI,CAACa,IAAI,KAAK,gBAAgB,IAC9CmK,gBAAgB,CAAChL,IAAI,CAACgC,KAAK,KAAK+I,UAAU,CAAC/K,IAAI,IAC/CgL,gBAAgB,CAACD,UAAU,CAAClK,IAAI,KAAK,eAAgB,IAEtDmK,gBAAgB,CAAChL,IAAI,CAACa,IAAI,KAAK,gBAAgB,IAC9CmK,gBAAgB,CAAChL,IAAI,CAACiL,IAAI,KAAKF,UAAU,CAAC/K,IAAK,EACjD;QACA,MAAMF,IAAI,CAACoL,mBAAmB,CAC3B,kDAAiDpL,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAK,mCACtE,CAAC;MACH;IACF;EACF,CAAC,CAAC;EACF,MAAM6K,eAAe,GAAG,IAAIK,GAAG,CAAe,CAAC;EAC/C,KAAK,MAAMlL,IAAI,IAAI0K,uBAAuB,EAAE;IAC1CG,eAAe,CAACM,GAAG,CAACnL,IAAI,EAAE,IAAI,CAAC;EACjC;EACAH,IAAI,CAACF,QAAQ,CAACgL,kBAAkB,EAAE;IAChCE,eAAe,EAAEA;EACnB,CAAC,CAAC;AACJ;AAEA,SAASO,cAAcA,CACrBvL,IAAuB,EACvB6J,KAAiB,EACjB2B,aAAsB,EACtB7I,OAA6B,EAC7B7B,SAA8D,EAC9D2K,eAAoC,EAC1B;EAAA,IAAAC,mBAAA;EACV,MAAM7J,IAAI,GAAG7B,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC;EAElC,MAAMsK,eAAe,GAAG3L,IAAI,CAACE,IAAI,CAACoH,UAAU;EAC5C,IAAIsE,oBAAoB,GAAG,KAAK;EAChC,IAAIC,0BAA0B,GAAG,KAAK;EACtC,IAAIC,oBAAoB,GAAG,KAAK;EAEhC,MAAMC,uBAAuB,GAAGpL,qCAAqC,CAACX,IAAI,CAAC;EAE3E,MAAMgM,WAAqC,GAAG,EAAE;EAChD,MAAMC,WAAkB,GAAGjM,IAAI,CAACiB,KAAK,CAACO,MAAM;EAC5C,MAAM0K,iBAAiB,GAAGA,CAACrI,UAAwB,EAAEsI,IAAY,KAAK;IACpE,MAAMC,gBAAgB,GAAGH,WAAW,CAACxK,6BAA6B,CAAC0K,IAAI,CAAC;IACxEH,WAAW,CAAC7H,IAAI,CAAC3D,WAAC,CAAC6C,oBAAoB,CAAC,GAAG,EAAE+I,gBAAgB,EAAEvI,UAAU,CAAC,CAAC;IAC3E,OAAOrD,WAAC,CAACe,SAAS,CAAC6K,gBAAgB,CAAC;EACtC,CAAC;EAED,IAAInH,cAA4B;EAChC,IAAIoH,eAA6B;EAIjC,KAAK,MAAM7J,OAAO,IAAIX,IAAI,EAAE;IAC1B,IAAI,CAACsH,6BAA6B,CAAC3G,OAAO,CAAC,EAAE;MAC3C;IACF;IAEA,IAAI8J,WAAW,CAAC9J,OAAO,CAACtC,IAAI,CAAC,EAAE;MAC7B,QAAQsC,OAAO,CAACtC,IAAI,CAACa,IAAI;QACvB,KAAK,eAAe;UAElB0K,eAAe,CAACc,aAAa,CAC3B/J,OAAO,EACPqH,KACF,CAAC;UACD;QACF,KAAK,sBAAsB;UAEzB4B,eAAe,CAACe,oBAAoB,CAClChK,OAAO,EACPqH,KACF,CAAC;UACD;QACF,KAAK,uBAAuB;UAE1B4B,eAAe,CAACgB,qBAAqB,CACnCjK,OAAO,EACPqH,KACF,CAAC;UACD,IAAIlH,OAAO,KAAK,SAAS,EAAE;YACzB;UACF;QAEF;UACE,IAAIH,OAAO,CAACtC,IAAI,CAACwM,MAAM,EAAE;YAAA,IAAAC,gBAAA;YACvB,CAAAA,gBAAA,GAAAN,eAAe,YAAAM,gBAAA,GAAfN,eAAe,GACbJ,WAAW,CAACxK,6BAA6B,CAAC,YAAY,CAAC;UAC3D,CAAC,MAAM;YAAA,IAAAmL,eAAA;YACL,CAAAA,eAAA,GAAA3H,cAAc,YAAA2H,eAAA,GAAd3H,cAAc,GACZgH,WAAW,CAACxK,6BAA6B,CAAC,WAAW,CAAC;UAC1D;UACA;MACJ;MACAmK,oBAAoB,GAAG,IAAI;MAC3BE,oBAAoB,KAApBA,oBAAoB,GAClBtJ,OAAO,CAACtC,IAAI,CAACoH,UAAU,CAACI,IAAI,CAAC0C,mBAAmB,CAAC;IACrD,CAAC,MAAM,IAAI5H,OAAO,CAACtC,IAAI,CAACa,IAAI,KAAK,uBAAuB,EAAE;MAExD0K,eAAe,CAACgB,qBAAqB,CACnCjK,OAAO,EACPqH,KACF,CAAC;MACD,MAAM;QAAE5H,GAAG;QAAEC,KAAK;QAAEwK,MAAM,EAAEvK,QAAQ;QAAE0K;MAAS,CAAC,GAAGrK,OAAO,CAACtC,IAAI;MAE/D,MAAM4M,KAAK,GAAGf,uBAAuB,CAAC,CAAC;MACvC,MAAMgB,QAAQ,GAAG/K,qBAAqB,CAAC8K,KAAK,EAAE5K,KAAK,EAAEC,QAAQ,CAAC;MAC9D,MAAM6K,OAAO,GAAGxK,OAAO,CAACnB,GAAG,CAAC,KAAK,CAAC;MAClC,MAAM,CAACS,OAAO,CAAC,GAAGU,OAAO,CAAClB,WAAW,CAACyL,QAAQ,CAAC;MAE/CxK,oBAAoB,CAClBvC,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ0C,OAAO,EACP+K,QAAQ,IAAI,CAACG,OAAO,CAACC,oBAAoB,CAAC,CAAC,GACvCf,iBAAiB,CACfnC,uBAAuB,CAACF,KAAK,EAAE5H,GAAmB,CAAC,EACnD,aACF,CAAC,GACDA,GAAG,EACP6K,KAAK,EACLnK,OAAO,EACPkK,QAAQ,EACR1K,QACF,CAAC;IACH;IAEA,IAAI,UAAU,IAAIK,OAAO,CAACtC,IAAI,IAAIsC,OAAO,CAACtC,IAAI,CAAC2M,QAAQ,EAAE;MACvDhB,0BAA0B,KAA1BA,0BAA0B,GAAK,CAACI,WAAW,CAAC9J,QAAQ,CAACK,OAAO,CAACtC,IAAI,CAAC+B,GAAG,CAAC;IACxE;EACF;EAEA,IAAI,CAAC0J,eAAe,IAAI,CAACC,oBAAoB,EAAE;IAG7C,IAAII,WAAW,CAAC1M,MAAM,GAAG,CAAC,EAAE;MAC1BU,IAAI,CAACkN,YAAY,CAAClB,WAAW,CAACrG,GAAG,CAACC,IAAI,IAAIpF,WAAC,CAAC4C,mBAAmB,CAACwC,IAAI,CAAC,CAAC,CAAC;MAGvE5F,IAAI,CAACiB,KAAK,CAACkM,KAAK,CAAC,CAAC;IACpB;IAEA;EACF;EAEA,MAAMC,oBAAqC,GAAG,EAAE;EAEhD,IAAI1I,eAAoD;EACxD,MAAMmG,uBAAuB,GAAG,IAAIhL,GAAG,CAAS,CAAC;EAEjD,IAAIwN,cAA4B,EAAEC,YAA0B;EAC5D,IAAIC,mBAAwC,GAAG,IAAI;EAUnD,SAASC,0BAA0BA,CACjCxJ,WAA2B,EACO;IAClC,IAAIyJ,cAAc,GAAG,KAAK;IAC1B,IAAIC,aAAa,GAAG,KAAK;IACzB,MAAMnG,cAAuC,GAAG,EAAE;IAClD,KAAK,MAAM1D,UAAU,IAAIG,WAAW,EAAE;MACpC,IAAI2J,MAAM;MACV,KACGhL,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDnC,WAAC,CAACoN,kBAAkB,CAAC/J,UAAU,CAAC,EAChC;QACA,IAAIrD,WAAC,CAACiF,OAAO,CAAC5B,UAAU,CAAC8J,MAAM,CAAC,EAAE;UAChCA,MAAM,GAAGnN,WAAC,CAACsC,cAAc,CAAC,CAAC;QAC7B,CAAC,MAAM,IAAImJ,WAAW,CAAC9J,QAAQ,CAAC0B,UAAU,CAAC8J,MAAM,CAAC,EAAE;UAClDA,MAAM,GAAGnN,WAAC,CAACe,SAAS,CAACsC,UAAU,CAAC8J,MAAM,CAAC;QACzC,CAAC,MAAM;UAAA,IAAAE,oBAAA;UACL,CAAAA,oBAAA,GAAAN,mBAAmB,YAAAM,oBAAA,GAAnBN,mBAAmB,GACjBtB,WAAW,CAACxK,6BAA6B,CAAC,KAAK,CAAC;UAClDkM,MAAM,GAAGnN,WAAC,CAAC6C,oBAAoB,CAC7B,GAAG,EACH7C,WAAC,CAACe,SAAS,CAACgM,mBAAmB,CAAC,EAChC1J,UAAU,CAAC8J,MACb,CAAC;UACD9J,UAAU,CAAC8J,MAAM,GAAGnN,WAAC,CAACe,SAAS,CAACgM,mBAAmB,CAAC;QACtD;MACF;MACAhG,cAAc,CAACpD,IAAI,CAACwJ,MAAM,CAAC;MAC3BF,cAAc,KAAdA,cAAc,GAAK,CAACxB,WAAW,CAAC9J,QAAQ,CAAC0B,UAAU,CAAC;MACpD6J,aAAa,KAAbA,aAAa,GAAKtD,mBAAmB,CAACvG,UAAU,CAAC;IACnD;IACA,OAAO;MAAE4J,cAAc;MAAEC,aAAa;MAAEnG;IAAe,CAAC;EAC1D;EAEA,MAAMuG,uBAAuB,GAC3BjC,0BAA0B,IAGtBC,oBAAoB,IAAInJ,OAAO,KAAK,SAAU;EAEpD,IAAIoL,8BAA8B,GAAG,KAAK;EAC1C,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,gBAAgC,GAAG,EAAE;EACzC,IAAIC,kBAAgC;EACpC,IAAIvC,eAAe,EAAE;IACnB0B,cAAc,GAAGpB,WAAW,CAACxK,6BAA6B,CAAC,WAAW,CAAC;IACvEsM,8BAA8B,GAAG/N,IAAI,CAACmO,kBAAkB,CAAC,CAAC;IAC1D,CAAC;MAAE/O,EAAE,EAAEkO,YAAY;MAAEtN;IAAK,CAAC,GAAGa,mBAAmB,CAACb,IAAI,EAAEc,SAAS,CAAC;IAElEd,IAAI,CAACE,IAAI,CAACoH,UAAU,GAAG,IAAI;IAE3B,MAAM8G,oBAAoB,GAAGzC,eAAe,CAAChG,GAAG,CAACyB,EAAE,IAAIA,EAAE,CAACvD,UAAU,CAAC;IACrE,MAAMwK,uBAAuB,GAAGD,oBAAoB,CAAC1G,IAAI,CAAC+C,gBAAgB,CAAC;IAC3E,MAAM;MAAEgD,cAAc;MAAElG;IAAe,CAAC,GACtCiG,0BAA0B,CAACY,oBAAoB,CAAC;IAElD,MAAM;MAAErG,QAAQ;MAAEH;IAAK,CAAC,GAAGP,sBAAsB,CAC/C+G,oBAAoB,EACpB7G,cAAc,EACd5E,OACF,CAAC;IACDqL,oBAAoB,GAAGjG,QAAQ,GAAG,CAAC,GAAG,CAAC;IACvCkG,gBAAgB,GAAGrG,IAAI;IAEvB,IACG6F,cAAc,IAAIK,uBAAuB,IAC1CO,uBAAuB,EACvB;MACAH,kBAAkB,GAAGhC,iBAAiB,CACpC1L,WAAC,CAAC0H,eAAe,CAAC+F,gBAAgB,CAAC,EACnC,WACF,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAI,CAACjO,IAAI,CAACE,IAAI,CAACd,EAAE,EAAE;MACjBY,IAAI,CAACE,IAAI,CAACd,EAAE,GAAGY,IAAI,CAACiB,KAAK,CAACqN,qBAAqB,CAAC,OAAO,CAAC;IAC1D;IACAhB,YAAY,GAAG9M,WAAC,CAACe,SAAS,CAACvB,IAAI,CAACE,IAAI,CAACd,EAAE,CAAC;EAC1C;EAEA,IAAImP,uBAAsC;EAC1C,IAAIC,8BAA8B,GAAG,KAAK;EAE1C,IAAIC,2BAA2B,GAAG,EAAE;EACpC,IAAIC,iCAAiD,GAAG,EAAE;EAE1D,IAAI9C,oBAAoB,EAAE;IACxB,IAAI3G,cAAc,EAAE;MAClB,MAAML,aAAa,GAAGpE,WAAC,CAAC2E,cAAc,CAAC3E,WAAC,CAACe,SAAS,CAAC0D,cAAc,CAAC,EAAE,CAClEzE,WAAC,CAACsC,cAAc,CAAC,CAAC,CACnB,CAAC;MACF2L,2BAA2B,CAACtK,IAAI,CAACS,aAAa,CAAC;IACjD;IACA,KAAK,MAAMpC,OAAO,IAAIX,IAAI,EAAE;MAC1B,IAAI,CAACsH,6BAA6B,CAAC3G,OAAO,CAAC,EAAE;QAC3C,IACEkM,iCAAiC,CAACpP,MAAM,GAAG,CAAC,IAC5CkD,OAAO,CAACmM,aAAa,CAAC,CAAC,EACvB;UACArK,+BAA+B,CAC7BoK,iCAAiC,EACjClM,OACF,CAAC;UACDkM,iCAAiC,GAAG,EAAE;QACxC;QACA;MACF;MAEA,MAAM;QAAExO;MAAK,CAAC,GAAGsC,OAAO;MACxB,MAAM8E,UAAU,GAAGpH,IAAI,CAACoH,UAAU;MAElC,MAAMsH,aAAa,GAAG,CAAC,EAACtH,UAAU,YAAVA,UAAU,CAAEhI,MAAM;MAE1C,MAAMsD,UAAU,GAAG,UAAU,IAAI1C,IAAI,IAAIA,IAAI,CAAC2M,QAAQ;MAEtD,IAAI1M,IAAI,GAAG,aAAa;MAExB,IAAID,IAAI,CAAC+B,GAAG,CAAClB,IAAI,KAAK,aAAa,EAAE;QACnCZ,IAAI,GAAGD,IAAI,CAAC+B,GAAG,CAAC7C,EAAE,CAACe,IAAI;MACzB,CAAC,MAAM,IAAI,CAACyC,UAAU,IAAI1C,IAAI,CAAC+B,GAAG,CAAClB,IAAI,KAAK,YAAY,EAAE;QACxDZ,IAAI,GAAGD,IAAI,CAAC+B,GAAG,CAAC9B,IAAI;MACtB;MACA,IAAIkI,eAAgE;MACpE,IAAID,kBAAkB;MAEtB,IAAIwG,aAAa,EAAE;QACjB,MAAMR,oBAAoB,GAAG9G,UAAU,CAAC3B,GAAG,CAACkJ,CAAC,IAAIA,CAAC,CAAChL,UAAU,CAAC;QAC9D,MAAM;UAAE4J,cAAc;UAAEC,aAAa;UAAEnG;QAAe,CAAC,GACrDiG,0BAA0B,CAACY,oBAAoB,CAAC;QAClD,MAAM;UAAExG,IAAI;UAAEG;QAAS,CAAC,GAAGV,sBAAsB,CAC/C+G,oBAAoB,EACpB7G,cAAc,EACd5E,OACF,CAAC;QACDyF,kBAAkB,GAAGL,QAAQ;QAC7BM,eAAe,GAAGT,IAAI,CAACtI,MAAM,KAAK,CAAC,GAAGsI,IAAI,CAAC,CAAC,CAAC,GAAGpH,WAAC,CAAC0H,eAAe,CAACN,IAAI,CAAC;QACvE,IAAI8F,aAAa,IAAKD,cAAc,IAAIK,uBAAwB,EAAE;UAChEzF,eAAe,GAAG6D,iBAAiB,CAAC7D,eAAe,EAAElI,IAAI,GAAG,MAAM,CAAC;QACrE;MACF;MAEA,IAAIyC,UAAU,EAAE;QACd,IAAI,CAACJ,OAAO,CAACnB,GAAG,CAAC,KAAK,CAAC,CAAC4L,oBAAoB,CAAC,CAAC,EAAE;UAC9C/M,IAAI,CAAC+B,GAAG,GAAGiK,iBAAiB,CAC1BnC,uBAAuB,CAACF,KAAK,EAAE3J,IAAI,CAAC+B,GAAmB,CAAC,EACxD,aACF,CAAC;QACH;MACF;MAEA,MAAM;QAAEA,GAAG;QAAEyK,MAAM,EAAEvK;MAAS,CAAC,GAAGjC,IAAI;MAEtC,MAAM4O,SAAS,GAAG7M,GAAG,CAAClB,IAAI,KAAK,aAAa;MAE5C,MAAMiF,IAAI,GAAGgB,cAAc,CAACxE,OAAO,CAAC;MAEpC,IAAIsM,SAAS,IAAI,CAAC3M,QAAQ,EAAE;QAC1B,IAAIyM,aAAa,EAAE;UACjBJ,8BAA8B,GAAG,IAAI;QACvC;QACA,IAAIhO,WAAC,CAACuO,sBAAsB,CAAC7O,IAAI,CAAC,IAAI,CAACqO,uBAAuB,EAAE;UAC9DA,uBAAuB,GAAGtM,GAAG;QAC/B;MACF;MAEA,IAAIO,OAAO,CAACwM,aAAa,CAAC;QAAEhJ,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;QAClDtB,eAAe,GAAGlC,OAAO;MAC3B;MAEA,IAAIiG,MAAsB;MAC1B,IAAImG,aAAa,EAAE;QACjB,IAAItG,cAEH;QAED,IAAItC,IAAI,KAAKS,QAAQ,EAAE;UACrB,MAAM;YAAEvE;UAAM,CAAC,GAAGM,OAAO,CAACtC,IAA+B;UAEzD,MAAM+I,MAAsB,GACOtG,OAAO,KAAK,SAAS,IAAKR,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACsC,cAAc,CAAC,CAAC,CAAC;UAE1B,IAAIZ,KAAK,EAAE;YACT+G,MAAM,CAAC9E,IAAI,CAAC3D,WAAC,CAACe,SAAS,CAACW,KAAK,CAAC,CAAC;UACjC;UAEA,MAAM4K,KAAK,GAAGf,uBAAuB,CAAC,CAAC;UACvC,MAAMkD,cAAc,GAClBzM,OAAO,CAACvB,KAAK,CAACO,MAAM,CAACC,6BAA6B,CAAE,QAAOtB,IAAK,EAAC,CAAC;UACpE,MAAM+O,QAAQ,GAAG1O,WAAC,CAAC2E,cAAc,CAC/B3E,WAAC,CAACe,SAAS,CAAC0N,cAAc,CAAC,EAC3BhG,MACF,CAAC;UAED,MAAM8D,QAAQ,GAAG/K,qBAAqB,CAAC8K,KAAK,EAAEoC,QAAQ,EAAE/M,QAAQ,CAAC;UACjE,MAAM,CAACL,OAAO,CAAC,GAAGU,OAAO,CAAClB,WAAW,CAACyL,QAAQ,CAAC;UAE/C,IAAI+B,SAAS,EAAE;YACbxG,cAAc,GAAG3E,wBAAwB,CAACmJ,KAAK,EAAEnK,OAAO,CAAC;YAEzD,MAAMkG,KAAK,GAAG/G,OAAO,CAACb,KAAK,CAACO,MAAM,CAACC,6BAA6B,CAC7D,OAAMtB,IAAK,EACd,CAAC;YACD,MAAM2I,KAAK,GAAGhH,OAAO,CAACb,KAAK,CAACO,MAAM,CAACC,6BAA6B,CAC7D,OAAMtB,IAAK,EACd,CAAC;YAEDyI,mBAAmB,CAACjG,OAAO,EAAEb,OAAO,EAAEG,GAAG,EAAE4G,KAAK,EAAEC,KAAK,EAAE3G,QAAQ,CAAC;YAElEsG,MAAM,GAAG,CAACwG,cAAc,EAAEpG,KAAK,EAAEC,KAAK,CAAC;UACzC,CAAC,MAAM;YACLvG,oBAAoB,CAClBvC,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ0C,OAAO,EACPG,GAAG,EACH6K,KAAK,EACLnK,OAAO,EACPC,UAAU,EACVT,QACF,CAAC;YACDsG,MAAM,GAAG,CAACwG,cAAc,CAAC;UAC3B;QACF,CAAC,MAAM,IAAIjJ,IAAI,KAAKQ,KAAK,EAAE;UACzB,MAAM2I,MAAM,GAAG3M,OAAO,CAACvB,KAAK,CAACO,MAAM,CAACC,6BAA6B,CAC9D,QAAOtB,IAAK,EACf,CAAC;UACD,MAAMiP,SAAS,GACb5M,OAAO,CACPnB,GAAG,CAAC,OAAO,CAAC;UAEd,MAAMgO,IAAoB,GACS1M,OAAO,KAAK,SAAS,IAAKR,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACsC,cAAc,CAAC,CAAC,CAAC;UAC1B,IAAIsM,SAAS,CAAClP,IAAI,EAAEmP,IAAI,CAAClL,IAAI,CAACiL,SAAS,CAAClP,IAAI,CAAC;UAE7CkP,SAAS,CAAC9N,WAAW,CAACd,WAAC,CAAC2E,cAAc,CAAC3E,WAAC,CAACe,SAAS,CAAC4N,MAAM,CAAC,EAAEE,IAAI,CAAC,CAAC;UAElE5G,MAAM,GAAG,CAAC0G,MAAM,CAAC;UAEjB,IAAIL,SAAS,EAAE;YACbxG,cAAc,GAAG3E,wBAAwB,CAAC1B,GAAG,EAAEU,OAAO,CAAC;UACzD;QACF,CAAC,MAAM,IAAImM,SAAS,EAAE;UACpB,MAAMQ,MAAM,GAAG9M,OAAO,CAACvB,KAAK,CAACO,MAAM,CAACC,6BAA6B,CAC9D,QAAOtB,IAAK,EACf,CAAC;UACDsI,MAAM,GAAG,CAAC6G,MAAM,CAAC;UAEjB,MAAMC,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtChE,aAAa;YACbiE,UAAU,EAAEjN,OAAyC;YACrDkN,SAAS,EAAEpC,YAAY;YACvBqC,QAAQ,EAAE3P,IAAI,CAACE,IAAI,CAAC0B,UAAU;YAC9BgO,IAAI,EAAE/F,KAAK,CAAC+F,IAAI;YAChBC,aAAa,EAAEvC;UACjB,CAAC,CAAC;UAEFiC,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvBxH,cAAc,GAAG,CACfiB,yCAAyC,CACvC/G,OAAO,CAACtC,IACV,CAAC,CACF;UAED,IAAI8F,IAAI,KAAKW,MAAM,IAAIX,IAAI,KAAKY,MAAM,EAAE;YACtCmC,mBAAmB,CACjBvG,OAAO,EACPhC,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBzB,WAAC,CAACe,SAAS,CAAC+N,MAAM,CAAC,EACnBnN,QACF,CAAC;UACH,CAAC,MAAM;YACL,MAAMjC,IAAI,GAAGsC,OAAO,CAACtC,IAA4B;YAGjDF,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACzBiB,WAAC,CAAC4B,oBAAoB,CAACH,GAAG,EAAEzB,WAAC,CAACe,SAAS,CAAC+N,MAAM,CAAC,EAAE,EAAE,EAAEpP,IAAI,CAACwM,MAAM,CAClE,CAAC;YAED7B,uBAAuB,CAAC5K,GAAG,CAACgC,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;YAExCqC,OAAO,CAACuN,MAAM,CAAC,CAAC;UAClB;QACF;QAEA,IAAIC,QAAsB;QAE1B,IAAIpN,UAAU,EAAE;UACdoN,QAAQ,GAAGxP,WAAC,CAACe,SAAS,CAACU,GAAmB,CAAC;QAC7C,CAAC,MAAM,IAAIA,GAAG,CAAClB,IAAI,KAAK,aAAa,EAAE;UACrCiP,QAAQ,GAAGxP,WAAC,CAACyP,aAAa,CAAChO,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;QACzC,CAAC,MAAM,IAAI8B,GAAG,CAAClB,IAAI,KAAK,YAAY,EAAE;UACpCiP,QAAQ,GAAGxP,WAAC,CAACyP,aAAa,CAAChO,GAAG,CAAC9B,IAAI,CAAC;QACtC,CAAC,MAAM;UACL6P,QAAQ,GAAGxP,WAAC,CAACe,SAAS,CAACU,GAAmB,CAAC;QAC7C;QAEAmL,oBAAoB,CAACjJ,IAAI,CAAC;UACxB6B,IAAI;UACJqC,eAAe;UACfD,kBAAkB;UAClBjI,IAAI,EAAE6P,QAAQ;UACd7N,QAAQ;UACRmG,cAAc;UACdG;QACF,CAAC,CAAC;QAEF,IAAIjG,OAAO,CAACtC,IAAI,EAAE;UAChBsC,OAAO,CAACtC,IAAI,CAACoH,UAAU,GAAG,IAAI;QAChC;MACF;MAEA,IACEmH,2BAA2B,CAACnP,MAAM,GAAG,CAAC,IACtC,CAAC6C,QAAQ,KACR6D,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACA1C,oCAAoC,CAClC0K,2BAA2B,EAC3BjM,OACF,CAAC;QACDiM,2BAA2B,GAAG,EAAE;MAClC;MAEA,IACEC,iCAAiC,CAACpP,MAAM,GAAG,CAAC,IAC5C6C,QAAQ,KACP6D,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACA1C,oCAAoC,CAClC2K,iCAAiC,EACjClM,OACF,CAAC;QACDkM,iCAAiC,GAAG,EAAE;MACxC;MAEA,IAAIE,aAAa,IAAIjM,OAAO,KAAK,SAAS,EAAE;QAC1C,IAAIqD,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,EAAE;UACvC,MAAMyJ,WAAW,GAAGjE,WAAW,CAACxK,6BAA6B,CAC1D,cAAatB,IAAK,EACrB,CAAC;UACDsI,MAAM,CAACtE,IAAI,CAAC+L,WAAW,CAAC;UACxB,MAAMC,aAAa,GAAG3P,WAAC,CAAC2E,cAAc,CACpC3E,WAAC,CAACe,SAAS,CAAC2O,WAAW,CAAC,EACxB/N,QAAQ,GAAG,EAAE,GAAG,CAAC3B,WAAC,CAACsC,cAAc,CAAC,CAAC,CACrC,CAAC;UACD,IAAI,CAACX,QAAQ,EAAE;YACbsM,2BAA2B,CAACtK,IAAI,CAACgM,aAAa,CAAC;UACjD,CAAC,MAAM;YACLzB,iCAAiC,CAACvK,IAAI,CAACgM,aAAa,CAAC;UACvD;QACF;MACF;IACF;EACF;EAEA,IAAI1B,2BAA2B,CAACnP,MAAM,GAAG,CAAC,EAAE;IAC1C,MAAM4G,cAAc,GAAG,CAAC,CAAClG,IAAI,CAACE,IAAI,CAAC0B,UAAU;IAC7C,IAAI8C,eAAe,EAAE;MACnB,IAAIwB,cAAc,EAAE;QAClBZ,0CAA0C,CACxCmJ,2BAA2B,EAC3B/J,eAAe,EACfO,cACF,CAAC;MACH,CAAC,MAAM;QACLR,+BAA+B,CAC7BgK,2BAA2B,EAC3B/J,eACF,CAAC;MACH;IACF,CAAC,MAAM;MACL1E,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACzB0G,gCAAgC,CAC9BwI,2BAA2B,EAC3BvI,cACF,CACF,CAAC;IACH;IACAuI,2BAA2B,GAAG,EAAE;EAClC;EAEA,IAAIC,iCAAiC,CAACpP,MAAM,GAAG,CAAC,EAAE;IAChDU,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACsC,IAAI,CACtBmC,gCAAgC,CAACoI,iCAAiC,CACpE,CAAC;IACDA,iCAAiC,GAAG,EAAE;EACxC;EAEA,MAAM0B,0BAA0B,GAC9BnJ,qBAAqB,CAACmG,oBAAoB,CAAC;EAE7C,MAAMiD,kBAAkB,GAAGrI,uBAAuB,CAChBrF,OAAO,KAAK,SAAS,GACjDyK,oBAAoB,GACpBgD,0BAA0B,EAC9BzN,OACF,CAAC;EAED,MAAM2N,aAA6B,GAAG/H,8BAA8B,CAClE6H,0BACF,CAAC;EAED,IAAInL,cAAc,EAAE;IAClBqL,aAAa,CAACnM,IAAI,CAACc,cAAc,CAAC;EACpC;EAEA,IAAIoH,eAAe,EAAE;IACnBiE,aAAa,CAACnM,IAAI,CAACkI,eAAe,CAAC;EACrC;EAEA,MAAMkE,WAA2B,GAAG,EAAE;EACtC,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,MAAMC,aAAa,GACjBpD,cAAc,IAAI7M,WAAC,CAAC2E,cAAc,CAAC3E,WAAC,CAACe,SAAS,CAAC8L,cAAc,CAAC,EAAE,EAAE,CAAC;EAErE,MAAMqD,aAAa,GAAG1Q,IAAI,CAACE,IAAI;EAE/B,IAAIyL,eAAe,EAAE;IACnB4E,WAAW,CAACpM,IAAI,CAACmJ,YAAY,EAAED,cAAc,CAAC;IAC9C,MAAMsD,OAIH,GAAG,EAAE;IACR3Q,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC,CAACuP,OAAO,CAACpO,OAAO,IAAI;MAGvC,IAAIA,OAAO,CAACmM,aAAa,CAAC,CAAC,EAAE;QAC3BD,iCAAiC,CAACvK,IAAI,CAACiF,iBAAiB,CAAC5G,OAAO,CAACtC,IAAI,CAAC,CAAC;QACvEsC,OAAO,CAACuN,MAAM,CAAC,CAAC;QAChB;MACF;MAEA,MAAMc,UAAU,GACdrO,OAAO,CAACsO,eAAe,CAAC,CAAC,IAAItO,OAAO,CAACuM,sBAAsB,CAAC,CAAC;MAE/D,IACE,CAAC8B,UAAU,IAAIrO,OAAO,CAACuO,oBAAoB,CAAC,CAAC,KAC7CvO,OAAO,CAACtC,IAAI,CAACwM,MAAM,EACnB;QACA,IAAImE,UAAU,IAAInC,iCAAiC,CAACpP,MAAM,GAAG,CAAC,EAAE;UAC9DyE,oCAAoC,CAClC2K,iCAAiC,EACjClM,OACF,CAAC;UACDkM,iCAAiC,GAAG,EAAE;QACxC;QAEAlM,OAAO,CAACtC,IAAI,CAACwM,MAAM,GAAG,KAAK;QAC3BiE,OAAO,CAACxM,IAAI,CAAC3B,OAAO,CAACtC,IAAI,CAAC;QAC1BsC,OAAO,CAACuN,MAAM,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,IAAIY,OAAO,CAACrR,MAAM,GAAG,CAAC,IAAIoP,iCAAiC,CAACpP,MAAM,GAAG,CAAC,EAAE;MACtE,MAAM0R,YAAY,GAAGpN,cAAQ,CAACC,UAAU,CAACC,GAAI;AACnD,wBAAwB+F,KAAK,CAACC,SAAS,CAAC,UAAU,CAAE;AACpD,OAA4B;MACtBkH,YAAY,CAACnP,IAAI,CAACA,IAAI,GAAG,CACvBrB,WAAC,CAAC+F,WAAW,CAAC,CACZ/F,WAAC,CAACyQ,WAAW,CAACP,aAAa,EAAE,IAAI,CAAC,IAGhClQ,WAAC,CAAC4C,mBAAmB,CAACsN,aAAkC,CAAC,CAC5D,CAAC,EACF,GAAGC,OAAO,CACX;MAED,MAAMO,eAA+B,GAAG,EAAE;MAE1C,MAAMC,OAAO,GAAG3Q,WAAC,CAAC4Q,aAAa,CAACJ,YAAY,EAAE,EAAE,CAAC;MAEjD,IAAItC,iCAAiC,CAACpP,MAAM,GAAG,CAAC,EAAE;QAChD4R,eAAe,CAAC/M,IAAI,CAAC,GAAGuK,iCAAiC,CAAC;MAC5D;MACA,IAAI+B,aAAa,EAAE;QACjBD,iBAAiB,GAAG,IAAI;QACxBU,eAAe,CAAC/M,IAAI,CAACsM,aAAa,CAAC;MACrC;MACA,IAAIS,eAAe,CAAC5R,MAAM,GAAG,CAAC,EAAE;QAC9B4R,eAAe,CAAC3R,OAAO,CACrBiB,WAAC,CAAC2E,cAAc,CAAC3E,WAAC,CAAC2F,KAAK,CAAC,CAAC,EAAE,CAAC3F,WAAC,CAACe,SAAS,CAAC+L,YAAY,CAAC,CAAC,CACzD,CAAC;QAGD0D,YAAY,CAACnP,IAAI,CAACA,IAAI,CAACsC,IAAI,CACzB8B,gCAAgC,CAC9BiL,eAAe,EACM,KACvB,CACF,CAAC;MACH,CAAC,MAAM;QACLC,OAAO,CAACE,SAAS,CAAClN,IAAI,CAAC3D,WAAC,CAACe,SAAS,CAAC+L,YAAY,CAAC,CAAC;MACnD;MAEAtN,IAAI,CAACsB,WAAW,CAAC6P,OAAO,CAAC;IAC3B;EACF;EACA,IAAI,CAACX,iBAAiB,IAAIC,aAAa,EAAE;IACvCzQ,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACsC,IAAI,CACtB3D,WAAC,CAAC+F,WAAW,CAAC,CAAC/F,WAAC,CAAC4C,mBAAmB,CAACqN,aAAa,CAAC,CAAC,CACtD,CAAC;EACH;EAEA,IAAI;IAAE7O;EAAW,CAAC,GAAG8O,aAAa;EAClC,IACE9O,UAAU,KAERe,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,GACvB;IACA,MAAMvD,EAAE,GAAGY,IAAI,CAACiB,KAAK,CAACqQ,qBAAqB,CAAC1P,UAAU,CAAC;IACvD,IAAIxC,EAAE,EAAE;MACNsR,aAAa,CAAC9O,UAAU,GAAGpB,WAAC,CAAC6C,oBAAoB,CAAC,GAAG,EAAEjE,EAAE,EAAEwC,UAAU,CAAC;MACtEA,UAAU,GAAGxC,EAAE;IACjB;EACF;EACAsR,aAAa,CAAC7O,IAAI,CAACA,IAAI,CAACtC,OAAO,CAC7BiB,WAAC,CAAC+F,WAAW,CACX,CACE/F,WAAC,CAAC4C,mBAAmB,CACnBmO,sBAAsB,CACpBjB,aAAa,EACbC,WAAW,EACXF,kBAAkB,GAAA3E,mBAAA,GAClBwC,kBAAkB,YAAAxC,mBAAA,GAAIlL,WAAC,CAAC0H,eAAe,CAAC+F,gBAAgB,CAAC,EACzDzN,WAAC,CAACsH,cAAc,CAACkG,oBAAoB,CAAC,EACtCQ,8BAA8B,GAAGD,uBAAuB,GAAG,IAAI,EAC/D,OAAOzN,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGuB,SAAS,EACrD7B,WAAC,CAACe,SAAS,CAACK,UAAU,CAAC,EACvBiI,KAAK,EACLlH,OACF,CACF,CAAC,EACD0J,eAAe,IACb7L,WAAC,CAAC4C,mBAAmB,CACnB5C,WAAC,CAAC2E,cAAc,CAAC3E,WAAC,CAACe,SAAS,CAAC8K,eAAe,CAAC,EAAE,CAC7C7L,WAAC,CAACsC,cAAc,CAAC,CAAC,CACnB,CACH,CAAC,CACJ,CAACqE,MAAM,CAACQ,OAAO,CAClB,CACF,CAAC;EAID3H,IAAI,CAACkN,YAAY,CAAClB,WAAW,CAACrG,GAAG,CAACC,IAAI,IAAIpF,WAAC,CAAC4C,mBAAmB,CAACwC,IAAI,CAAC,CAAC,CAAC;EAEvE,IAAImI,8BAA8B,EAAE;IAClC/N,IAAI,CAACkN,YAAY,CACf1M,WAAC,CAACgR,mBAAmB,CAAC,KAAK,EAAE,CAC3BhR,WAAC,CAACiR,kBAAkB,CAACjR,WAAC,CAACe,SAAS,CAAC+L,YAAY,CAAC,CAAC,CAChD,CACH,CAAC;EACH;EAEA,IAAIzC,uBAAuB,CAAC6G,IAAI,GAAG,CAAC,EAAE;IACpC9G,6BAA6B,CAAC5K,IAAI,EAAE6K,uBAAuB,CAAC;EAC9D;EAGA7K,IAAI,CAACiB,KAAK,CAACkM,KAAK,CAAC,CAAC;EAElB,OAAOnN,IAAI;AACb;AAEA,SAASuR,sBAAsBA,CAC7BjB,aAA6B,EAC7BC,WAA2B,EAC3BF,kBAAoD,EACpDpC,gBAAkD,EAClDD,oBAAsC,EACtC2D,qBAA2C,EAC3CC,YAAwD,EACxDhQ,UAA+B,EAC/BiI,KAAiB,EACjBlH,OAA6B,EAC7B;EACA,IAAIkP,GAAG,EAAEC,GAAG;EACZ,MAAMzC,IAAoB,GAAG,CAC3BuC,YAAY,GACRhI,yBAAyB,CAACC,KAAK,EAAE+H,YAAY,CAAC,GAC9CpR,WAAC,CAACsC,cAAc,CAAC,CAAC,EACtBmL,gBAAgB,EAChBoC,kBAAkB,CACnB;EAEkC;IACjC,IAAI1N,OAAO,KAAK,SAAS,EAAE;MACzB0M,IAAI,CAACjK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEiL,kBAAkB,EAAEpC,gBAAgB,CAAC;IACzD;IACA,IACEtL,OAAO,KAAK,SAAS,IACpBA,OAAO,KAAK,SAAS,IAAI,CAACkH,KAAK,CAACkI,eAAe,CAAC,gBAAgB,CAAE,EACnE;MACAF,GAAG,GAAGrR,WAAC,CAACwR,YAAY,CAAC,CAAC,GAAG1B,aAAa,EAAE,GAAGC,WAAW,CAAC,CAAC;MACxDuB,GAAG,GAAGtR,WAAC,CAAC2E,cAAc,CACpB0E,KAAK,CAACC,SAAS,CAACnH,OAAO,KAAK,SAAS,GAAG,WAAW,GAAG,eAAe,CAAC,EACtE0M,IACF,CAAC;MACD,OAAO7O,WAAC,CAAC6C,oBAAoB,CAAC,GAAG,EAAEwO,GAAG,EAAEC,GAAG,CAAC;IAC9C,CAAC,MAAM,IAAInP,OAAO,KAAK,SAAS,EAAE;MAChCmP,GAAG,GAAGtR,WAAC,CAAC2E,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,gBAAgB,CAAC,EAAEuF,IAAI,CAAC;IACjE,CAAC,MAAM,IAAI1M,OAAO,KAAK,SAAS,EAAE;MAChC,IAAIgP,qBAAqB,EAAE;QACzBtC,IAAI,CAAClL,IAAI,CAAC8F,8BAA8B,CAAC0H,qBAAqB,CAAC,CAAC;MAClE;MACAG,GAAG,GAAGtR,WAAC,CAAC2E,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAEuF,IAAI,CAAC;IAChE,CAAC,MAAM,IAAI1M,OAAO,KAAK,SAAS,EAAE;MAChC,IACEgP,qBAAqB,IACrB/P,UAAU,IACVoM,oBAAoB,CAAC9L,KAAK,KAAK,CAAC,EAChC;QACAmN,IAAI,CAAClL,IAAI,CAAC6J,oBAAoB,CAAC;MACjC;MACA,IAAI2D,qBAAqB,EAAE;QACzBtC,IAAI,CAAClL,IAAI,CAAC8F,8BAA8B,CAAC0H,qBAAqB,CAAC,CAAC;MAClE,CAAC,MAAM,IAAI/P,UAAU,EAAE;QACrByN,IAAI,CAAClL,IAAI,CAAC3D,WAAC,CAAC4D,eAAe,CAAC,MAAM,EAAE5D,WAAC,CAACsH,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D;MACA,IAAIlG,UAAU,EAAEyN,IAAI,CAAClL,IAAI,CAACvC,UAAU,CAAC;MACrCkQ,GAAG,GAAGtR,WAAC,CAAC2E,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAEuF,IAAI,CAAC;IAChE;EACF;EACA,IAAoC1M,OAAO,KAAK,SAAS,EAAE;IACzD,IACEgP,qBAAqB,IACrB/P,UAAU,IACVoM,oBAAoB,CAAC9L,KAAK,KAAK,CAAC,EAChC;MACAmN,IAAI,CAAClL,IAAI,CAAC6J,oBAAoB,CAAC;IACjC;IACA,IAAI2D,qBAAqB,EAAE;MACzBtC,IAAI,CAAClL,IAAI,CAAC8F,8BAA8B,CAAC0H,qBAAqB,CAAC,CAAC;IAClE,CAAC,MAAM,IAAI/P,UAAU,EAAE;MACrByN,IAAI,CAAClL,IAAI,CAAC3D,WAAC,CAAC4D,eAAe,CAAC,MAAM,EAAE5D,WAAC,CAACsH,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAIlG,UAAU,EAAEyN,IAAI,CAAClL,IAAI,CAACvC,UAAU,CAAC;IACrCkQ,GAAG,GAAGtR,WAAC,CAAC2E,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAEuF,IAAI,CAAC;EAChE;EAIA,IAAIiB,aAAa,CAAChR,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAIiR,WAAW,CAACjR,MAAM,GAAG,CAAC,EAAE;MAC1BuS,GAAG,GAAGrR,WAAC,CAACyR,aAAa,CAAC,CACpBzR,WAAC,CAAC0R,cAAc,CAAC1R,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAACwR,YAAY,CAAC1B,aAAa,CAAC,CAAC,EAClE9P,WAAC,CAAC0R,cAAc,CAAC1R,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAACwR,YAAY,CAACzB,WAAW,CAAC,CAAC,CACjE,CAAC;IACJ,CAAC,MAAM;MACLsB,GAAG,GAAGrR,WAAC,CAACwR,YAAY,CAAC1B,aAAa,CAAC;MACnCwB,GAAG,GAAGtR,WAAC,CAAC0C,gBAAgB,CAAC4O,GAAG,EAAEtR,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE;EACF,CAAC,MAAM;IAELmR,GAAG,GAAGrR,WAAC,CAACwR,YAAY,CAACzB,WAAW,CAAC;IACjCuB,GAAG,GAAGtR,WAAC,CAAC0C,gBAAgB,CAAC4O,GAAG,EAAEtR,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE;EAEA,OAAOF,WAAC,CAAC6C,oBAAoB,CAAC,GAAG,EAAEwO,GAAG,EAAEC,GAAG,CAAC;AAC9C;AAEA,SAASK,UAAUA,CACjBjS,IAAyE,EACzE;EACA,OAAOA,IAAI,CAACa,IAAI,KAAK,YAAY,GAC7Bb,IAAI,CAACC,IAAI,KAAK,WAAW,GACzBD,IAAI,CAACgC,KAAK,KAAK,WAAW;AAChC;AAEA,SAASoK,WAAWA,CAACpM,IAAuC,EAAE;EAC5D,OAAOA,IAAI,CAACoH,UAAU,IAAIpH,IAAI,CAACoH,UAAU,CAAChI,MAAM,GAAG,CAAC;AACtD;AAEA,SAAS8S,sBAAsBA,CAAClS,IAAkB,EAAE;EAClD,QAAQA,IAAI,CAACa,IAAI;IACf,KAAK,uBAAuB;MAC1B,OAAO,IAAI;IACb,KAAK,aAAa;IAClB,KAAK,eAAe;IACpB,KAAK,oBAAoB;IACzB,KAAK,sBAAsB;MACzB,OAAOuL,WAAW,CAACpM,IAAI,CAAC;IAC1B;MACE,OAAO,KAAK;EAChB;AACF;AAEA,SAASmS,oBAAoBA,CAACnS,IAAa,EAAE;EAC3C,OAAOoM,WAAW,CAACpM,IAAI,CAAC,IAAIA,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAAC6F,IAAI,CAAC0K,sBAAsB,CAAC;AACzE;AAGA,SAASE,8BAA8BA,CACrCC,WAAwC,EACxCC,OASS,EACT;EACA,SAASC,sBAAsBA,CAC7BC,YAEC,EACDzQ,GAAiB,EACjB4H,KAAiB,EACe;IAChC,QAAQ5H,GAAG,CAAClB,IAAI;MACd,KAAK,eAAe;QAClB,OAAOP,WAAC,CAACyP,aAAa,CAAChO,GAAG,CAACC,KAAK,CAAC;MACnC,KAAK,gBAAgB;MACrB,KAAK,eAAe;QAAE;UACpB,MAAMyQ,QAAQ,GAAG1Q,GAAG,CAACC,KAAK,GAAG,EAAE;UAC/BwQ,YAAY,CAACrR,GAAG,CAAC,KAAK,CAAC,CAACC,WAAW,CAACd,WAAC,CAACyP,aAAa,CAAC0C,QAAQ,CAAC,CAAC;UAC9D,OAAOnS,WAAC,CAACyP,aAAa,CAAC0C,QAAQ,CAAC;QAClC;MACA;QAAS;UACP,MAAMC,GAAG,GAAGF,YAAY,CAACzR,KAAK,CAACqQ,qBAAqB,CAACrP,GAAG,CAAC;UACzDyQ,YAAY,CACTrR,GAAG,CAAC,KAAK,CAAC,CACVC,WAAW,CACVd,WAAC,CAAC6C,oBAAoB,CACpB,GAAG,EACHuP,GAAG,EACH7I,uBAAuB,CAACF,KAAK,EAAE5H,GAAG,CACpC,CACF,CAAC;UACH,OAAOzB,WAAC,CAACe,SAAS,CAACqR,GAAG,CAAC;QACzB;IACF;EACF;EACA,OAAO;IACLC,kBAAkBA,CAAC7S,IAAI,EAAE6J,KAAK,EAAE;MAC9B,MAAMzK,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACd,EAAE;MACvB,IAAIA,EAAE,CAAC2B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAMmD,WAAW,GAAG,IAAA4O,oEAA2B,EAAC9S,IAAI,CAACqB,GAAG,CAAC,MAAM,CAAC,CAAC;QACjE,IAAIkR,WAAW,CAACrO,WAAW,CAAC,EAAE;UAC5B,MAAM/D,IAAI,GAAGf,EAAE,CAACe,IAAI;UACpBqS,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE1J,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IACD4S,oBAAoBA,CAAC/S,IAAI,EAAE6J,KAAK,EAAE;MAChC,MAAMzK,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACiL,IAAI;MACzB,IAAI/L,EAAE,CAAC2B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAMmD,WAAW,GAAG,IAAA4O,oEAA2B,EAAC9S,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAIkR,WAAW,CAACrO,WAAW,CAAC,EAAE;UAC5B,QAAQlE,IAAI,CAACE,IAAI,CAAC8S,QAAQ;YACxB,KAAK,GAAG;YACR,KAAK,KAAK;YACV,KAAK,KAAK;YACV,KAAK,KAAK;cACRR,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAEzK,EAAE,CAACe,IAAI,CAAC;UACxC;QACF;MACF;IACF,CAAC;IACD8S,iBAAiBA,CAACjT,IAAI,EAAE6J,KAAK,EAAE;MAC7B,MAAMzK,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACiL,IAAI;MACzB,IAAI/L,EAAE,CAAC2B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAMmD,WAAW,GAAG,IAAA4O,oEAA2B,EAAC9S,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAIkR,WAAW,CAACrO,WAAW,CAAC,EAAE;UAC5B,MAAM/D,IAAI,GAAGf,EAAE,CAACe,IAAI;UACpBqS,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE1J,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IAGD+S,gBAAgBA,CAAClT,IAAI,EAAE6J,KAAK,EAAE;MAC5B,KAAK,MAAM6I,YAAY,IAAI1S,IAAI,CAACqB,GAAG,CAAC,YAAY,CAAC,EAAE;QACjD,MAAM;UAAEnB;QAAK,CAAC,GAAGwS,YAAY;QAC7B,IAAIxS,IAAI,CAACa,IAAI,KAAK,gBAAgB,EAAE;QACpC,MAAM3B,EAAE,GAAGc,IAAI,CAAC+B,GAAG;QACnB,MAAMiC,WAAW,GAAG,IAAA4O,oEAA2B,EAC7CJ,YAAY,CAACrR,GAAG,CAAC,OAAO,CAC1B,CAAC;QACD,IAAIkR,WAAW,CAACrO,WAAW,CAAC,EAAE;UAC5B,IAAI,CAAChE,IAAI,CAAC2M,QAAQ,EAAE;YAElB,IAAI,CAACsF,UAAU,CAAC/S,EAAoC,CAAC,EAAE;cACrD,IAAIA,EAAE,CAAC2B,IAAI,KAAK,YAAY,EAAE;gBAC5ByR,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAEzK,EAAE,CAACe,IAAI,CAAC;cACtC,CAAC,MAAM;gBACL,MAAMW,SAAS,GAAGN,WAAC,CAACyP,aAAa,CAC9B7Q,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;gBACDsQ,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE/I,SAAS,CAAC;cACxC;YACF;UACF,CAAC,MAAM;YACL,MAAM8R,GAAG,GAAGH,sBAAsB,CAChCC,YAAY,EAEZtT,EAAE,EACFyK,KACF,CAAC;YACD2I,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE+I,GAAG,CAAC;UAClC;QACF;MACF;IACF,CAAC;IACDpG,oBAAoBA,CAACxM,IAAI,EAAE6J,KAAK,EAAE;MAChC,MAAM;QAAE3J;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAMkE,WAAW,GAAG,IAAA4O,oEAA2B,EAAC9S,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAIkR,WAAW,CAACrO,WAAW,CAAC,EAAE;QAC5B,MAAMpD,SAAS,GAAGN,WAAC,CAACyP,aAAa,CAAC,GAAG,GAAG/P,IAAI,CAAC+B,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;QACzDqS,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE/I,SAAS,CAAC;MACxC;IACF,CAAC;IACD2L,qBAAqBA,CAACzM,IAAI,EAAE6J,KAAK,EAAE;MACjC,MAAM;QAAE3J;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAMZ,EAAE,GAAGc,IAAI,CAAC+B,GAAG;MACnB,MAAMiC,WAAW,GAAG,IAAA4O,oEAA2B,EAAC9S,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAIkR,WAAW,CAACrO,WAAW,CAAC,EAAE;QAC5B,IAAI,CAAChE,IAAI,CAAC2M,QAAQ,EAAE;UAClB,IAAIzN,EAAE,CAAC2B,IAAI,KAAK,YAAY,EAAE;YAC5ByR,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAEzK,EAAE,CAACe,IAAI,CAAC;UACtC,CAAC,MAAM,IAAIf,EAAE,CAAC2B,IAAI,KAAK,aAAa,EAAE;YACpC,MAAMD,SAAS,GAAGN,WAAC,CAACyP,aAAa,CAAC,GAAG,GAAG7Q,EAAE,CAACA,EAAE,CAACe,IAAI,CAAC;YACnDqS,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE/I,SAAS,CAAC;UACxC,CAAC,MAAM;YACL,MAAMA,SAAS,GAAGN,WAAC,CAACyP,aAAa,CAC9B7Q,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;YACDsQ,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE/I,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAM8R,GAAG,GAAGH,sBAAsB,CAChCzS,IAAI,EAEJZ,EAAE,EACFyK,KACF,CAAC;UACD2I,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE+I,GAAG,CAAC;QAClC;MACF;IACF,CAAC;IACDrG,aAAaA,CAACvM,IAAI,EAAE6J,KAAK,EAAE;MACzB,MAAM;QAAE3J;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAMZ,EAAE,GAAGc,IAAI,CAAC+B,GAAG;MACnB,MAAMiC,WAAW,GAAG,IAAA4O,oEAA2B,EAAC9S,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAIkR,WAAW,CAACrO,WAAW,CAAC,EAAE;QAC5B,IAAI,CAAChE,IAAI,CAAC2M,QAAQ,EAAE;UAClB,IAAIzN,EAAE,CAAC2B,IAAI,KAAK,YAAY,EAAE;YAC5ByR,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAEzK,EAAE,CAACe,IAAI,CAAC;UACtC,CAAC,MAAM;YACL,MAAMW,SAAS,GAAGN,WAAC,CAACyP,aAAa,CAC9B7Q,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;YACDsQ,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE/I,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAM8R,GAAG,GAAGH,sBAAsB,CAACzS,IAAI,EAAEZ,EAAE,EAAEyK,KAAK,CAAC;UACnD2I,OAAO,CAACtO,WAAW,EAAE2F,KAAK,EAAE+I,GAAG,CAAC;QAClC;MACF;IACF;EACF,CAAC;AACH;AAEA,SAASO,mCAAmCA,CAACnT,IAAc,EAAE;EAC3D,OACEA,IAAI,CAACoT,iBAAiB,CAAC;IAAEhU,EAAE,EAAE;EAAK,CAAC,CAAC,IAAIiT,oBAAoB,CAACrS,IAAI,CAACE,IAAI,CAAC;AAE3E;AAEe,SAAAmT,SACb;EAAEC,aAAa;EAAEC;AAAsB,CAAC,EACxC;EAAEC;AAAe,CAAC,EAClB7Q,OAA6B,EAC7B8Q,QAAkC,EACpB;EAAA,IAAAC,WAAA;EAGP;IACL,IACE/Q,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,EACrB;MACA2Q,aAAa,CAAC,SAAS,CAAC;IAC1B,CAAC,MAAM,IAAI3Q,OAAO,KAAK,SAAS,EAAE;MAChC2Q,aAAa,CAAC,SAAS,CAAC;IAC1B,CAAC,MAAM;MACLA,aAAa,CAAC,SAAS,CAAC;IAC1B;EACF;EAEA,MAAMK,OAAO,GAAG,IAAIC,OAAO,CAAW,CAAC;EACvC,MAAMpI,aAAa,IAAAkI,WAAA,GAAGH,UAAU,CAAC,eAAe,CAAC,YAAAG,WAAA,GAAIF,KAAK;EAE1D,MAAMK,sBAA2C,GAC/CvB,8BAA8B,CAC5Ba,mCAAmC,EACnCW,UACF,CAAC;EAEH,SAASA,UAAUA,CACjB9T,IAAuB,EACvB6J,KAAiB,EACjB/I,SAA8D,EAC9D;IAAA,IAAAiT,UAAA,EAAAC,QAAA;IACA,IAAIL,OAAO,CAACpT,GAAG,CAACP,IAAI,CAAC,EAAE;IACvB,MAAM;MAAEE;IAAK,CAAC,GAAGF,IAAI;IACrB,CAAA+T,UAAA,GAAAjT,SAAS,YAAAiT,UAAA,GAATjT,SAAS,IAAAkT,QAAA,GAAK9T,IAAI,CAACd,EAAE,qBAAP4U,QAAA,CAAS7T,IAAI;IAC3B,MAAM2B,OAAO,GAAGyJ,cAAc,CAC5BvL,IAAI,EACJ6J,KAAK,EACL2B,aAAa,EACb7I,OAAO,EACP7B,SAAS,EACT+S,sBACF,CAAC;IACD,IAAI/R,OAAO,EAAE;MACX6R,OAAO,CAAC1T,GAAG,CAAC6B,OAAO,CAAC;MACpB;IACF;IACA6R,OAAO,CAAC1T,GAAG,CAACD,IAAI,CAAC;EACnB;EAEA,OAAO;IACLG,IAAI,EAAE,qBAAqB;IAC3BsT,QAAQ,EAAEA,QAAQ;IAElBjB,OAAO,EAAAyB,MAAA,CAAAC,MAAA;MACLC,wBAAwBA,CAACnU,IAAI,EAAE6J,KAAK,EAAE;QACpC,MAAM;UAAEuK;QAAY,CAAC,GAAGpU,IAAI,CAACE,IAAI;QACjC,IACE,CAAAkU,WAAW,oBAAXA,WAAW,CAAErT,IAAI,MAAK,kBAAkB,IAGxCuL,WAAW,CAAC8H,WAAW,CAAC,EACxB;UACA,MAAM7B,WAAW,GAAG,CAAC6B,WAAW,CAAChV,EAAE;UACnC,MAAMiV,yBAAyB,GAAG,IAAAC,qCAAsB,EACtDtU,IACF,CAA4C;UAC5C,IAAIuS,WAAW,EAAE;YACfuB,UAAU,CACRO,yBAAyB,EACzBxK,KAAK,EACLrJ,WAAC,CAACyP,aAAa,CAAC,SAAS,CAC3B,CAAC;UACH;QACF;MACF,CAAC;MACDsE,sBAAsBA,CAACvU,IAAI,EAAE;QAC3B,MAAM;UAAEoU;QAAY,CAAC,GAAGpU,IAAI,CAACE,IAAI;QACjC,IACE,CAAAkU,WAAW,oBAAXA,WAAW,CAAErT,IAAI,MAAK,kBAAkB,IAGxCuL,WAAW,CAAC8H,WAAW,CAAC,EACxB;UACA,IAAAE,qCAAsB,EAACtU,IAAI,CAAC;QAC9B;MACF,CAAC;MAEDwU,KAAKA,CAACxU,IAAI,EAAE6J,KAAK,EAAE;QACjBiK,UAAU,CAAC9T,IAAI,EAAE6J,KAAK,EAAExH,SAAS,CAAC;MACpC;IAAC,GAEEwR,sBAAsB;EAE7B,CAAC;AACH"}