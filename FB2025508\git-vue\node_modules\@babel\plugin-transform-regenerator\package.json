{"_from": "@babel/plugin-transform-regenerator@^7.23.3", "_id": "@babel/plugin-transform-regenerator@7.23.3", "_inBundle": false, "_integrity": "sha512-KP+75h0KghBMcVpuKisx3XTu9Ncut8Q8TuvGO4IhY+9D5DFEckQefOuIsB/gQ2tG71lCke4NMrtIPS8pOj18BQ==", "_location": "/@babel/plugin-transform-regenerator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-regenerator@^7.23.3", "name": "@babel/plugin-transform-regenerator", "escapedName": "@babel%2fplugin-transform-regenerator", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.23.3.tgz", "_shasum": "141afd4a2057298602069fce7f2dc5173e6c561c", "_spec": "@babel/plugin-transform-regenerator@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "regenerator-transform": "^0.15.2"}, "deprecated": false, "description": "Explode async and generator functions into a state machine.", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-regenerator", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-regenerator"}, "type": "commonjs", "version": "7.23.3"}