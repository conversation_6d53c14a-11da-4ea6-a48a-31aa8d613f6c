{"version": 3, "names": ["_babelPluginPolyfillCorejs", "require", "pluginCorejs3", "_pluginCorejs3", "default", "pluginsCompat", "createCorejs3Plugin", "corejs", "absoluteImports", "proposals", "rawVersion", "version", "Boolean", "Number", "Error", "JSON", "stringify", "api", "_", "filename", "method", "useBabelRuntime", "ext"], "sources": ["../src/core-js.ts"], "sourcesContent": ["// TODO: Consider removing babel-plugin-polyfill-corejs3 from here, and ask\n// users to explicitly enable it in their Babel configuration files.\n\nimport type { PluginAPI } from \"@babel/core\";\nimport _pluginCorejs3 from \"babel-plugin-polyfill-corejs3\";\n// eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\nconst pluginCorejs3 = (_pluginCorejs3.default ||\n  _pluginCorejs3) as typeof _pluginCorejs3.default;\n\nimport type { Options } from \"./index.ts\";\n\nconst pluginsCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\nexport function createCorejs3Plugin(\n  corejs: Options[\"corejs\"],\n  absoluteImports: boolean,\n) {\n  let proposals = false;\n  let rawVersion;\n\n  if (typeof corejs === \"object\" && corejs !== null) {\n    rawVersion = corejs.version;\n    proposals = Boolean(corejs.proposals);\n  } else {\n    rawVersion = corejs;\n  }\n\n  if (!rawVersion) return null;\n\n  const version = rawVersion ? Number(rawVersion) : false;\n\n  // TODO: Allow specifying minor versions\n  if (version !== 3) {\n    throw new Error(\n      `The \\`core-js\\` version must be 3, but got ${JSON.stringify(\n        rawVersion,\n      )}.`,\n    );\n  }\n\n  return (api: PluginAPI, _: {}, filename: string) =>\n    pluginCorejs3(\n      api,\n      {\n        method: \"usage-pure\",\n        proposals,\n        absoluteImports,\n        [pluginsCompat]: { useBabelRuntime: true, ext: \"\" },\n      },\n      filename,\n    );\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,0BAAA,GAAAC,OAAA;AAEA,MAAMC,aAAa,GAAIC,0BAAc,CAACC,OAAO,IAC3CD,0BAAgD;AAIlD,MAAME,aAAa,GAAG,8CAA8C;AAE7D,SAASC,mBAAmBA,CACjCC,MAAyB,EACzBC,eAAwB,EACxB;EACA,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,UAAU;EAEd,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjDG,UAAU,GAAGH,MAAM,CAACI,OAAO;IAC3BF,SAAS,GAAGG,OAAO,CAACL,MAAM,CAACE,SAAS,CAAC;EACvC,CAAC,MAAM;IACLC,UAAU,GAAGH,MAAM;EACrB;EAEA,IAAI,CAACG,UAAU,EAAE,OAAO,IAAI;EAE5B,MAAMC,OAAO,GAAGD,UAAU,GAAGG,MAAM,CAACH,UAAU,CAAC,GAAG,KAAK;EAGvD,IAAIC,OAAO,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIG,KAAK,CACZ,8CAA6CC,IAAI,CAACC,SAAS,CAC1DN,UACF,CAAE,GACJ,CAAC;EACH;EAEA,OAAO,CAACO,GAAc,EAAEC,CAAK,EAAEC,QAAgB,KAC7CjB,aAAa,CACXe,GAAG,EACH;IACEG,MAAM,EAAE,YAAY;IACpBX,SAAS;IACTD,eAAe;IACf,CAACH,aAAa,GAAG;MAAEgB,eAAe,EAAE,IAAI;MAAEC,GAAG,EAAE;IAAG;EACpD,CAAC,EACDH,QACF,CAAC;AACL"}