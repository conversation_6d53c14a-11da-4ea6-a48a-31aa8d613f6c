{"_from": "@babel/plugin-syntax-import-assertions@^7.23.3", "_id": "@babel/plugin-syntax-import-assertions@7.23.3", "_inBundle": false, "_integrity": "sha512-lPgDSU+SJLK3xmFDTV2ZRQAiM7UuUjGidwBywFavObCiZc1BeAAcMtHJKUya92hPHO+at63JJPLygilZard8jw==", "_location": "/@babel/plugin-syntax-import-assertions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-import-assertions@^7.23.3", "name": "@babel/plugin-syntax-import-assertions", "escapedName": "@babel%2fplugin-syntax-import-assertions", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.23.3.tgz", "_shasum": "9c05a7f592982aff1a2768260ad84bcd3f0c77fc", "_spec": "@babel/plugin-syntax-import-assertions@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Allow parsing of the module assertion attributes in the import statement", "devDependencies": {"@babel/core": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-import-assertions", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "type": "commonjs", "version": "7.23.3"}