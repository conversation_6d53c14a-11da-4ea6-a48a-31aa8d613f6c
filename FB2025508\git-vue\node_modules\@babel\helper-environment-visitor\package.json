{"_from": "@babel/helper-environment-visitor@^7.22.20", "_id": "@babel/helper-environment-visitor@7.22.20", "_inBundle": false, "_integrity": "sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==", "_location": "/@babel/helper-environment-visitor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-environment-visitor@^7.22.20", "name": "@babel/helper-environment-visitor", "escapedName": "@babel%2fhelper-environment-visitor", "scope": "@babel", "rawSpec": "^7.22.20", "saveSpec": null, "fetchSpec": "^7.22.20"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-module-transforms", "/@babel/helper-remap-async-to-generator", "/@babel/helper-replace-supers", "/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly", "/@babel/plugin-transform-async-generator-functions", "/@babel/plugin-transform-classes", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.20.tgz", "_shasum": "96159db61d34a29dba454c959f5ae4a649ba9167", "_spec": "@babel/helper-environment-visitor@^7.22.20", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-module-transforms", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Helper visitor to only visit nodes in the current 'this' context", "devDependencies": {"@babel/traverse": "^7.22.20", "@babel/types": "^7.22.19"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-environment-visitor", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-environment-visitor", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-environment-visitor"}, "type": "commonjs", "version": "7.22.20"}