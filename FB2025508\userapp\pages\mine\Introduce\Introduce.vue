<template>
	<view class="introduce-container">
		<!-- 标题 -->
		<view class="page-header">
			<view class="header-title">自我介绍</view>
		</view>
		
		<!-- 表单内容 -->
		<view class="form-content">
			<!-- 姓名 -->
			<view class="form-item">
				<view class="form-label">姓名<text class="required">*</text>:</view>
				<input class="form-input" type="text" v-model="formData.staffName" :disabled="formDisabled" />
			</view>
			
			<!-- 性别 -->
			<view class="form-item">
				<view class="form-label">性别<text class="required">*</text>:</view>
				<view class="gender-group">
					<view 
						class="gender-option" 
						:class="{'gender-selected': formData.gender === '0', 'gender-disabled': formDisabled}"
						@click="!formDisabled && selectGender('0')"
					>男</view>
					<view 
						class="gender-option" 
						:class="{'gender-selected': formData.gender === '1', 'gender-disabled': formDisabled}"
						@click="!formDisabled && selectGender('1')"
					>女</view>
				</view>
			</view>
			
			<!-- 手机号 -->
			<view class="form-item">
				<view class="form-label">手机号<text class="required">*</text>:</view>
				<input class="form-input" type="text" v-model="formData.phone" :disabled="formDisabled" />
			</view>
			
			<!-- 微信号 -->
			<view class="form-item">
				<view class="form-label">微信号:</view>
				<input class="form-input" type="text" v-model="formData.wchat" :disabled="formDisabled" />
			</view>
			
			<!-- 自我介绍 -->
			<view class="form-item">
				<view class="form-label">自我介绍<text class="required">*</text>:(限150字)</view>
				<textarea 
					class="form-textarea" 
					v-model="formData.introduce" 
					:disabled="formDisabled" 
					maxlength="150"
					placeholder="请输入自我个人简介、工作方法、沟通方式等"
				></textarea>
				<view class="char-count">{{formData.introduce ? formData.introduce.length : 0}}/150</view>
			</view>
			
			<!-- 提交日期时间 -->
			<view class="form-item" v-if="formData.updateTime">
				<view class="form-label">日期时间:</view>
				<input class="form-input date-input" type="text" :value="formatDateTime(formData.updateTime)" disabled />
			</view>
			
			<!-- 按钮组 -->
			<view class="btn-group">
				<button class="btn btn-reset" @click="resetForm">重置</button>
				<button class="btn btn-edit" @click="editForm" v-if="formDisabled">修改</button>
				<button class="btn btn-submit" @click="submitForm" :disabled="formDisabled" :class="{'btn-disabled': formDisabled}">提交</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { getStaffInfo, updateStaffIntroduce } from '@/api/work/usertotal'
	
	export default {
		data() {
			return {
				formData: {
					staffId: null,
					staffName: '',
					gender: '0',
					phone: '',
					wchat: '',
					introduce: '',
					updateTime: null
				},
				formDisabled: false,  // 表单是否禁用
				submitted: false      // 是否已提交过
			}
		},
		onLoad() {
			this.getStaffData()
		},
		methods: {
			// 获取店员信息
			async getStaffData() {
				try {
					const res = await getStaffInfo()
					if (res.code === 200 && res.data) {
						this.formData = {
							staffId: res.data.staff_id,
							staffName: res.data.staff_name || '',
							gender: res.data.gender || '0',
							phone: res.data.phone || '',
							wchat: res.data.wchat || '',
							introduce: res.data.introduce || '',
							updateTime: res.data.update_time
						}
						
						// 如果已有更新时间，说明已经提交过，表单禁用
						if (res.data.update_time && res.data.introduce) {
							this.formDisabled = true
							this.submitted = true
						}
					} else {
						uni.showToast({
							title: '获取个人信息失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('获取店员信息失败:', error)
					uni.showToast({
						title: '获取信息失败，请稍后重试',
						icon: 'none'
					})
				}
			},
			
			// 选择性别
			selectGender(gender) {
				this.formData.gender = gender
			},
			
			// 格式化日期时间
			formatDateTime(dateTimeStr) {
				if (!dateTimeStr) return ''
				
				const date = new Date(dateTimeStr)
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				const hours = String(date.getHours()).padStart(2, '0')
				const minutes = String(date.getMinutes()).padStart(2, '0')
				const seconds = String(date.getSeconds()).padStart(2, '0')
				
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
			},
			
			// 表单验证
			validateForm() {
				if (!this.formData.staffName) {
					uni.showToast({
						title: '请输入姓名',
						icon: 'none'
					})
					return false
				}
				
				if (!this.formData.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return false
				}
				
				// 手机号格式验证
				const phoneReg = /^1[3-9]\d{9}$/
				if (!phoneReg.test(this.formData.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return false
				}
				
				if (!this.formData.introduce) {
					uni.showToast({
						title: '请输入自我介绍',
						icon: 'none'
					})
					return false
				}
				
				if (this.formData.introduce.length > 150) {
					uni.showToast({
						title: '自我介绍不能超过150字',
						icon: 'none'
					})
					return false
				}
				
				return true
			},
			
			// 提交表单
			async submitForm() {
				if (this.formDisabled) return
				
				if (!this.validateForm()) return
				
				try {
					uni.showLoading({
						title: '提交中...'
					})
					
					const res = await updateStaffIntroduce({
						staffName: this.formData.staffName,
						gender: this.formData.gender,
						phone: this.formData.phone,
						wchat: this.formData.wchat,
						introduce: this.formData.introduce
					})
					
					uni.hideLoading()
					
					if (res.code === 200) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						
						// 重新获取数据
						this.getStaffData()
						
						// 禁用表单
						this.formDisabled = true
						this.submitted = true
					} else {
						uni.showToast({
							title: res.msg || '提交失败',
							icon: 'none'
						})
					}
				} catch (error) {
					uni.hideLoading()
					console.error('提交失败:', error)
					uni.showToast({
						title: '提交失败，请稍后重试',
						icon: 'none'
					})
				}
			},
			
			// 修改表单
			editForm() {
				this.formDisabled = false
			},
			
			// 重置表单
			resetForm() {
				if (this.submitted) {
					// 如果已提交过，只恢复到原始状态
					this.getStaffData()
					return
				}
				
				// 否则清空所有字段
				this.formData = {
					staffId: this.formData.staffId, // 保留ID
					staffName: '',
					gender: '0',
					phone: '',
					wchat: '',
					introduce: '',
					updateTime: null
				}
			}
		}
	}
</script>

<style>
	.introduce-container {
		padding: 20rpx;
	}
	
	.page-header {
		padding: 20rpx 0;
		border-bottom: 1px solid #EEEEEE;
	}
	
	.header-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.form-content {
		padding: 20rpx 0;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.form-label {
		margin-bottom: 10rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.required {
		color: #F56C6C;
		margin-right: 4rpx;
	}
	
	.form-input {
		width: 100%;
		height: 80rpx;
		border: 1px solid #DCDFE6;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #FFFFFF;
	}
	
	.form-input:disabled {
		background-color: #F5F7FA;
		color: #909399;
	}
	
	.gender-group {
		display: flex;
		flex-direction: row;
	}
	
	.gender-option {
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 30rpx;
		border: 1px solid #DCDFE6;
		margin-right: 20rpx;
		border-radius: 8rpx;
		text-align: center;
		font-size: 28rpx;
	}
	
	.gender-selected {
		background-color: #409EFF;
		color: #FFFFFF;
		border-color: #409EFF;
	}
	
	.gender-disabled {
		background-color: #F5F7FA;
		color: #909399;
		border-color: #DCDFE6;
	}
	
	.form-textarea {
		width: 100%;
		height: 200rpx;
		border: 1px solid #DCDFE6;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		background-color: #FFFFFF;
	}
	
	.form-textarea:disabled {
		background-color: #F5F7FA;
		color: #909399;
	}
	
	.char-count {
		text-align: right;
		font-size: 24rpx;
		color: #909399;
		margin-top: 10rpx;
	}
	
	.date-input {
		color: #909399;
	}
	
	.btn-group {
		display: flex;
		justify-content: space-around;
		margin-top: 50rpx;
	}
	
	.btn {
		width: 200rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 8rpx;
		font-size: 28rpx;
	}
	
	.btn-reset {
		background-color: #FFFFFF;
		color: #606266;
		border: 1px solid #DCDFE6;
	}
	
	.btn-edit {
		background-color: #E6A23C;
		color: #FFFFFF;
		border: 1px solid #E6A23C;
	}
	
	.btn-submit {
		background-color: #409EFF;
		color: #FFFFFF;
		border: 1px solid #409EFF;
	}
	
	.btn-disabled {
		background-color: #A0CFFF;
		border-color: #A0CFFF;
		cursor: not-allowed;
		opacity: 0.6;
	}
</style>
