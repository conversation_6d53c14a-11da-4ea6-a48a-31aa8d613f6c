<template>
  <view class="container">
    <!-- 头部标题 -->
    <view class="header">
      <view class="back-icon" @click="goBack">
        <text class="iconfont icon-left"></text>
      </view>
      <view class="title">店员用户列表</view>
      <view class="assign-button" @click="assignToStaff">
        <text>分配</text>
      </view>
    </view>

    <!-- 总计用户 -->
    <view class="total-info">
      <text>已选择 {{ selectedUsers.length }} 人</text>
    </view>

    <!-- 用户列表 -->
    <scroll-view scroll-y="true" class="user-list">
      <view v-for="(user, index) in userList" :key="index" 
            :class="['user-item', {'user-item-selected': isSelected(user)}]" 
            @click="toggleSelectUser(user)">
        <view class="user-info">
          <view class="user-name">{{ user.user_name || user.name }}</view>
          <view class="user-details">
            <text>{{ user.gender === '0' ? '男' : '女' }}</text>
            <text>{{ user.age }}</text>
            <text>{{ user.phone }}</text>
          </view>
        </view>
      </view>
      
      <!-- 空数据提示 -->
      <view v-if="userList.length === 0" class="empty-data">
        <text>暂无用户数据</text>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="finish-button" @click="finishAssignment">完成</button>
    </view>
  </view>
</template>

<script>
import { getStaffAssignedUsers } from '@/api/work/retrun_part.js'

export default {
  data() {
    return {
      userList: [], // 用户列表
      selectedUsers: [], // 已选择的用户
      sourceStaffId: null, // 来源店员ID
      shopId: '', // 店铺ID
      regionId: '' // 区域ID
    }
  },
  onLoad(options) {
    // 获取传入的参数
    if (options.staffId) {
      this.sourceStaffId = options.staffId
    }
    
    if (options.shopId) {
      this.shopId = options.shopId
    }
    
    if (options.regionId) {
      this.regionId = options.regionId
    }
    
    // 加载店员的用户列表
    this.loadStaffUsers()
  },
  // 页面每次显示时刷新数据
  onShow() {
    // 检查是否有分配标记
    const assignInfo = uni.getStorageSync('user_assigned');
    
    // 如果有分配标记或其他需要刷新的情况
    if (assignInfo || this.sourceStaffId) {
      console.log('userassigondev页面显示，重新加载用户列表');
      
      // 清空已选用户
      this.selectedUsers = [];
      
      // 重新加载用户列表
      if (this.sourceStaffId) {
        // 如果是从分配页面返回，且当前店员就是源店员，需要延迟刷新以确保数据更新
        if (assignInfo && assignInfo.sourceStaffId === this.sourceStaffId) {
          setTimeout(() => {
            this.loadStaffUsers();
          }, 300); // 短暂延迟确保后端数据已更新
        } else {
          this.loadStaffUsers();
        }
      }
      
      // 移除分配标记
      if (assignInfo) {
        uni.removeStorageSync('user_assigned');
      }
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 加载店员的用户列表
    loadStaffUsers() {
      if (!this.sourceStaffId) {
        uni.showToast({
          title: '参数错误',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({
        title: '加载中...'
      })
      
      // 调用API获取店员的用户列表
      getStaffAssignedUsers({
        staffId: this.sourceStaffId
      }).then(res => {
        uni.hideLoading()
        
        if (res.code === 200 && res.data) {
          // 处理用户数据
          this.userList = res.data.map(user => {
            return {
              ...user,
              gender: typeof user.gender === 'string' ? user.gender : (user.gender || '0').toString(),
              age: this.calculateAge(user.birth_date || user.birth_time)
            }
          })
          
          // 按性别排序（先男后女）
          this.userList.sort((a, b) => {
            return a.gender.localeCompare(b.gender)
          })
        } else {
          uni.showToast({
            title: res.msg || '获取用户列表失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        uni.hideLoading()
        console.error('获取用户列表异常:', err)
        uni.showToast({
          title: '获取用户列表异常',
          icon: 'none'
        })
      })
    },
    
    // 计算年龄
    calculateAge(birthDateString) {
      if (!birthDateString) return '--'
      
      try {
        const birthDate = new Date(birthDateString)
        const today = new Date()
        let age = today.getFullYear() - birthDate.getFullYear()
        const monthDiff = today.getMonth() - birthDate.getMonth()
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--
        }
        
        return age
      } catch (e) {
        return '--'
      }
    },
    
    // 判断用户是否被选中
    isSelected(user) {
      return this.selectedUsers.some(u => u.user_id === user.user_id)
    },
    
    // 切换用户选中状态
    toggleSelectUser(user) {
      const index = this.selectedUsers.findIndex(u => u.user_id === user.user_id)
      if (index === -1) {
        // 添加到选中列表
        this.selectedUsers.push(user)
      } else {
        // 从选中列表移除
        this.selectedUsers.splice(index, 1)
      }
    },
    
    // 分配给店员
    assignToStaff() {
      if (this.selectedUsers.length === 0) {
        uni.showToast({
          title: '请选择要分配的用户',
          icon: 'none'
        })
        return
      }
      
      // 跳转到店员选择页面，并排除当前店员
      uni.navigateTo({
        url: './shopassigndevex?shopId=' + this.shopId + 
             '&userIds=' + JSON.stringify(this.selectedUsers.map(user => user.user_id)) +
             '&excludeStaffId=' + this.sourceStaffId
      })
    },
    
    // 完成分配流程
    finishAssignment() {
      // 如果有分配操作发生，设置标记
      if (this.selectedUsers.length > 0) {
        uni.setStorageSync('user_assigned', {
          assigned: false,  // 未分配但需要刷新
          timestamp: Date.now()
        });
      }
      
      uni.navigateBack({
        delta: 2 // 返回到最初的页面
      })
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.back-icon {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.assign-button {
  position: absolute;
  right: 30rpx;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #1890ff;
  color: #ffffff;
}

.total-info {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-top: 2rpx;
  font-size: 28rpx;
}

.user-list {
  flex: 1;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eeeeee;
  background-color: #ffffff;
  transition: all 0.3s ease;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.user-item-selected {
  background-color: #e6f7ff;
  border-left: 8rpx solid #1890ff;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-details {
  display: flex;
  font-size: 28rpx;
  color: #666666;
}

.user-details text {
  margin-right: 20rpx;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999999;
  font-size: 28rpx;
}

.footer-buttons {
  padding: 30rpx;
}

.finish-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4b8ff0;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 40rpx;
  text-align: center;
}
</style>
