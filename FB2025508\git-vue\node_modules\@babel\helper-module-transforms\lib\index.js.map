{"version": 3, "names": ["_assert", "require", "_core", "_helperModuleImports", "_rewriteThis", "_rewriteLiveReferences", "_normalizeAndLoadMetadata", "Lazy", "_dynamicImport", "_getModuleName", "booleanLiteral", "callExpression", "cloneNode", "directive", "directiveLiteral", "expressionStatement", "identifier", "isIdentifier", "memberExpression", "stringLiteral", "valueToNode", "variableDeclaration", "variableDeclarator", "t", "exports", "getDynamicImportSource", "rewriteModuleStatementsAndPrepareHeader", "path", "exportName", "strict", "allowTopLevelThis", "strictMode", "noInterop", "importInterop", "lazy", "getWrapperPayload", "toGetWrapperPayload", "wrapReference", "esNamespaceOnly", "filename", "constantReexports", "arguments", "loose", "enumerableModuleMeta", "noIncompleteNsImportDetection", "validateImportInteropOption", "assert", "isModule", "node", "sourceType", "meta", "normalizeModuleAndLoadMetadata", "initializeReexports", "rewriteThis", "rewriteLiveReferences", "hasStrict", "directives", "some", "value", "unshiftContainer", "headers", "hasExports", "push", "buildESModuleHeader", "nameList", "buildExportNameListDeclaration", "exportNameListName", "name", "statement", "buildExportInitializationStatements", "ensureStatementsHoisted", "statements", "for<PERSON>ach", "header", "_blockHoist", "wrapInterop", "programPath", "expr", "type", "hub", "addHelper", "helper", "Error", "buildNamespaceInitStatements", "metadata", "sourceMetadata", "_wrapReference", "srcNamespaceId", "localName", "importsNamespace", "template", "NAME", "SOURCE", "srcNamespace", "wrap", "buildReexportsFromMeta", "reexportNamespace", "EXPORTS", "NAMESPACE", "reexportAll", "buildNamespaceReexport", "loc", "ReexportTemplate", "constant", "constantComputed", "spec", "_wrapReference2", "namespace", "stringSpecifiers", "Array", "from", "reexports", "importName", "NAMESPACE_IMPORT", "interop", "has", "astNodes", "EXPORT_NAME", "VERIFY_NAME_LIST", "EXPORTS_LIST", "exportedVars", "Object", "create", "data", "local", "values", "names", "hasReexport", "source", "keys", "length", "scope", "generateUidIdentifier", "default", "initStatements", "kind", "buildInitStatement", "reexportsStatements", "i", "sort", "a", "b", "results", "initStatement", "chunkSize", "uninitializedExportNames", "j", "buildUndefinedNode", "InitTemplate", "computed", "expression", "define", "exportNames", "initExpr", "reduce", "acc", "params", "VALUE"], "sources": ["../src/index.ts"], "sourcesContent": ["import assert from \"assert\";\nimport { template, types as t } from \"@babel/core\";\n\nimport { isModule } from \"@babel/helper-module-imports\";\n\nimport rewriteThis from \"./rewrite-this.ts\";\nimport rewriteLiveReferences from \"./rewrite-live-references.ts\";\nimport normalizeModuleAndLoadMetadata, {\n  hasExports,\n  isSideEffectImport,\n  validateImportInteropOption,\n} from \"./normalize-and-load-metadata.ts\";\nimport type {\n  ImportInterop,\n  InteropType,\n  ModuleMetadata,\n  SourceModuleMetadata,\n} from \"./normalize-and-load-metadata.ts\";\nimport * as Lazy from \"./lazy-modules.ts\";\nimport type { NodePath } from \"@babel/traverse\";\n\nconst {\n  booleanLiteral,\n  callExpression,\n  cloneNode,\n  directive,\n  directiveLiteral,\n  expressionStatement,\n  identifier,\n  isIdentifier,\n  memberExpression,\n  stringLiteral,\n  valueToNode,\n  variableDeclaration,\n  variableDeclarator,\n} = t;\n\nexport { buildDynamicImport } from \"./dynamic-import.ts\";\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.getDynamicImportSource =\n    // eslint-disable-next-line no-restricted-globals, import/extensions\n    require(\"./dynamic-import\").getDynamicImportSource;\n}\n\nexport { default as getModuleName } from \"./get-module-name.ts\";\nexport type { PluginOptions } from \"./get-module-name.ts\";\n\nexport { hasExports, isSideEffectImport, isModule, rewriteThis };\n\nexport interface RewriteModuleStatementsAndPrepareHeaderOptions {\n  exportName?: string;\n  strict: boolean;\n  allowTopLevelThis?: boolean;\n  strictMode: boolean;\n  loose?: boolean;\n  importInterop?: ImportInterop;\n  noInterop?: boolean;\n  lazy?: Lazy.Lazy;\n  getWrapperPayload?: (\n    source: string,\n    metadata: SourceModuleMetadata,\n    importNodes: t.Node[],\n  ) => unknown;\n  wrapReference?: (ref: t.Expression, payload: unknown) => t.Expression | null;\n  esNamespaceOnly?: boolean;\n  filename: string | undefined;\n  constantReexports?: boolean | void;\n  enumerableModuleMeta?: boolean | void;\n  noIncompleteNsImportDetection?: boolean | void;\n}\n\n/**\n * Perform all of the generic ES6 module rewriting needed to handle initial\n * module processing. This function will rewrite the majority of the given\n * program to reference the modules described by the returned metadata,\n * and returns a list of statements for use when initializing the module.\n */\nexport function rewriteModuleStatementsAndPrepareHeader(\n  path: NodePath<t.Program>,\n  {\n    exportName,\n    strict,\n    allowTopLevelThis,\n    strictMode,\n    noInterop,\n    importInterop = noInterop ? \"none\" : \"babel\",\n    // TODO(Babel 8): After that `lazy` implementation is moved to the CJS\n    // transform, remove this parameter.\n    lazy,\n    getWrapperPayload = Lazy.toGetWrapperPayload(lazy ?? false),\n    wrapReference = Lazy.wrapReference,\n    esNamespaceOnly,\n    filename,\n\n    constantReexports = process.env.BABEL_8_BREAKING\n      ? undefined\n      : arguments[1].loose,\n    enumerableModuleMeta = process.env.BABEL_8_BREAKING\n      ? undefined\n      : arguments[1].loose,\n    noIncompleteNsImportDetection,\n  }: RewriteModuleStatementsAndPrepareHeaderOptions,\n) {\n  validateImportInteropOption(importInterop);\n  assert(isModule(path), \"Cannot process module statements in a script\");\n  path.node.sourceType = \"script\";\n\n  const meta = normalizeModuleAndLoadMetadata(path, exportName, {\n    importInterop,\n    initializeReexports: constantReexports,\n    getWrapperPayload,\n    esNamespaceOnly,\n    filename,\n  });\n\n  if (!allowTopLevelThis) {\n    rewriteThis(path);\n  }\n\n  rewriteLiveReferences(path, meta, wrapReference);\n\n  if (strictMode !== false) {\n    const hasStrict = path.node.directives.some(directive => {\n      return directive.value.value === \"use strict\";\n    });\n    if (!hasStrict) {\n      path.unshiftContainer(\n        \"directives\",\n        directive(directiveLiteral(\"use strict\")),\n      );\n    }\n  }\n\n  const headers = [];\n  if (hasExports(meta) && !strict) {\n    headers.push(buildESModuleHeader(meta, enumerableModuleMeta));\n  }\n\n  const nameList = buildExportNameListDeclaration(path, meta);\n\n  if (nameList) {\n    meta.exportNameListName = nameList.name;\n    headers.push(nameList.statement);\n  }\n\n  // Create all of the statically known named exports.\n  headers.push(\n    ...buildExportInitializationStatements(\n      path,\n      meta,\n      wrapReference,\n      constantReexports,\n      noIncompleteNsImportDetection,\n    ),\n  );\n\n  return { meta, headers };\n}\n\n/**\n * Flag a set of statements as hoisted above all else so that module init\n * statements all run before user code.\n */\nexport function ensureStatementsHoisted(statements: t.Statement[]) {\n  // Force all of the header fields to be at the top of the file.\n  statements.forEach(header => {\n    // @ts-expect-error Fixme: handle _blockHoist property\n    header._blockHoist = 3;\n  });\n}\n\n/**\n * Given an expression for a standard import object, like \"require('foo')\",\n * wrap it in a call to the interop helpers based on the type.\n */\nexport function wrapInterop(\n  programPath: NodePath<t.Program>,\n  expr: t.Expression,\n  type: InteropType,\n): t.CallExpression {\n  if (type === \"none\") {\n    return null;\n  }\n\n  if (type === \"node-namespace\") {\n    return callExpression(programPath.hub.addHelper(\"interopRequireWildcard\"), [\n      expr,\n      booleanLiteral(true),\n    ]);\n  } else if (type === \"node-default\") {\n    return null;\n  }\n\n  let helper;\n  if (type === \"default\") {\n    helper = \"interopRequireDefault\";\n  } else if (type === \"namespace\") {\n    helper = \"interopRequireWildcard\";\n  } else {\n    throw new Error(`Unknown interop: ${type}`);\n  }\n\n  return callExpression(programPath.hub.addHelper(helper), [expr]);\n}\n\n/**\n * Create the runtime initialization statements for a given requested source.\n * These will initialize all of the runtime import/export logic that\n * can't be handled statically by the statements created by\n * buildExportInitializationStatements().\n */\nexport function buildNamespaceInitStatements(\n  metadata: ModuleMetadata,\n  sourceMetadata: SourceModuleMetadata,\n  constantReexports: boolean | void = false,\n  wrapReference: (\n    ref: t.Identifier,\n    payload: unknown,\n  ) => t.Expression | null = Lazy.wrapReference,\n) {\n  const statements = [];\n\n  const srcNamespaceId = identifier(sourceMetadata.name);\n\n  for (const localName of sourceMetadata.importsNamespace) {\n    if (localName === sourceMetadata.name) continue;\n\n    // Create and assign binding to namespace object\n    statements.push(\n      template.statement`var NAME = SOURCE;`({\n        NAME: localName,\n        SOURCE: cloneNode(srcNamespaceId),\n      }),\n    );\n  }\n\n  const srcNamespace =\n    wrapReference(srcNamespaceId, sourceMetadata.wrap) ?? srcNamespaceId;\n\n  if (constantReexports) {\n    statements.push(\n      ...buildReexportsFromMeta(metadata, sourceMetadata, true, wrapReference),\n    );\n  }\n  for (const exportName of sourceMetadata.reexportNamespace) {\n    // Assign export to namespace object.\n    statements.push(\n      (!t.isIdentifier(srcNamespace)\n        ? template.statement`\n            Object.defineProperty(EXPORTS, \"NAME\", {\n              enumerable: true,\n              get: function() {\n                return NAMESPACE;\n              }\n            });\n          `\n        : template.statement`EXPORTS.NAME = NAMESPACE;`)({\n        EXPORTS: metadata.exportName,\n        NAME: exportName,\n        NAMESPACE: cloneNode(srcNamespace),\n      }),\n    );\n  }\n  if (sourceMetadata.reexportAll) {\n    const statement = buildNamespaceReexport(\n      metadata,\n      cloneNode(srcNamespace),\n      constantReexports,\n    );\n    statement.loc = sourceMetadata.reexportAll.loc;\n\n    // Iterate props creating getter for each prop.\n    statements.push(statement);\n  }\n  return statements;\n}\n\nconst ReexportTemplate = {\n  constant: template.statement`EXPORTS.EXPORT_NAME = NAMESPACE_IMPORT;`,\n  constantComputed: template.statement`EXPORTS[\"EXPORT_NAME\"] = NAMESPACE_IMPORT;`,\n  spec: template.statement`\n    Object.defineProperty(EXPORTS, \"EXPORT_NAME\", {\n      enumerable: true,\n      get: function() {\n        return NAMESPACE_IMPORT;\n      },\n    });\n    `,\n};\n\nfunction buildReexportsFromMeta(\n  meta: ModuleMetadata,\n  metadata: SourceModuleMetadata,\n  constantReexports: boolean,\n  wrapReference: (ref: t.Expression, payload: unknown) => t.Expression | null,\n) {\n  let namespace: t.Expression = identifier(metadata.name);\n  namespace = wrapReference(namespace, metadata.wrap) ?? namespace;\n\n  const { stringSpecifiers } = meta;\n  return Array.from(metadata.reexports, ([exportName, importName]) => {\n    let NAMESPACE_IMPORT: t.Expression = cloneNode(namespace);\n    if (importName === \"default\" && metadata.interop === \"node-default\") {\n      // Nothing, it's ok as-is\n    } else if (stringSpecifiers.has(importName)) {\n      NAMESPACE_IMPORT = memberExpression(\n        NAMESPACE_IMPORT,\n        stringLiteral(importName),\n        true,\n      );\n    } else {\n      NAMESPACE_IMPORT = memberExpression(\n        NAMESPACE_IMPORT,\n        identifier(importName),\n      );\n    }\n    const astNodes = {\n      EXPORTS: meta.exportName,\n      EXPORT_NAME: exportName,\n      NAMESPACE_IMPORT,\n    };\n    if (constantReexports || isIdentifier(NAMESPACE_IMPORT)) {\n      if (stringSpecifiers.has(exportName)) {\n        return ReexportTemplate.constantComputed(astNodes);\n      } else {\n        return ReexportTemplate.constant(astNodes);\n      }\n    } else {\n      return ReexportTemplate.spec(astNodes);\n    }\n  });\n}\n\n/**\n * Build an \"__esModule\" header statement setting the property on a given object.\n */\nfunction buildESModuleHeader(\n  metadata: ModuleMetadata,\n  enumerableModuleMeta: boolean | void = false,\n) {\n  return (\n    enumerableModuleMeta\n      ? template.statement`\n        EXPORTS.__esModule = true;\n      `\n      : template.statement`\n        Object.defineProperty(EXPORTS, \"__esModule\", {\n          value: true,\n        });\n      `\n  )({ EXPORTS: metadata.exportName });\n}\n\n/**\n * Create a re-export initialization loop for a specific imported namespace.\n */\nfunction buildNamespaceReexport(\n  metadata: ModuleMetadata,\n  namespace: t.Expression,\n  constantReexports: boolean | void,\n) {\n  return (\n    constantReexports\n      ? template.statement`\n        Object.keys(NAMESPACE).forEach(function(key) {\n          if (key === \"default\" || key === \"__esModule\") return;\n          VERIFY_NAME_LIST;\n          if (key in EXPORTS && EXPORTS[key] === NAMESPACE[key]) return;\n\n          EXPORTS[key] = NAMESPACE[key];\n        });\n      `\n      : // Also skip already assigned bindings if they are strictly equal\n        // to be somewhat more spec-compliant when a file has multiple\n        // namespace re-exports that would cause a binding to be exported\n        // multiple times. However, multiple bindings of the same name that\n        // export the same primitive value are silently skipped\n        // (the spec requires an \"ambiguous bindings\" early error here).\n        template.statement`\n        Object.keys(NAMESPACE).forEach(function(key) {\n          if (key === \"default\" || key === \"__esModule\") return;\n          VERIFY_NAME_LIST;\n          if (key in EXPORTS && EXPORTS[key] === NAMESPACE[key]) return;\n\n          Object.defineProperty(EXPORTS, key, {\n            enumerable: true,\n            get: function() {\n              return NAMESPACE[key];\n            },\n          });\n        });\n    `\n  )({\n    NAMESPACE: namespace,\n    EXPORTS: metadata.exportName,\n    VERIFY_NAME_LIST: metadata.exportNameListName\n      ? template`\n            if (Object.prototype.hasOwnProperty.call(EXPORTS_LIST, key)) return;\n          `({ EXPORTS_LIST: metadata.exportNameListName })\n      : null,\n  });\n}\n\n/**\n * Build a statement declaring a variable that contains all of the exported\n * variable names in an object so they can easily be referenced from an\n * export * from statement to check for conflicts.\n */\nfunction buildExportNameListDeclaration(\n  programPath: NodePath,\n  metadata: ModuleMetadata,\n) {\n  const exportedVars = Object.create(null);\n  for (const data of metadata.local.values()) {\n    for (const name of data.names) {\n      exportedVars[name] = true;\n    }\n  }\n\n  let hasReexport = false;\n  for (const data of metadata.source.values()) {\n    for (const exportName of data.reexports.keys()) {\n      exportedVars[exportName] = true;\n    }\n    for (const exportName of data.reexportNamespace) {\n      exportedVars[exportName] = true;\n    }\n\n    hasReexport = hasReexport || !!data.reexportAll;\n  }\n\n  if (!hasReexport || Object.keys(exportedVars).length === 0) return null;\n\n  const name = programPath.scope.generateUidIdentifier(\"exportNames\");\n\n  delete exportedVars.default;\n\n  return {\n    name: name.name,\n    statement: variableDeclaration(\"var\", [\n      variableDeclarator(name, valueToNode(exportedVars)),\n    ]),\n  };\n}\n\n/**\n * Create a set of statements that will initialize all of the statically-known\n * export names with their expected values.\n */\nfunction buildExportInitializationStatements(\n  programPath: NodePath,\n  metadata: ModuleMetadata,\n  wrapReference: (ref: t.Expression, payload: unknown) => t.Expression | null,\n  constantReexports: boolean | void = false,\n  noIncompleteNsImportDetection: boolean | void = false,\n) {\n  const initStatements: Array<[string, t.Statement | null]> = [];\n\n  for (const [localName, data] of metadata.local) {\n    if (data.kind === \"import\") {\n      // No-open since these are explicitly set with the \"reexports\" block.\n    } else if (data.kind === \"hoisted\") {\n      initStatements.push([\n        // data.names is always of length 1 because a hoisted export\n        // name must be id of a function declaration\n        data.names[0],\n        buildInitStatement(metadata, data.names, identifier(localName)),\n      ]);\n    } else if (!noIncompleteNsImportDetection) {\n      for (const exportName of data.names) {\n        initStatements.push([exportName, null]);\n      }\n    }\n  }\n\n  for (const data of metadata.source.values()) {\n    if (!constantReexports) {\n      const reexportsStatements = buildReexportsFromMeta(\n        metadata,\n        data,\n        false,\n        wrapReference,\n      );\n      const reexports = [...data.reexports.keys()];\n      for (let i = 0; i < reexportsStatements.length; i++) {\n        initStatements.push([reexports[i], reexportsStatements[i]]);\n      }\n    }\n    if (!noIncompleteNsImportDetection) {\n      for (const exportName of data.reexportNamespace) {\n        initStatements.push([exportName, null]);\n      }\n    }\n  }\n\n  // https://tc39.es/ecma262/#sec-module-namespace-exotic-objects\n  // The [Exports] list is ordered as if an Array of those String values\n  // had been sorted using %Array.prototype.sort% using undefined as comparefn\n  initStatements.sort(([a], [b]) => {\n    if (a < b) return -1;\n    if (b < a) return 1;\n    return 0;\n  });\n\n  const results = [];\n  if (noIncompleteNsImportDetection) {\n    for (const [, initStatement] of initStatements) {\n      results.push(initStatement);\n    }\n  } else {\n    // We generate init statements (`exports.a = exports.b = ... = void 0`)\n    // for every 100 exported names to avoid deeply-nested AST structures.\n    const chunkSize = 100;\n    for (let i = 0; i < initStatements.length; i += chunkSize) {\n      let uninitializedExportNames = [];\n      for (let j = 0; j < chunkSize && i + j < initStatements.length; j++) {\n        const [exportName, initStatement] = initStatements[i + j];\n        if (initStatement !== null) {\n          if (uninitializedExportNames.length > 0) {\n            results.push(\n              buildInitStatement(\n                metadata,\n                uninitializedExportNames,\n                programPath.scope.buildUndefinedNode(),\n              ),\n            );\n            // reset after uninitializedExportNames has been transformed\n            // to init statements\n            uninitializedExportNames = [];\n          }\n          results.push(initStatement);\n        } else {\n          uninitializedExportNames.push(exportName);\n        }\n      }\n      if (uninitializedExportNames.length > 0) {\n        results.push(\n          buildInitStatement(\n            metadata,\n            uninitializedExportNames,\n            programPath.scope.buildUndefinedNode(),\n          ),\n        );\n      }\n    }\n  }\n\n  return results;\n}\n\n/**\n * Given a set of export names, create a set of nested assignments to\n * initialize them all to a given expression.\n */\nconst InitTemplate = {\n  computed: template.expression`EXPORTS[\"NAME\"] = VALUE`,\n  default: template.expression`EXPORTS.NAME = VALUE`,\n  define: template.expression`Object.defineProperty(EXPORTS, \"NAME\", { enumerable:true, value: void 0, writable: true })[\"NAME\"] = VALUE`,\n};\n\nfunction buildInitStatement(\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  initExpr: t.Expression,\n) {\n  const { stringSpecifiers, exportName: EXPORTS } = metadata;\n  return expressionStatement(\n    exportNames.reduce((acc, exportName) => {\n      const params = {\n        EXPORTS,\n        NAME: exportName,\n        VALUE: acc,\n      };\n\n      if (exportName === \"__proto__\") {\n        return InitTemplate.define(params);\n      }\n\n      if (stringSpecifiers.has(exportName)) {\n        return InitTemplate.computed(params);\n      }\n\n      return InitTemplate.default(params);\n    }, initExpr),\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,oBAAA,GAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,sBAAA,GAAAJ,OAAA;AACA,IAAAK,yBAAA,GAAAL,OAAA;AAWA,IAAAM,IAAA,GAAAN,OAAA;AAmBA,IAAAO,cAAA,GAAAP,OAAA;AASA,IAAAQ,cAAA,GAAAR,OAAA;AAzBA,MAAM;EACJS,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,SAAS;EACTC,gBAAgB;EAChBC,mBAAmB;EACnBC,UAAU;EACVC,YAAY;EACZC,gBAAgB;EAChBC,aAAa;EACbC,WAAW;EACXC,mBAAmB;EACnBC;AACF,CAAC,GAAGC,WAAC;AAI4D;EAE/DC,OAAO,CAACC,sBAAsB,GAE5BxB,OAAO,CAAC,kBAAkB,CAAC,CAACwB,sBAAsB;AACtD;AAmCO,SAASC,uCAAuCA,CACrDC,IAAyB,EACzB;EACEC,UAAU;EACVC,MAAM;EACNC,iBAAiB;EACjBC,UAAU;EACVC,SAAS;EACTC,aAAa,GAAGD,SAAS,GAAG,MAAM,GAAG,OAAO;EAG5CE,IAAI;EACJC,iBAAiB,GAAG5B,IAAI,CAAC6B,mBAAmB,CAACF,IAAI,WAAJA,IAAI,GAAI,KAAK,CAAC;EAC3DG,aAAa,GAAG9B,IAAI,CAAC8B,aAAa;EAClCC,eAAe;EACfC,QAAQ;EAERC,iBAAiB,GAEbC,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EACtBC,oBAAoB,GAEhBF,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK;EACtBE;AAC8C,CAAC,EACjD;EACA,IAAAC,qDAA2B,EAACZ,aAAa,CAAC;EAC1Ca,OAAM,CAAC,IAAAC,6BAAQ,EAACpB,IAAI,CAAC,EAAE,8CAA8C,CAAC;EACtEA,IAAI,CAACqB,IAAI,CAACC,UAAU,GAAG,QAAQ;EAE/B,MAAMC,IAAI,GAAG,IAAAC,iCAA8B,EAACxB,IAAI,EAAEC,UAAU,EAAE;IAC5DK,aAAa;IACbmB,mBAAmB,EAAEZ,iBAAiB;IACtCL,iBAAiB;IACjBG,eAAe;IACfC;EACF,CAAC,CAAC;EAEF,IAAI,CAACT,iBAAiB,EAAE;IACtB,IAAAuB,oBAAW,EAAC1B,IAAI,CAAC;EACnB;EAEA,IAAA2B,8BAAqB,EAAC3B,IAAI,EAAEuB,IAAI,EAAEb,aAAa,CAAC;EAEhD,IAAIN,UAAU,KAAK,KAAK,EAAE;IACxB,MAAMwB,SAAS,GAAG5B,IAAI,CAACqB,IAAI,CAACQ,UAAU,CAACC,IAAI,CAAC5C,SAAS,IAAI;MACvD,OAAOA,SAAS,CAAC6C,KAAK,CAACA,KAAK,KAAK,YAAY;IAC/C,CAAC,CAAC;IACF,IAAI,CAACH,SAAS,EAAE;MACd5B,IAAI,CAACgC,gBAAgB,CACnB,YAAY,EACZ9C,SAAS,CAACC,gBAAgB,CAAC,YAAY,CAAC,CAC1C,CAAC;IACH;EACF;EAEA,MAAM8C,OAAO,GAAG,EAAE;EAClB,IAAI,IAAAC,oCAAU,EAACX,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE;IAC/B+B,OAAO,CAACE,IAAI,CAACC,mBAAmB,CAACb,IAAI,EAAEP,oBAAoB,CAAC,CAAC;EAC/D;EAEA,MAAMqB,QAAQ,GAAGC,8BAA8B,CAACtC,IAAI,EAAEuB,IAAI,CAAC;EAE3D,IAAIc,QAAQ,EAAE;IACZd,IAAI,CAACgB,kBAAkB,GAAGF,QAAQ,CAACG,IAAI;IACvCP,OAAO,CAACE,IAAI,CAACE,QAAQ,CAACI,SAAS,CAAC;EAClC;EAGAR,OAAO,CAACE,IAAI,CACV,GAAGO,mCAAmC,CACpC1C,IAAI,EACJuB,IAAI,EACJb,aAAa,EACbG,iBAAiB,EACjBI,6BACF,CACF,CAAC;EAED,OAAO;IAAEM,IAAI;IAAEU;EAAQ,CAAC;AAC1B;AAMO,SAASU,uBAAuBA,CAACC,UAAyB,EAAE;EAEjEA,UAAU,CAACC,OAAO,CAACC,MAAM,IAAI;IAE3BA,MAAM,CAACC,WAAW,GAAG,CAAC;EACxB,CAAC,CAAC;AACJ;AAMO,SAASC,WAAWA,CACzBC,WAAgC,EAChCC,IAAkB,EAClBC,IAAiB,EACC;EAClB,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,KAAK,gBAAgB,EAAE;IAC7B,OAAOnE,cAAc,CAACiE,WAAW,CAACG,GAAG,CAACC,SAAS,CAAC,wBAAwB,CAAC,EAAE,CACzEH,IAAI,EACJnE,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC;EACJ,CAAC,MAAM,IAAIoE,IAAI,KAAK,cAAc,EAAE;IAClC,OAAO,IAAI;EACb;EAEA,IAAIG,MAAM;EACV,IAAIH,IAAI,KAAK,SAAS,EAAE;IACtBG,MAAM,GAAG,uBAAuB;EAClC,CAAC,MAAM,IAAIH,IAAI,KAAK,WAAW,EAAE;IAC/BG,MAAM,GAAG,wBAAwB;EACnC,CAAC,MAAM;IACL,MAAM,IAAIC,KAAK,CAAE,oBAAmBJ,IAAK,EAAC,CAAC;EAC7C;EAEA,OAAOnE,cAAc,CAACiE,WAAW,CAACG,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;AAClE;AAQO,SAASM,4BAA4BA,CAC1CC,QAAwB,EACxBC,cAAoC,EACpC7C,iBAAiC,GAAG,KAAK,EACzCH,aAGwB,GAAG9B,IAAI,CAAC8B,aAAa,EAC7C;EAAA,IAAAiD,cAAA;EACA,MAAMf,UAAU,GAAG,EAAE;EAErB,MAAMgB,cAAc,GAAGvE,UAAU,CAACqE,cAAc,CAAClB,IAAI,CAAC;EAEtD,KAAK,MAAMqB,SAAS,IAAIH,cAAc,CAACI,gBAAgB,EAAE;IACvD,IAAID,SAAS,KAAKH,cAAc,CAAClB,IAAI,EAAE;IAGvCI,UAAU,CAACT,IAAI,CACb4B,cAAQ,CAACtB,SAAU,oBAAmB,CAAC;MACrCuB,IAAI,EAAEH,SAAS;MACfI,MAAM,EAAEhF,SAAS,CAAC2E,cAAc;IAClC,CAAC,CACH,CAAC;EACH;EAEA,MAAMM,YAAY,IAAAP,cAAA,GAChBjD,aAAa,CAACkD,cAAc,EAAEF,cAAc,CAACS,IAAI,CAAC,YAAAR,cAAA,GAAIC,cAAc;EAEtE,IAAI/C,iBAAiB,EAAE;IACrB+B,UAAU,CAACT,IAAI,CACb,GAAGiC,sBAAsB,CAACX,QAAQ,EAAEC,cAAc,EAAE,IAAI,EAAEhD,aAAa,CACzE,CAAC;EACH;EACA,KAAK,MAAMT,UAAU,IAAIyD,cAAc,CAACW,iBAAiB,EAAE;IAEzDzB,UAAU,CAACT,IAAI,CACb,CAAC,CAACvC,WAAC,CAACN,YAAY,CAAC4E,YAAY,CAAC,GAC1BH,cAAQ,CAACtB,SAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GACDsB,cAAQ,CAACtB,SAAU,2BAA0B,EAAE;MACjD6B,OAAO,EAAEb,QAAQ,CAACxD,UAAU;MAC5B+D,IAAI,EAAE/D,UAAU;MAChBsE,SAAS,EAAEtF,SAAS,CAACiF,YAAY;IACnC,CAAC,CACH,CAAC;EACH;EACA,IAAIR,cAAc,CAACc,WAAW,EAAE;IAC9B,MAAM/B,SAAS,GAAGgC,sBAAsB,CACtChB,QAAQ,EACRxE,SAAS,CAACiF,YAAY,CAAC,EACvBrD,iBACF,CAAC;IACD4B,SAAS,CAACiC,GAAG,GAAGhB,cAAc,CAACc,WAAW,CAACE,GAAG;IAG9C9B,UAAU,CAACT,IAAI,CAACM,SAAS,CAAC;EAC5B;EACA,OAAOG,UAAU;AACnB;AAEA,MAAM+B,gBAAgB,GAAG;EACvBC,QAAQ,EAAEb,cAAQ,CAACtB,SAAU,yCAAwC;EACrEoC,gBAAgB,EAAEd,cAAQ,CAACtB,SAAU,4CAA2C;EAChFqC,IAAI,EAAEf,cAAQ,CAACtB,SAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,SAAS2B,sBAAsBA,CAC7B7C,IAAoB,EACpBkC,QAA8B,EAC9B5C,iBAA0B,EAC1BH,aAA2E,EAC3E;EAAA,IAAAqE,eAAA;EACA,IAAIC,SAAuB,GAAG3F,UAAU,CAACoE,QAAQ,CAACjB,IAAI,CAAC;EACvDwC,SAAS,IAAAD,eAAA,GAAGrE,aAAa,CAACsE,SAAS,EAAEvB,QAAQ,CAACU,IAAI,CAAC,YAAAY,eAAA,GAAIC,SAAS;EAEhE,MAAM;IAAEC;EAAiB,CAAC,GAAG1D,IAAI;EACjC,OAAO2D,KAAK,CAACC,IAAI,CAAC1B,QAAQ,CAAC2B,SAAS,EAAE,CAAC,CAACnF,UAAU,EAAEoF,UAAU,CAAC,KAAK;IAClE,IAAIC,gBAA8B,GAAGrG,SAAS,CAAC+F,SAAS,CAAC;IACzD,IAAIK,UAAU,KAAK,SAAS,IAAI5B,QAAQ,CAAC8B,OAAO,KAAK,cAAc,EAAE,CAErE,CAAC,MAAM,IAAIN,gBAAgB,CAACO,GAAG,CAACH,UAAU,CAAC,EAAE;MAC3CC,gBAAgB,GAAG/F,gBAAgB,CACjC+F,gBAAgB,EAChB9F,aAAa,CAAC6F,UAAU,CAAC,EACzB,IACF,CAAC;IACH,CAAC,MAAM;MACLC,gBAAgB,GAAG/F,gBAAgB,CACjC+F,gBAAgB,EAChBjG,UAAU,CAACgG,UAAU,CACvB,CAAC;IACH;IACA,MAAMI,QAAQ,GAAG;MACfnB,OAAO,EAAE/C,IAAI,CAACtB,UAAU;MACxByF,WAAW,EAAEzF,UAAU;MACvBqF;IACF,CAAC;IACD,IAAIzE,iBAAiB,IAAIvB,YAAY,CAACgG,gBAAgB,CAAC,EAAE;MACvD,IAAIL,gBAAgB,CAACO,GAAG,CAACvF,UAAU,CAAC,EAAE;QACpC,OAAO0E,gBAAgB,CAACE,gBAAgB,CAACY,QAAQ,CAAC;MACpD,CAAC,MAAM;QACL,OAAOd,gBAAgB,CAACC,QAAQ,CAACa,QAAQ,CAAC;MAC5C;IACF,CAAC,MAAM;MACL,OAAOd,gBAAgB,CAACG,IAAI,CAACW,QAAQ,CAAC;IACxC;EACF,CAAC,CAAC;AACJ;AAKA,SAASrD,mBAAmBA,CAC1BqB,QAAwB,EACxBzC,oBAAoC,GAAG,KAAK,EAC5C;EACA,OAAO,CACLA,oBAAoB,GAChB+C,cAAQ,CAACtB,SAAU;AAC3B;AACA,OAAO,GACCsB,cAAQ,CAACtB,SAAU;AAC3B;AACA;AACA;AACA,OAAO,EACH;IAAE6B,OAAO,EAAEb,QAAQ,CAACxD;EAAW,CAAC,CAAC;AACrC;AAKA,SAASwE,sBAAsBA,CAC7BhB,QAAwB,EACxBuB,SAAuB,EACvBnE,iBAAiC,EACjC;EACA,OAAO,CACLA,iBAAiB,GACbkD,cAAQ,CAACtB,SAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,GAOCsB,cAAQ,CAACtB,SAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,EACD;IACA8B,SAAS,EAAES,SAAS;IACpBV,OAAO,EAAEb,QAAQ,CAACxD,UAAU;IAC5B0F,gBAAgB,EAAElC,QAAQ,CAAClB,kBAAkB,GACzC,IAAAwB,cAAQ,CAAC;AACjB;AACA,WAAW,CAAC;MAAE6B,YAAY,EAAEnC,QAAQ,CAAClB;IAAmB,CAAC,CAAC,GAClD;EACN,CAAC,CAAC;AACJ;AAOA,SAASD,8BAA8BA,CACrCW,WAAqB,EACrBQ,QAAwB,EACxB;EACA,MAAMoC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACxC,KAAK,MAAMC,IAAI,IAAIvC,QAAQ,CAACwC,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE;IAC1C,KAAK,MAAM1D,IAAI,IAAIwD,IAAI,CAACG,KAAK,EAAE;MAC7BN,YAAY,CAACrD,IAAI,CAAC,GAAG,IAAI;IAC3B;EACF;EAEA,IAAI4D,WAAW,GAAG,KAAK;EACvB,KAAK,MAAMJ,IAAI,IAAIvC,QAAQ,CAAC4C,MAAM,CAACH,MAAM,CAAC,CAAC,EAAE;IAC3C,KAAK,MAAMjG,UAAU,IAAI+F,IAAI,CAACZ,SAAS,CAACkB,IAAI,CAAC,CAAC,EAAE;MAC9CT,YAAY,CAAC5F,UAAU,CAAC,GAAG,IAAI;IACjC;IACA,KAAK,MAAMA,UAAU,IAAI+F,IAAI,CAAC3B,iBAAiB,EAAE;MAC/CwB,YAAY,CAAC5F,UAAU,CAAC,GAAG,IAAI;IACjC;IAEAmG,WAAW,GAAGA,WAAW,IAAI,CAAC,CAACJ,IAAI,CAACxB,WAAW;EACjD;EAEA,IAAI,CAAC4B,WAAW,IAAIN,MAAM,CAACQ,IAAI,CAACT,YAAY,CAAC,CAACU,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEvE,MAAM/D,IAAI,GAAGS,WAAW,CAACuD,KAAK,CAACC,qBAAqB,CAAC,aAAa,CAAC;EAEnE,OAAOZ,YAAY,CAACa,OAAO;EAE3B,OAAO;IACLlE,IAAI,EAAEA,IAAI,CAACA,IAAI;IACfC,SAAS,EAAE/C,mBAAmB,CAAC,KAAK,EAAE,CACpCC,kBAAkB,CAAC6C,IAAI,EAAE/C,WAAW,CAACoG,YAAY,CAAC,CAAC,CACpD;EACH,CAAC;AACH;AAMA,SAASnD,mCAAmCA,CAC1CO,WAAqB,EACrBQ,QAAwB,EACxB/C,aAA2E,EAC3EG,iBAAiC,GAAG,KAAK,EACzCI,6BAA6C,GAAG,KAAK,EACrD;EACA,MAAM0F,cAAmD,GAAG,EAAE;EAE9D,KAAK,MAAM,CAAC9C,SAAS,EAAEmC,IAAI,CAAC,IAAIvC,QAAQ,CAACwC,KAAK,EAAE;IAC9C,IAAID,IAAI,CAACY,IAAI,KAAK,QAAQ,EAAE,CAE5B,CAAC,MAAM,IAAIZ,IAAI,CAACY,IAAI,KAAK,SAAS,EAAE;MAClCD,cAAc,CAACxE,IAAI,CAAC,CAGlB6D,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,EACbU,kBAAkB,CAACpD,QAAQ,EAAEuC,IAAI,CAACG,KAAK,EAAE9G,UAAU,CAACwE,SAAS,CAAC,CAAC,CAChE,CAAC;IACJ,CAAC,MAAM,IAAI,CAAC5C,6BAA6B,EAAE;MACzC,KAAK,MAAMhB,UAAU,IAAI+F,IAAI,CAACG,KAAK,EAAE;QACnCQ,cAAc,CAACxE,IAAI,CAAC,CAAClC,UAAU,EAAE,IAAI,CAAC,CAAC;MACzC;IACF;EACF;EAEA,KAAK,MAAM+F,IAAI,IAAIvC,QAAQ,CAAC4C,MAAM,CAACH,MAAM,CAAC,CAAC,EAAE;IAC3C,IAAI,CAACrF,iBAAiB,EAAE;MACtB,MAAMiG,mBAAmB,GAAG1C,sBAAsB,CAChDX,QAAQ,EACRuC,IAAI,EACJ,KAAK,EACLtF,aACF,CAAC;MACD,MAAM0E,SAAS,GAAG,CAAC,GAAGY,IAAI,CAACZ,SAAS,CAACkB,IAAI,CAAC,CAAC,CAAC;MAC5C,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,mBAAmB,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;QACnDJ,cAAc,CAACxE,IAAI,CAAC,CAACiD,SAAS,CAAC2B,CAAC,CAAC,EAAED,mBAAmB,CAACC,CAAC,CAAC,CAAC,CAAC;MAC7D;IACF;IACA,IAAI,CAAC9F,6BAA6B,EAAE;MAClC,KAAK,MAAMhB,UAAU,IAAI+F,IAAI,CAAC3B,iBAAiB,EAAE;QAC/CsC,cAAc,CAACxE,IAAI,CAAC,CAAClC,UAAU,EAAE,IAAI,CAAC,CAAC;MACzC;IACF;EACF;EAKA0G,cAAc,CAACK,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAK;IAChC,IAAID,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;IACpB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC;IACnB,OAAO,CAAC;EACV,CAAC,CAAC;EAEF,MAAME,OAAO,GAAG,EAAE;EAClB,IAAIlG,6BAA6B,EAAE;IACjC,KAAK,MAAM,GAAGmG,aAAa,CAAC,IAAIT,cAAc,EAAE;MAC9CQ,OAAO,CAAChF,IAAI,CAACiF,aAAa,CAAC;IAC7B;EACF,CAAC,MAAM;IAGL,MAAMC,SAAS,GAAG,GAAG;IACrB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACJ,MAAM,EAAEQ,CAAC,IAAIM,SAAS,EAAE;MACzD,IAAIC,wBAAwB,GAAG,EAAE;MACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,IAAIN,CAAC,GAAGQ,CAAC,GAAGZ,cAAc,CAACJ,MAAM,EAAEgB,CAAC,EAAE,EAAE;QACnE,MAAM,CAACtH,UAAU,EAAEmH,aAAa,CAAC,GAAGT,cAAc,CAACI,CAAC,GAAGQ,CAAC,CAAC;QACzD,IAAIH,aAAa,KAAK,IAAI,EAAE;UAC1B,IAAIE,wBAAwB,CAACf,MAAM,GAAG,CAAC,EAAE;YACvCY,OAAO,CAAChF,IAAI,CACV0E,kBAAkB,CAChBpD,QAAQ,EACR6D,wBAAwB,EACxBrE,WAAW,CAACuD,KAAK,CAACgB,kBAAkB,CAAC,CACvC,CACF,CAAC;YAGDF,wBAAwB,GAAG,EAAE;UAC/B;UACAH,OAAO,CAAChF,IAAI,CAACiF,aAAa,CAAC;QAC7B,CAAC,MAAM;UACLE,wBAAwB,CAACnF,IAAI,CAAClC,UAAU,CAAC;QAC3C;MACF;MACA,IAAIqH,wBAAwB,CAACf,MAAM,GAAG,CAAC,EAAE;QACvCY,OAAO,CAAChF,IAAI,CACV0E,kBAAkB,CAChBpD,QAAQ,EACR6D,wBAAwB,EACxBrE,WAAW,CAACuD,KAAK,CAACgB,kBAAkB,CAAC,CACvC,CACF,CAAC;MACH;IACF;EACF;EAEA,OAAOL,OAAO;AAChB;AAMA,MAAMM,YAAY,GAAG;EACnBC,QAAQ,EAAE3D,cAAQ,CAAC4D,UAAW,yBAAwB;EACtDjB,OAAO,EAAE3C,cAAQ,CAAC4D,UAAW,sBAAqB;EAClDC,MAAM,EAAE7D,cAAQ,CAAC4D,UAAW;AAC9B,CAAC;AAED,SAASd,kBAAkBA,CACzBpD,QAAwB,EACxBoE,WAAqB,EACrBC,QAAsB,EACtB;EACA,MAAM;IAAE7C,gBAAgB;IAAEhF,UAAU,EAAEqE;EAAQ,CAAC,GAAGb,QAAQ;EAC1D,OAAOrE,mBAAmB,CACxByI,WAAW,CAACE,MAAM,CAAC,CAACC,GAAG,EAAE/H,UAAU,KAAK;IACtC,MAAMgI,MAAM,GAAG;MACb3D,OAAO;MACPN,IAAI,EAAE/D,UAAU;MAChBiI,KAAK,EAAEF;IACT,CAAC;IAED,IAAI/H,UAAU,KAAK,WAAW,EAAE;MAC9B,OAAOwH,YAAY,CAACG,MAAM,CAACK,MAAM,CAAC;IACpC;IAEA,IAAIhD,gBAAgB,CAACO,GAAG,CAACvF,UAAU,CAAC,EAAE;MACpC,OAAOwH,YAAY,CAACC,QAAQ,CAACO,MAAM,CAAC;IACtC;IAEA,OAAOR,YAAY,CAACf,OAAO,CAACuB,MAAM,CAAC;EACrC,CAAC,EAAEH,QAAQ,CACb,CAAC;AACH"}