{"_from": "@babel/code-frame@^7.23.5", "_id": "@babel/code-frame@7.23.5", "_inBundle": false, "_integrity": "sha512-CgH3s1a96LipHCmSUmYFPwY7MNx8C3avkq7i4Wl3cfa662ldtUe4VM1TPXX70pfmrlWTb6jLqTYrZyT2ZTJBgA==", "_location": "/@babel/code-frame", "_phantomChildren": {"ansi-styles": "3.2.1", "escape-string-regexp": "1.0.5", "supports-color": "5.5.0"}, "_requested": {"type": "range", "registry": true, "raw": "@babel/code-frame@^7.23.5", "name": "@babel/code-frame", "escapedName": "@babel%2fcode-frame", "scope": "@babel", "rawSpec": "^7.23.5", "saveSpec": null, "fetchSpec": "^7.23.5"}, "_requiredBy": ["/@babel/core", "/@babel/template", "/@babel/traverse", "/@vue/babel-plugin-resolve-type", "/babel-eslint", "/eslint", "/parse-json"], "_resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.23.5.tgz", "_shasum": "9009b69a8c602293476ad598ff53e4562e15c244", "_spec": "@babel/code-frame@^7.23.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/highlight": "^7.23.4", "chalk": "^2.4.2"}, "deprecated": false, "description": "Generate errors that contain a code frame that point to source locations.", "devDependencies": {"import-meta-resolve": "^4.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "license": "MIT", "main": "./lib/index.js", "name": "@babel/code-frame", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-code-frame"}, "type": "commonjs", "version": "7.23.5"}