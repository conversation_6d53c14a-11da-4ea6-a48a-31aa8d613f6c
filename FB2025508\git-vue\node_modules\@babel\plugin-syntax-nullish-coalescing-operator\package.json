{"_from": "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3", "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3", "_inBundle": false, "_integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==", "_location": "/@babel/plugin-syntax-nullish-coalescing-operator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3", "name": "@babel/plugin-syntax-nullish-coalescing-operator", "escapedName": "@babel%2fplugin-syntax-nullish-coalescing-operator", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@babel/plugin-transform-nullish-coalescing-operator", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "_shasum": "167ed70368886081f74b5c36c65a88c03b66d1a9", "_spec": "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "deprecated": false, "description": "Allow parsing of the nullish-coalescing operator", "devDependencies": {"@babel/core": "^7.8.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-nullish-coalescing-operator", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "version": "7.8.3"}