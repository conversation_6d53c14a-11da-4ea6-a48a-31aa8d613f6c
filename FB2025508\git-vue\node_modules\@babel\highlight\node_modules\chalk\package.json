{"_from": "chalk@^2.4.2", "_id": "chalk@2.4.2", "_inBundle": false, "_integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "_location": "/@babel/highlight/chalk", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "chalk@^2.4.2", "name": "chalk", "escapedName": "chalk", "rawSpec": "^2.4.2", "saveSpec": null, "fetchSpec": "^2.4.2"}, "_requiredBy": ["/@babel/highlight"], "_resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "_shasum": "cd42541677a54333cf541a49108c1432b44c9424", "_spec": "chalk@^2.4.2", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\highlight", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "bundleDependencies": false, "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "deprecated": false, "description": "Terminal string styling done right", "devDependencies": {"ava": "*", "coveralls": "^3.0.0", "execa": "^0.9.0", "flow-bin": "^0.68.0", "import-fresh": "^2.0.0", "matcha": "^0.7.0", "nyc": "^11.0.2", "resolve-from": "^4.0.0", "typescript": "^2.5.3", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "templates.js", "types/index.d.ts", "index.js.flow"], "homepage": "https://github.com/chalk/chalk#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "chalk", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "scripts": {"bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava"}, "types": "types/index.d.ts", "version": "2.4.2", "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}}