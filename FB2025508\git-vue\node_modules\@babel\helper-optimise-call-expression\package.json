{"_from": "@babel/helper-optimise-call-expression@^7.22.5", "_id": "@babel/helper-optimise-call-expression@7.22.5", "_inBundle": false, "_integrity": "sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==", "_location": "/@babel/helper-optimise-call-expression", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-optimise-call-expression@^7.22.5", "name": "@babel/helper-optimise-call-expression", "escapedName": "@babel%2fhelper-optimise-call-expression", "scope": "@babel", "rawSpec": "^7.22.5", "saveSpec": null, "fetchSpec": "^7.22.5"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-replace-supers"], "_resolved": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz", "_shasum": "f21531a9ccbff644fdd156b4077c16ff0c3f609e", "_spec": "@babel/helper-optimise-call-expression@^7.22.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.5"}, "deprecated": false, "description": "Helper function to optimise call expression", "devDependencies": {"@babel/generator": "^7.22.5", "@babel/parser": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-optimise-call-expression", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-optimise-call-expression"}, "type": "commonjs", "version": "7.22.5"}