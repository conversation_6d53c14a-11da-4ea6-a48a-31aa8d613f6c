<template>
	<view class="container">
		<view class="header">
			<view class="title">店长意见反馈</view>
		</view>
		
		<view class="content">
			<view class="section">
				<view class="section-title">投诉与建议</view>
				
				<view class="form-item">
					<view class="label required">店长姓名</view>
					<input 
						class="input" 
						type="text" 
						v-model="formData.managerName" 
						placeholder="请输入店长姓名" 
						:disabled="isSubmitted && !isEditing"
					/>
				</view>
				
				<view class="form-item">
					<view class="label required">联系电话</view>
					<input 
						class="input" 
						type="text" 
						v-model="formData.contactPhone" 
						placeholder="请输入联系电话" 
						maxlength="11"
						:disabled="isSubmitted && !isEditing"
					/>
				</view>
				
				<view class="form-item">
					<view class="label required">投诉/建议内容描述 <text class="limit">(限100字)</text></view>
					<textarea 
						class="textarea" 
						v-model="formData.content" 
						placeholder="请输入投诉或建议内容" 
						maxlength="100"
						:disabled="isSubmitted && !isEditing"
					></textarea>
					<view class="count">{{formData.content.length || 0}}/100</view>
				</view>
				
				<view class="form-item">
					<view class="label required">日期时间</view>
					<view class="datetime-display">{{formattedDateTime}}</view>
				</view>
			</view>
			
			<view class="action-buttons">
				<button class="btn reset" @click="resetForm">重置</button>
				<button
					v-if="isSubmitted"
					class="btn modify"
					@click="toggleEditing"
				>{{isEditing ? '取消修改' : '修改'}}</button>
				<button
					class="btn submit"
					:class="{disabled: isSubmitted && !isEditing}"
					@click="submitForm"
					:disabled="isSubmitted && !isEditing"
				>{{ isEditing && isSubmitted ? '保存修改' : '提交' }}</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getManagerFeedback, submitManagerFeedback, updateManagerFeedback } from '@/api/work/retrun_part.js';

export default {
	data() {
		return {
			formData: {
				feedbackId: null,
				managerName: '',
				contactPhone: '',
				content: '',
				feedbackTime: new Date()
			},
			isSubmitted: false,
			isEditing: false,
			loading: false,
			originalFormData: null // 保存原始表单数据用于比较
		};
	},
	computed: {
		formattedDateTime() {
			const dt = this.formData.feedbackTime;
			if (!(dt instanceof Date)) {
				return '';
			}
			
			const year = dt.getFullYear();
			const month = String(dt.getMonth() + 1).padStart(2, '0');
			const day = String(dt.getDate()).padStart(2, '0');
			const hour = String(dt.getHours()).padStart(2, '0');
			const minute = String(dt.getMinutes()).padStart(2, '0');
			const second = String(dt.getSeconds()).padStart(2, '0');
			
			return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
		}
	},
	onLoad() {
		// 页面加载时先加载本地存储的数据
		this.loadFormDataFromStorage();
	},
	onShow() {
		// 获取已有反馈数据
		this.fetchFeedbackData();
		// 如果表单已提交，保存当前状态到本地存储
		if (this.isSubmitted) {
			this.saveFormDataToStorage();
		}
	},
	onHide() {
		// 页面隐藏时保存当前状态
		if (this.formData.managerName || this.formData.contactPhone || this.formData.content) {
			this.saveFormDataToStorage();
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		async fetchFeedbackData() {
			try {
				uni.showLoading({
					title: '加载中...'
				});
				
				const res = await getManagerFeedback({});
				if (res.code === 200 && res.data && Object.keys(res.data).length > 0) {
					const data = res.data;
					this.formData = {
						feedbackId: data.feedback_id,
						managerName: data.manager_name,
						contactPhone: data.contact_phone,
						content: data.content,
						feedbackTime: new Date(data.feedback_time)
					};
					this.isSubmitted = true;
				}
			} catch (error) {
				console.error('获取反馈信息失败', error);
				uni.showToast({
					title: '获取反馈信息失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		validateForm() {
			if (!this.formData.managerName) {
				uni.showToast({
					title: '请输入店长姓名',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.contactPhone) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return false;
			}
			
			if (!/^1[3-9]\d{9}$/.test(this.formData.contactPhone)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.content) {
				uni.showToast({
					title: '请输入投诉/建议内容',
					icon: 'none'
				});
				return false;
			}
			
			if (this.formData.content.length > 100) {
				uni.showToast({
					title: '投诉/建议内容不能超过100字',
					icon: 'none'
				});
				return false;
			}
			
			return true;
		},
		async submitForm() {
			if (!this.validateForm()) return;
			
			if (this.loading) return;
			this.loading = true;
			
			try {
				uni.showLoading({
					title: '提交中...'
				});
				
				let res;
				if (this.isSubmitted && this.isEditing) {
					// 更新反馈
					res = await updateManagerFeedback({
						feedbackId: this.formData.feedbackId,
						managerName: this.formData.managerName,
						contactPhone: this.formData.contactPhone,
						content: this.formData.content
					});
					
					if (res.code === 200) {
						this.isEditing = false;
						this.formData.feedbackTime = new Date();
						// 保存原始数据
						this.originalFormData = JSON.parse(JSON.stringify(this.formData));
						// 保存到本地存储
						this.saveFormDataToStorage();
						uni.showToast({
							title: '更新成功',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.msg || '更新失败',
							icon: 'none'
						});
					}
				} else {
					// 新提交反馈
					res = await submitManagerFeedback({
						managerName: this.formData.managerName,
						contactPhone: this.formData.contactPhone,
						content: this.formData.content
					});
					
					if (res.code === 200) {
						this.formData.feedbackId = res.data;
						this.isSubmitted = true;
						this.formData.feedbackTime = new Date();
						// 保存原始数据
						this.originalFormData = JSON.parse(JSON.stringify(this.formData));
						// 保存到本地存储
						this.saveFormDataToStorage();
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.msg || '提交失败',
							icon: 'none'
						});
					}
				}
			} catch (error) {
				console.error('提交失败', error);
				uni.showToast({
					title: '提交失败，请稍后重试',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
				this.loading = false;
			}
		},
		resetForm() {
			uni.showModal({
				title: '提示',
				content: '确定要重置表单吗？这将清空所有已填写的内容。',
				success: res => {
					if (res.confirm) {
						// 重置表单数据
						this.formData = {
							feedbackId: null,
							managerName: '',
							contactPhone: '',
							content: '',
							feedbackTime: new Date()
						};

						// 重置状态，允许编辑
						this.isSubmitted = false;
						this.isEditing = true;
						this.originalFormData = null;

						// 清除本地存储
						this.clearFormDataFromStorage();

						uni.showToast({
							title: '表单已重置',
							icon: 'success'
						});
					}
				}
			});
		},
		toggleEditing() {
			if (!this.isSubmitted) return;
			
			this.isEditing = !this.isEditing;
			if (!this.isEditing) {
				// 取消编辑，恢复原数据
				this.fetchFeedbackData();
			}
		}
	}
};
</script>

<style scoped>
.container {
	background-color: #f7f7f7;
	min-height: 100vh;
}

.header {
	display: flex;
	align-items: center;
	height: 90rpx;
	background-color: #ffffff;
	padding: 0 30rpx;
	position: relative;
	border-bottom: 1rpx solid #eeeeee;
}

.back {
	position: absolute;
	left: 30rpx;
	z-index: 10;
}

.title {
	flex: 1;
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
}

.content {
	padding: 30rpx;
}

.section {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.form-item {
	margin-bottom: 30rpx;
	position: relative;
}

.label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.required::before {
	content: '*';
	color: #e43d33;
	margin-right: 4rpx;
}

.limit {
	font-size: 24rpx;
	color: #999;
}

.input {
	border: 1rpx solid #ddd;
	height: 80rpx;
	border-radius: 8rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	background-color: #fff;
}

.textarea {
	border: 1rpx solid #ddd;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	width: auto;
	height: 200rpx;
	background-color: #fff;
}

.count {
	position: absolute;
	right: 10rpx;
	bottom: 10rpx;
	font-size: 24rpx;
	color: #999;
}

.datetime-display {
	border: 1rpx solid #ddd;
	height: 80rpx;
	border-radius: 8rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	line-height: 80rpx;
	color: #e32222;
	background-color: #f7f7f7;
}

.action-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 30rpx;
}

.btn {
	width: 30%;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 28rpx;
}

.reset {
	background-color: #ffffff;
	color: #333333;
	border: 1rpx solid #dddddd;
}

.modify {
	background-color: #ff9900;
	color: #ffffff;
}

.submit {
	background-color: #0066ff;
	color: #ffffff;
}

.submit.disabled {
	background-color: #cccccc;
	cursor: not-allowed;
}

/* 灰显已提交的表单内容 */
.input:disabled,
.textarea:disabled {
	background-color: #f7f7f7;
	color: #999;
}
</style>
