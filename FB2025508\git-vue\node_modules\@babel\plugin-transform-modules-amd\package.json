{"_from": "@babel/plugin-transform-modules-amd@^7.23.3", "_id": "@babel/plugin-transform-modules-amd@7.23.3", "_inBundle": false, "_integrity": "sha512-vJYQGxeKM4t8hYCKVBlZX/gtIY2I7mRGFNcm85sgXGMTBcoV3QdVtdpbcWEbzbfUIUZKwvgFT82mRvaQIebZzw==", "_location": "/@babel/plugin-transform-modules-amd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-modules-amd@^7.23.3", "name": "@babel/plugin-transform-modules-amd", "escapedName": "@babel%2fplugin-transform-modules-amd", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.23.3.tgz", "_shasum": "e19b55436a1416829df0a1afc495deedfae17f7d", "_spec": "@babel/plugin-transform-modules-amd@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-transforms": "^7.23.3", "@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "This plugin transforms ES2015 modules to AMD", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-external-helpers": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-amd", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-modules-amd", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-amd"}, "type": "commonjs", "version": "7.23.3"}