{"_from": "@babel/plugin-syntax-numeric-separator@^7.10.4", "_id": "@babel/plugin-syntax-numeric-separator@7.10.4", "_inBundle": false, "_integrity": "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==", "_location": "/@babel/plugin-syntax-numeric-separator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-numeric-separator@^7.10.4", "name": "@babel/plugin-syntax-numeric-separator", "escapedName": "@babel%2fplugin-syntax-numeric-separator", "scope": "@babel", "rawSpec": "^7.10.4", "saveSpec": null, "fetchSpec": "^7.10.4"}, "_requiredBy": ["/@babel/plugin-transform-numeric-separator", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "_shasum": "b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97", "_spec": "@babel/plugin-syntax-numeric-separator@^7.10.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "deprecated": false, "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-numeric-separator", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-numeric-separator"}, "version": "7.10.4"}