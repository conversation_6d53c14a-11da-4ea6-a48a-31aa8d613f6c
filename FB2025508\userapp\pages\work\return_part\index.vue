<template>
	<view class="page-container">
		<!-- 头部统计 -->
		<view class="stats-container">
			<view class="stats-card">
				<view class="stats-title">今日订单</view>
				<view class="stats-value">{{shopStats.today || 0}}</view>
			</view>
			<view class="stats-card">
				<view class="stats-title">本周订单</view>
				<view class="stats-value">{{shopStats.week || 0}}</view>
			</view>
			<view class="stats-card">
				<view class="stats-title">本月订单</view>
				<view class="stats-value">{{shopStats.month || 0}}</view>
			</view>
		</view>
		
		<!-- 员工列表头部 -->
		<view class="list-header">
			<view class="list-title">
				员工列表
				<text class="list-count">({{staffList.length || 0}})</text>
			</view>
			<view class="user-count">用户总数：{{totalUsers || 0}}</view>
			<view class="add-btn" @click="showAddStaffPopup">添加员工</view>
		</view>
		
		<!-- 员工列表 -->
		<view class="staff-list">
			<view v-if="staffList.length === 0" class="empty-list">
				暂无员工数据
			</view>
			<view v-else v-for="(staff, index) in staffList" :key="index" class="staff-item">
				<view class="staff-info">
					<view class="staff-name" @click="navigateToStaffDetail(staff)">{{staff.name}}</view>
					<view class="staff-code">{{staff.staffCode || '--'}}-{{staff.groupCode || '--'}}-{{staff.subgroupCode || '--'}}</view>
				</view>
				<view class="staff-stats">
					<view class="stat-item">
						<text class="stat-label">用户数：</text>
						<text class="stat-value">{{staff.userCount || 0}}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">执行中：</text>
						<text class="stat-value">{{staff.orderCount || 0}}</text>
					</view>
				</view>
				<view class="staff-actions">
					<view class="lock-btn" @click="toggleLock(staff)">
						{{staff.status === '1' ? '锁定' : '激活'}}
					</view>
					<view class="edit-btn" @click="showEditStaffPopup(staff)">修改</view>
				</view>
			</view>
		</view>
		
		<!-- 添加员工弹窗 -->
		<uni-popup ref="addStaffPopup" type="center">
			<view class="popup-container">
				<view class="popup-title">添加员工</view>
				<view class="form-item">
					<view class="form-label">员工用户名</view>
					<input type="text" v-model="newStaff.userName" class="form-input" placeholder="请输入员工用户名"/>
				</view>
				<view class="form-item">
					<view class="form-label">员工姓名</view>
					<input type="text" v-model="newStaff.name" class="form-input" placeholder="请输入员工姓名"/>
				</view>
				<view class="form-item">
					<view class="form-label">手机号</view>
					<input type="number" v-model="newStaff.phoneNumber" class="form-input" placeholder="请输入手机号" maxlength="11"/>
				</view>
				<view class="form-item">
					<view class="form-label">登录密码</view>
					<input type="password" v-model="newStaff.password" class="form-input" placeholder="请输入登录密码"/>
				</view>
				<view class="popup-actions">
					<view class="cancel-btn" @click="hideAddStaffPopup">取消</view>
					<view class="confirm-btn" @click="addStaff">确定</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 修改员工弹窗 -->
		<uni-popup ref="editStaffPopup" type="center">
			<view class="popup-container">
				<view class="popup-title">修改员工信息</view>
				<view class="form-item">
					<view class="form-label">员工姓名</view>
					<input type="text" v-model="editingStaff.staffName" class="form-input" placeholder="请输入员工姓名"/>
				</view>
				<view class="form-item">
					<view class="form-label">用户昵称</view>
					<input type="text" v-model="editingStaff.nickname" class="form-input" placeholder="请输入用户昵称"/>
				</view>
				<view class="form-item">
					<view class="form-label">手机号</view>
					<input type="number" v-model="editingStaff.phoneNumber" class="form-input" placeholder="请输入手机号" maxlength="11"/>
				</view>
				<view class="form-item">
					<view class="form-label">员工号</view>
					<picker @change="onEditStaffCodeChange" :value="editStaffCodeIndex" :range="availableCodes.staffCodes">
						<view class="picker-value">{{editingStaff.staffCode || '请选择员工号'}}</view>
					</picker>
				</view>
				<view class="form-item">
					<view class="form-label">班组</view>
					<picker @change="onEditGroupCodeChange" :value="editGroupCodeIndex" :range="availableCodes.groupCodes">
						<view class="picker-value">{{editingStaff.groupCode || '请选择班组号'}}</view>
					</picker>
				</view>
				<view class="form-item">
					<view class="form-label">子班组</view>
					<picker @change="onEditSubgroupCodeChange" :value="editSubgroupCodeIndex" :range="availableCodes.subgroupCodes">
						<view class="picker-value">{{editingStaff.subgroupCode || '请选择子班组号'}}</view>
					</picker>
				</view>
				<view class="form-item">
					<view class="form-label">性别</view>
					<picker @change="onGenderChange" :value="genderIndex" :range="genders" range-key="label">
						<view class="picker-value">{{getGenderLabel(editingStaff.gender)}}</view>
					</picker>
				</view>
				<view class="popup-actions">
					<view class="cancel-btn" @click="hideEditStaffPopup">取消</view>
					<view class="confirm-btn" @click="updateStaff">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { getStaffList, getShopStats, addStaffMember, addStaffSimple, checkStaffName, getAvailableCodes, toggleStaffLock, updateStaffInfo } from '@/api/work/retrun_part.js';
	
	export default {
		data() {
			return {
				shopStats: {
					today: 0,
					week: 0,
					month: 0
				},
				staffList: [],
				totalUsers: 0,
				newStaff: {
					userName: '',
					name: '',
					phoneNumber: '',
					password: ''
				},
				// 编辑员工信息
				editingStaff: {
					id: '',
					staffName: '',
					nickname: '',
					phoneNumber: '',
					staffCode: '',
					groupCode: '',
					subgroupCode: '',
					gender: '0',
					userId: null
				},
				nameError: '',
				availableCodes: {
					staffCodes: [],
					groupCodes: [],
					subgroupCodes: []
				},
				staffCodeIndex: 0,
				groupCodeIndex: 0,
				subgroupCodeIndex: 0,
				// 编辑时的选择器索引
				editStaffCodeIndex: 0,
				editGroupCodeIndex: 0,
				editSubgroupCodeIndex: 0,
				genderIndex: 0,
				// 性别选项
				genders: [
					{ value: '0', label: '男' },
					{ value: '1', label: '女' },
					{ value: '2', label: '未知' }
				],
				loading: false
			};
		},
		onLoad() {
			this.fetchData();
		},
		methods: {
			// 获取页面数据
			async fetchData() {
				uni.showLoading({
					title: '加载中'
				});
				
				try {
					// 获取店铺订单统计
					const statsRes = await getShopStats();
					if (statsRes.code === 200) {
						this.shopStats = statsRes.data || { today: 0, week: 0, month: 0 };
					}
					
					// 获取员工列表
					const listRes = await getStaffList();
					if (listRes.code === 200) {
						this.staffList = listRes.data.staffList || [];
						this.totalUsers = listRes.data.totalUsers || 0;
					}
					
					// 获取可用的编号
					this.fetchAvailableCodes();
				} catch (error) {
					console.error('获取数据失败', error);
					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			
			// 获取可用的编号
			async fetchAvailableCodes() {
				try {
					const res = await getAvailableCodes();
					if (res.code === 200) {
						this.availableCodes = res.data;
					}
				} catch (error) {
					console.error('获取编号失败', error);
				}
			},
			
			// 显示添加员工弹窗
			showAddStaffPopup() {
				this.resetNewStaff();
				this.$refs.addStaffPopup.open();
			},
			
			// 隐藏添加员工弹窗
			hideAddStaffPopup() {
				this.$refs.addStaffPopup.close();
			},
			
			// 重置新员工数据
			resetNewStaff() {
				this.newStaff = {
					userName: '',
					name: '',
					phoneNumber: '',
					password: ''
				};
			},
			
			// 添加员工
			async addStaff() {
				// 验证表单
				if (!this.newStaff.userName) {
					uni.showToast({
						title: '请输入员工用户名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.newStaff.name) {
					uni.showToast({
						title: '请输入员工姓名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.newStaff.phoneNumber || !/^1\d{10}$/.test(this.newStaff.phoneNumber)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				if (!this.newStaff.password) {
					uni.showToast({
						title: '请输入登录密码',
						icon: 'none'
					});
					return;
				}
				
				if (this.newStaff.password.length < 6) {
					uni.showToast({
						title: '密码长度至少为6位',
						icon: 'none'
					});
					return;
				}
				
				// 提交表单
				this.loading = true;
				uni.showLoading({
					title: '添加中'
				});
				
				try {
					const res = await addStaffSimple(this.newStaff);
					if (res.code === 200) {
						uni.showToast({
							title: '添加成功',
							icon: 'success'
						});
						this.hideAddStaffPopup();
						this.fetchData(); // 重新加载数据
					} else {
						uni.showToast({
							title: res.msg || '添加失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('添加员工失败', error);
					uni.showToast({
						title: '添加员工失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					uni.hideLoading();
				}
			},
			
			// 锁定/激活员工
			async toggleLock(staff) {
				// 使用 status 字段判断员工状态：1表示激活状态，0表示锁定状态
				const isActive = staff.status === '1';
				
				uni.showLoading({
					title: isActive ? '锁定中' : '激活中'
				});
				
				try {
					const res = await toggleStaffLock({
						staffId: staff.id,
						status: isActive ? '0' : '1' // 切换状态
					});
					
					if (res.code === 200) {
						uni.showToast({
							title: isActive ? '锁定成功' : '激活成功',
							icon: 'success'
						});
						this.fetchData(); // 重新加载数据
					} else {
						uni.showToast({
							title: res.msg || '操作失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('操作失败', error);
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			
			// 显示修改员工弹窗
			showEditStaffPopup(staff) {
				// 初始化编辑员工数据
				this.editingStaff = {
					id: staff.id,
					staffName: staff.name,
					nickname: staff.nickname || staff.name,
					phoneNumber: staff.phone,
					staffCode: staff.staffCode,
					groupCode: staff.groupCode,
					subgroupCode: staff.subgroupCode,
					gender: staff.gender || '0',
					userId: staff.userId
				};
				
				// 设置选择器索引
				this.editStaffCodeIndex = this.findIndexInArray(this.availableCodes.staffCodes, staff.staffCode);
				this.editGroupCodeIndex = this.findIndexInArray(this.availableCodes.groupCodes, staff.groupCode);
				this.editSubgroupCodeIndex = this.findIndexInArray(this.availableCodes.subgroupCodes, staff.subgroupCode);
				this.genderIndex = this.findGenderIndex(staff.gender || '0');
				
				// 打开弹窗
				this.$refs.editStaffPopup.open();
			},
			
			// 隐藏修改员工弹窗
			hideEditStaffPopup() {
				this.$refs.editStaffPopup.close();
			},
			
			// 在数组中查找索引
			findIndexInArray(array, value) {
				const index = array.findIndex(item => item === value);
				return index >= 0 ? index : 0;
			},
			
			// 查找性别索引
			findGenderIndex(gender) {
				const index = this.genders.findIndex(item => item.value === gender);
				return index >= 0 ? index : 0;
			},
			
			// 获取性别标签
			getGenderLabel(gender) {
				const genderItem = this.genders.find(item => item.value === gender);
				return genderItem ? genderItem.label : '男';
			},
			
			// 编辑时员工号选择变更
			onEditStaffCodeChange(e) {
				this.editStaffCodeIndex = e.detail.value;
				this.editingStaff.staffCode = this.availableCodes.staffCodes[this.editStaffCodeIndex];
			},
			
			// 编辑时班组选择变更
			onEditGroupCodeChange(e) {
				this.editGroupCodeIndex = e.detail.value;
				this.editingStaff.groupCode = this.availableCodes.groupCodes[this.editGroupCodeIndex];
			},
			
			// 编辑时子班组选择变更
			onEditSubgroupCodeChange(e) {
				this.editSubgroupCodeIndex = e.detail.value;
				this.editingStaff.subgroupCode = this.availableCodes.subgroupCodes[this.editSubgroupCodeIndex];
			},
			
			// 性别选择变更
			onGenderChange(e) {
				this.genderIndex = e.detail.value;
				this.editingStaff.gender = this.genders[this.genderIndex].value;
			},
			
			// 更新员工信息
			async updateStaff() {
				// 验证表单
				if (!this.editingStaff.staffName) {
					uni.showToast({
						title: '请输入员工姓名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.editingStaff.staffCode) {
					uni.showToast({
						title: '请选择员工号',
						icon: 'none'
					});
					return;
				}
				
				if (!this.editingStaff.groupCode) {
					uni.showToast({
						title: '请选择班组',
						icon: 'none'
					});
					return;
				}
				
				if (!this.editingStaff.subgroupCode) {
					uni.showToast({
						title: '请选择子班组',
						icon: 'none'
					});
					return;
				}
				
				if (!this.editingStaff.phoneNumber || !/^1\d{10}$/.test(this.editingStaff.phoneNumber)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				if (!this.editingStaff.nickname) {
					uni.showToast({
						title: '请输入用户昵称',
						icon: 'none'
					});
					return;
				}
				
				// 提交更新
				this.loading = true;
				uni.showLoading({
					title: '更新中'
				});
				
				try {
					const res = await updateStaffInfo({
						id: this.editingStaff.id,
						name: this.editingStaff.staffName,
						nickname: this.editingStaff.nickname,
						phoneNumber: this.editingStaff.phoneNumber,
						staffCode: this.editingStaff.staffCode,
						groupCode: this.editingStaff.groupCode,
						subgroupCode: this.editingStaff.subgroupCode,
						gender: this.editingStaff.gender
					});
					
					if (res.code === 200) {
						uni.showToast({
							title: '更新成功',
							icon: 'success'
						});
						this.hideEditStaffPopup();
						this.fetchData(); // 重新加载数据
					} else {
						uni.showToast({
							title: res.msg || '更新失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('更新员工信息失败', error);
					uni.showToast({
						title: '更新员工信息失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					uni.hideLoading();
				}
			},
			
			// 跳转到员工详情页面
			navigateToStaffDetail(staff) {
				uni.navigateTo({
					url: `/pages/work/return_part/staff-detail?id=${staff.id}`
				});
			}
		}
	};
</script>

<style lang="scss">
	.page-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.stats-container {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
		
		.stats-card {
			flex: 1;
			background-color: #ffffff;
			border-radius: 8rpx;
			padding: 20rpx;
			margin: 0 10rpx;
			text-align: center;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
			
			&:first-child {
				margin-left: 0;
			}
			
			&:last-child {
				margin-right: 0;
			}
			
			.stats-title {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 10rpx;
			}
			
			.stats-value {
				font-size: 40rpx;
				color: #333;
				font-weight: bold;
			}
		}
	}
	
	.list-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0;
		
		.list-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			
			.list-count {
				font-size: 28rpx;
				color: #666;
				font-weight: normal;
			}
		}
		
		.user-count {
			font-size: 28rpx;
			color: #666;
		}
		
		.add-btn {
			padding: 10rpx 30rpx;
			background-color: #007AFF;
			color: #fff;
			border-radius: 30rpx;
			font-size: 28rpx;
		}
	}
	
	.staff-list {
		background-color: #fff;
		border-radius: 8rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		padding: 0 20rpx;
		
		.empty-list {
			padding: 60rpx 0;
			text-align: center;
			color: #999;
			font-size: 28rpx;
		}
		
		.staff-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;
			border-bottom: 1px solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.staff-info {
				flex: 1;
				
				.staff-name {
					font-size: 32rpx;
					color: #007AFF;
					font-weight: bold;
					margin-bottom: 10rpx;
					text-decoration: underline;
				}
				
				.staff-code {
					font-size: 28rpx;
					color: #666;
				}
			}
			
			.staff-stats {
				flex: 2;
				
				.stat-item {
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.stat-label {
						font-size: 26rpx;
						color: #666;
					}
					
					.stat-value {
						font-size: 26rpx;
						color: #333;
					}
				}
			}
			
			.staff-actions {
				flex: 1;
				text-align: right;
				
				.lock-btn, .edit-btn {
					display: inline-block;
					padding: 10rpx 20rpx;
					background-color: #f0f0f0;
					color: #666;
					border-radius: 4rpx;
					font-size: 26rpx;
					margin-left: 10rpx;
				}
				
				.edit-btn {
					background-color: #007AFF;
					color: #fff;
				}
			}
		}
	}
	
	.popup-container {
		width: 600rpx;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
		}
		
		.form-item {
			margin-bottom: 20rpx;
			
			.form-label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.form-input {
				width: 100%;
				height: 80rpx;
				border: 1px solid #e0e0e0;
				border-radius: 4rpx;
				padding: 0 20rpx;
				font-size: 28rpx;
				box-sizing: border-box;
			}
			
			.picker-value {
				width: 100%;
				height: 80rpx;
				border: 1px solid #e0e0e0;
				border-radius: 4rpx;
				padding: 0 20rpx;
				font-size: 28rpx;
				box-sizing: border-box;
				line-height: 80rpx;
				color: #333;
			}
			
			.error-msg {
				font-size: 24rpx;
				color: #ff5500;
				margin-top: 6rpx;
			}
		}
		
		.popup-actions {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
			
			.cancel-btn, .confirm-btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 6rpx;
				font-size: 30rpx;
			}
			
			.cancel-btn {
				background-color: #f0f0f0;
				color: #666;
				margin-right: 15rpx;
			}
			
			.confirm-btn {
				background-color: #007AFF;
				color: #fff;
				margin-left: 15rpx;
			}
		}
	}
</style>
