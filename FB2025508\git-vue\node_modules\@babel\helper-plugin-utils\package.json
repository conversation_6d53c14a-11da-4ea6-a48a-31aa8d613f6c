{"_from": "@babel/helper-plugin-utils@^7.18.6", "_id": "@babel/helper-plugin-utils@7.24.0", "_inBundle": false, "_integrity": "sha512-9cUznXMG0+FxRuJfvL82QlTqIzhVW9sL0KjMPHhAOOvpQGL8QtdxnBKILjBqxlHyliz0yCa1G903ZXI/FuHy2w==", "_location": "/@babel/helper-plugin-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-plugin-utils@^7.18.6", "name": "@babel/helper-plugin-utils", "escapedName": "@babel%2fhelper-plugin-utils", "scope": "@babel", "rawSpec": "^7.18.6", "saveSpec": null, "fetchSpec": "^7.18.6"}, "_requiredBy": ["/@babel/helper-define-polyfill-provider", "/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly", "/@babel/plugin-proposal-class-properties", "/@babel/plugin-proposal-decorators", "/@babel/plugin-syntax-async-generators", "/@babel/plugin-syntax-class-properties", "/@babel/plugin-syntax-class-static-block", "/@babel/plugin-syntax-decorators", "/@babel/plugin-syntax-dynamic-import", "/@babel/plugin-syntax-export-namespace-from", "/@babel/plugin-syntax-import-assertions", "/@babel/plugin-syntax-import-attributes", "/@babel/plugin-syntax-import-meta", "/@babel/plugin-syntax-json-strings", "/@babel/plugin-syntax-jsx", "/@babel/plugin-syntax-logical-assignment-operators", "/@babel/plugin-syntax-nullish-coalescing-operator", "/@babel/plugin-syntax-numeric-separator", "/@babel/plugin-syntax-object-rest-spread", "/@babel/plugin-syntax-optional-catch-binding", "/@babel/plugin-syntax-optional-chaining", "/@babel/plugin-syntax-private-property-in-object", "/@babel/plugin-syntax-top-level-await", "/@babel/plugin-syntax-unicode-sets-regex", "/@babel/plugin-transform-arrow-functions", "/@babel/plugin-transform-async-generator-functions", "/@babel/plugin-transform-async-to-generator", "/@babel/plugin-transform-block-scoped-functions", "/@babel/plugin-transform-block-scoping", "/@babel/plugin-transform-class-properties", "/@babel/plugin-transform-class-static-block", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-computed-properties", "/@babel/plugin-transform-destructuring", "/@babel/plugin-transform-dotall-regex", "/@babel/plugin-transform-duplicate-keys", "/@babel/plugin-transform-dynamic-import", "/@babel/plugin-transform-exponentiation-operator", "/@babel/plugin-transform-export-namespace-from", "/@babel/plugin-transform-for-of", "/@babel/plugin-transform-function-name", "/@babel/plugin-transform-json-strings", "/@babel/plugin-transform-literals", "/@babel/plugin-transform-logical-assignment-operators", "/@babel/plugin-transform-member-expression-literals", "/@babel/plugin-transform-modules-amd", "/@babel/plugin-transform-modules-commonjs", "/@babel/plugin-transform-modules-systemjs", "/@babel/plugin-transform-modules-umd", "/@babel/plugin-transform-named-capturing-groups-regex", "/@babel/plugin-transform-new-target", "/@babel/plugin-transform-nullish-coalescing-operator", "/@babel/plugin-transform-numeric-separator", "/@babel/plugin-transform-object-rest-spread", "/@babel/plugin-transform-object-super", "/@babel/plugin-transform-optional-catch-binding", "/@babel/plugin-transform-optional-chaining", "/@babel/plugin-transform-parameters", "/@babel/plugin-transform-private-methods", "/@babel/plugin-transform-private-property-in-object", "/@babel/plugin-transform-property-literals", "/@babel/plugin-transform-regenerator", "/@babel/plugin-transform-reserved-words", "/@babel/plugin-transform-runtime", "/@babel/plugin-transform-shorthand-properties", "/@babel/plugin-transform-spread", "/@babel/plugin-transform-sticky-regex", "/@babel/plugin-transform-template-literals", "/@babel/plugin-transform-typeof-symbol", "/@babel/plugin-transform-unicode-escapes", "/@babel/plugin-transform-unicode-property-regex", "/@babel/plugin-transform-unicode-regex", "/@babel/plugin-transform-unicode-sets-regex", "/@babel/preset-env", "/@babel/preset-modules", "/@vue/babel-plugin-jsx", "/@vue/babel-plugin-resolve-type", "/babel-plugin-polyfill-corejs3/@babel/helper-define-polyfill-provider", "/babel-plugin-polyfill-regenerator/@babel/helper-define-polyfill-provider"], "_resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.0.tgz", "_shasum": "945681931a52f15ce879fd5b86ce2dae6d3d7f2a", "_spec": "@babel/helper-plugin-utils@^7.18.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\plugin-proposal-class-properties", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "General utilities for plugins to use", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-plugin-utils", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-plugin-utils"}, "type": "commonjs", "version": "7.24.0"}