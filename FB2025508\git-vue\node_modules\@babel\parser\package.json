{"_from": "@babel/parser@^7.24.0", "_id": "@babel/parser@7.24.0", "_inBundle": false, "_integrity": "sha512-QuP/FxEAzMSjXygs8v4N9dvdXzEHN4W1oF3PxuWAtPo08UdM17u89RDMgjLn/mlc56iM0HlLmVkO/wgR+rDgHg==", "_location": "/@babel/parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/parser@^7.24.0", "name": "@babel/parser", "escapedName": "@babel%2fparser", "scope": "@babel", "rawSpec": "^7.24.0", "saveSpec": null, "fetchSpec": "^7.24.0"}, "_requiredBy": ["/@babel/core", "/@babel/template", "/@babel/traverse", "/@vue/babel-plugin-resolve-type", "/@vue/compiler-core", "/@vue/compiler-sfc", "/babel-eslint"], "_resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.0.tgz", "_shasum": "26a3d1ff49031c53a97d03b604375f028746a9ac", "_spec": "@babel/parser@^7.24.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bin": {"parser": "bin/babel-parser.js"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A+parser+%28babylon%29%22+is%3Aopen"}, "bundleDependencies": false, "deprecated": false, "description": "A JavaScript parser", "devDependencies": {"@babel/code-frame": "^7.23.5", "@babel/helper-check-duplicate-nodes": "^7.22.5", "@babel/helper-fixtures": "^7.24.0", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.0.0"}, "files": ["bin", "lib", "typings/babel-parser.d.ts", "index.cjs"], "homepage": "https://babel.dev/docs/en/next/babel-parser", "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/parser", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-parser"}, "type": "commonjs", "types": "./typings/babel-parser.d.ts", "version": "7.24.0"}