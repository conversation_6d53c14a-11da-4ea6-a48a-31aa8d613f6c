{"_from": "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.23.3", "_id": "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.23.3", "_inBundle": false, "_integrity": "sha512-WwlxbfMNdVEpQjZmK5mhm7oSwD3dS6eU+Iwsi4Knl9wAletWem7kaRsGOG+8UEbRyqxY4SS5zvtfXwX+jMxUwQ==", "_location": "/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.23.3", "name": "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "escapedName": "@babel%2fplugin-bugfix-v8-spread-parameters-in-optional-chaining", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.23.3.tgz", "_shasum": "f6652bb16b94f8f9c20c50941e16e9756898dc5d", "_spec": "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5", "@babel/plugin-transform-optional-chaining": "^7.23.3"}, "deprecated": false, "description": "Transform optional chaining operators to workaround https://crbug.com/v8/11558", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining", "keywords": ["babel-plugin", "bugfix"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "peerDependencies": {"@babel/core": "^7.13.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining"}, "type": "commonjs", "version": "7.23.3"}