{"_from": "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2", "_id": "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2", "_inBundle": false, "_integrity": "sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==", "_location": "/@babel/plugin-proposal-private-property-in-object", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2", "name": "@babel/plugin-proposal-private-property-in-object", "escapedName": "@babel%2fplugin-proposal-private-property-in-object", "scope": "@babel", "rawSpec": "7.21.0-placeholder-for-preset-env.2", "saveSpec": null, "fetchSpec": "7.21.0-placeholder-for-preset-env.2"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "_shasum": "7844f9289546efa9febac2de4cfe358a050bd703", "_spec": "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel-plugin-proposal-private-property-in-object/issues"}, "bundleDependencies": false, "deprecated": false, "description": "This plugin transforms checks for a private property in an object", "devDependencies": {}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-private-property-in-object", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-proposal-private-property-in-object", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel-plugin-proposal-private-property-in-object.git"}, "type": "commonjs", "version": "7.21.0-placeholder-for-preset-env.2"}