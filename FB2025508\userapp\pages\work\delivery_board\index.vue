<!-- 看板发货 -->
<template>
	<view class="part-container">
		<!-- 列表 -->
		<uni-card :is-shadow="false" is-full v-for="item in mainList" v-show="listShow" :key='item.orderNum'>
			<view style="border: 1px solid #ccc;border-radius: 6px;padding: 10px;">
				
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="单号"><template v-slot:right>{{item.orderNum}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="计划日期"><template v-slot:right>{{item.planDate}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="ERP号"><template v-slot:right>{{item.tbDeliveryPlanSubList.map(m=> m.erpNum).join(',')}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="产品名称"><template v-slot:right>{{item.tbDeliveryPlanSubList.map(m=> m.productName).join(',')}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="数量"><template v-slot:right>{{item.actualLoadingQuantity}}</template></uni-section>
					</uni-col>
				</uni-row>
				<button type="primary" @click="()=>operationClick(item.id)" >操作</button>
			</view>
		</uni-card>
		

		<!-- 操作信息 -->
		<view v-show="!listShow">
			<uni-forms ref="form" :modelValue="formData">
				<uni-forms-item>
					<uni-col :span="24">
						<uni-section class="mb-10" title="单号"><template v-slot:right>{{formData.orderNum}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
				<uni-section title="器具" subTitle="" type="line" class="view-border">
					<template v-slot:right>
						<view @click="devicePlus()"><uni-icons type="plus" size="30"></uni-icons></view>
					</template>
				</uni-section>
				<uni-forms-item v-for="(item, index) in formData.deviceList" :key="index" class="view-border" label-width="10">
					<uni-section :title="'器具' + (index+1)" subTitle="" type="line" >
						<template v-slot:right>
							<view @click="deviceMinus(index)"><uni-icons type="minus" size="30"></uni-icons></view>
						</template>
					</uni-section>
					<uni-section class="mb-10" title="器具码">
						<template v-slot:right>
							<uni-view class="bar-code-css-view1 uni-input-wrapper">
								<uni-easyinput 
									:ref="'inputFocus' + (1 + index * 2)" 
									class="uni-input bar-code-css1" 
									focus 
									placeholder="请进行扫码" 
									trim="all"
									v-model="item.deviceCode" 
									@focus="barCodeFocus" 
									@blur="barCodeBlur" 
									@confirm="deviceCodeChange('inputFocus-' + (1 + index * 2))" />
							</uni-view>
						</template>
					</uni-section>
					<uni-section :title="`总成`" type="line" class="view-border">
						<template v-slot:right>
							<view @click="assemblyPlus(index)"><uni-icons type="plus" size="30"></uni-icons></view>
						</template>
					</uni-section>
					<view class="view-border " v-for="(assemblyItem, assemblyIndex) in item.assemblyList" :key="assemblyIndex" >
						<uni-section :title="'总成' + (assemblyIndex+1)" type="line">
							<template v-slot:right>
							<view @click="assemblyMinus(index, assemblyIndex)"><uni-icons type="minus" size="30"></uni-icons></view>
							</template>
						</uni-section>
						<uni-section title="条码" class="section-css">
							<template v-slot:right>
								<uni-view class="bar-code-css-view1 uni-input-wrapper">
									<uni-easyinput 
										:ref="'inputFocus' + (2 + index * 2) + '-' + (1 + assemblyIndex * 2)" 
										class="uni-input bar-code-css1" 
										placeholder="请进行扫码" 
										trim="all"
										v-model="assemblyItem.barCode" 
										@focus="barCodeFocus" 
										@blur="barCodeBlur" 
										@confirm="barCodeChange(index, assemblyIndex, 'inputFocus-' + (2 + index * 2) + '-' + (1 + assemblyIndex * 2))" />
								</uni-view>
							</template>
						</uni-section>
						<uni-section title="ERP号" class="section-css">
							<template v-slot:right>
								<uni-easyinput class="uni-mt-5" trim="all" v-model="assemblyItem.erpNum" placeholder="请输入ERP号" ></uni-easyinput>
							</template>
						</uni-section>
						<!-- <uni-section title="描述" subTitle="" type="line" padding style="word-break: break-all;">
							<span>{{item.remark}}</span>
						</uni-section> -->
						<uni-section title="数量" class="section-css">
							<template v-slot:right>
								<uni-easyinput 
									:ref="'inputFocus' + (2 + index * 2) + '-' + (2 + assemblyIndex * 2)" 
									class="uni-mt-5" 
									trim="all" 
									v-model="assemblyItem.num" 
									placeholder="请输入数量号"
									@confirm="barCodeClick('inputFocus-' + (2 + index * 2) + '-' + (2 + assemblyIndex * 2))"></uni-easyinput>
							</template>
						</uni-section>
					</view>
				</uni-forms-item>
	
				<button type="primary" @click="submit">上报</button>
			</uni-forms>
			<button type="warn" @click="reset">返回</button>
		</view>
		
		<!-- 遮罩 -->
		<uni-popup ref="loading" type="center" :animation="false" :mask-click="false">遮罩</uni-popup>
		<!-- 消息 -->
		<uni-popup ref="popup"><view class="popup-content"><text class="text" :duration="1000">上报成功</text></view></uni-popup>
		<uni-popup ref="popup1"><view class="popup1-content"><text class="text" :duration="1000">{{errorMsg}}</text></view></uni-popup>
		
		<!-- <view>
			<input v-model="text" placeholder="输入一段文字" />
			<button @click="speak">播放文本</button>
		</view> -->
	</view>
</template>

<script>
  import { findErpNumByBarCode } from '@/api/work/retrun_part.js'
  import { getPlan, planFindAll, saveDeliveryPlan, savePdaSub } from '@/api/work/delivery_board.js'
  
import { reactive } from "vue"
	export default {
	  data() {
	    return {
			//列表的显示
			listShow: true,
			// text: '',
			errorMsg: '',
			formData: {
				orderNum: '',
				deviceList: [],
			},
			formTem1: { barCode: '', erpNum: '', remark: '', num: '', },
			formTem2: {deviceCode: '', assemblyList: [{ barCode: '', erpNum: '', remark: '', num: '', }] },
			mainList: [],
			rules: {
				deviceCode: {
					rules:[
						{
							required: true,
							errorMessage: '请扫描器具码',
						}
					],
					validateTrigger:'submit'
				},
				erpNum: {
					rules:[
						{
							required: true,
							errorMessage: '请填写ERP号或者进行扫描条码',
						}
					],
					validateTrigger:'submit'
				},
				actualLoadingQuantity: {
					rules:[
						{
							required: true,
							errorMessage: '请填写数量',
						}
					],
					validateTrigger:'submit'
				},
			}
	    }
	  },
	  created(){
	  	this.tableList()
	  },
	  methods: {
		  //列表获取
		  tableList(){
			  planFindAll({status: '1'}).then(r=>{
			  	this.mainList= r.data
			  })
		  },
		  //总成的增加
		  assemblyPlus(index){
			this.formData.deviceList[index].assemblyList = JSON.parse(JSON.stringify([ ...this.formData.deviceList[index].assemblyList, JSON.parse(JSON.stringify(this.formTem2)) ]))
		  },
		  //总成的减少
		  assemblyMinus(index, assemblyIndex){
			 this.formData.deviceList[index].assemblyList = this.formData.deviceList[index].assemblyList.filter((f,i)=>i!=assemblyIndex)
		  },
		  //器具的增加
		  devicePlus(){
			 this.formData.deviceList = JSON.parse(JSON.stringify([ ...this.formData.deviceList, JSON.parse(JSON.stringify(this.formTem2)) ]))
		  },
		  //器具的减少
		  deviceMinus(index){
			 this.formData.deviceList = this.formData.deviceList.filter((f,i)=>i!=index)
		  },
		  //操作的点击事件
		  operationClick(id){
			this.listShow= false
			getPlan(id).then(r=>{
				this.formData= { ...r.data,  deviceList: [{deviceCode: '', assemblyList: [{ barCode: '', erpNum: '', remark: '', num: '', }] }] }
				
			})
		  },
		  // 器具码事件
		  deviceCodeChange(id){
			  this.barCodeClick(id);
		  },
		  // 条码事件
		  barCodeChange(index, assemblyIndex, id){
			  this.$refs.loading.open()
			  this.formData.deviceList[index].assemblyList[assemblyIndex].erpNum=''
			  findErpNumByBarCode({'barCode':this.formData.deviceList[index].assemblyList[assemblyIndex].barCode}).then(r=>{
				  console.log(r)
				  if(r && r.erpNum){
					  if(!this.formData.tbDeliveryPlanSubList.some(s=>s.erpNum == r.erpNum)){
						this.errorMsg= '获取到的ERP号不符合当前发货计划'
						this.speak(this.errorMsg)
						this.msg('popup1')
					  }else{
						  this.formData.deviceList[index].assemblyList[assemblyIndex].erpNum= r.erpNum
						  this.formData= JSON.parse(JSON.stringify(this.formData))
						  this.$refs.loading.close()
						  this.barCodeClick(id);
					  }
				  } else {
					setTimeout(()=>{
						this.formData.deviceList[index].assemblyList[assemblyIndex].barCode = ""
						this.focusClick(id)
					},500);
					this.errorMsg= '未获取到ERP号'
					this.speak(this.errorMsg)
					this.msg('popup1')
				  }
			  }).catch(e=>{
				  this.errorMsg= e
				  this.speak(this.errorMsg)
				  this.msg('popup1')
			  })
		  },
		  //触发器具条码焦点事件
		  deviceCodeClick(){
			const e = document.getElementById("inputFocus1")
			if(e){
				e.getElementsByTagName('input')[0].focus()
			}
		  },
		  //触发条码焦点事件
		  barCodeClick(id){
			const arr = id.split('-')
			//等于2，当前为器具码
			if(arr.length == 2){
				const e1 = document.getElementById(`${arr[0]}-${arr[1]*1+1}`);
				if(e1){
					e1.getElementsByTagName('input')[0].focus()
				}else{
					const e2 = document.getElementById(`${arr[0]}-${arr[1]*1+1}-1`);
					if(e2){
						e2.getElementsByTagName('input')[0].focus()
					}else{
						//没有下一个，不进行跳转
					}
				}
			}else if(arr.length == 3){// 等于3，当前为条码
					const e1 = document.getElementById(`${arr[0]}-${arr[1]}-${arr[2] * 1 + 1}`);
					if(e1){
						e1.getElementsByTagName('input')[0].focus()
					}else{
						const e2 = document.getElementById(`${arr[0]}-${arr[1]*1 + 1}`);
						if(e2){
							e2.getElementsByTagName('input')[0].focus()
						}else{
							//没有下一个，不进行跳转
						}
					}
			}
		  },
		  //条码触发焦点回调事件
		  barCodeFocus(){
			  //border-color: rgb(41, 121, 255);
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = 'rgb(41, 121, 255)';
			  }
		  },
		  //条码离开焦点回调事件
		  barCodeBlur(){
			  //去除边框样式
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = '';
			  }
		  },
		  //上报事件
		  submit(){
			  console.log('this.formData', this.formData)
			  this.$refs.loading.open()
			  //判断器具码是否为空
			  if(!this.formData.deviceList.every(s=>s.deviceCode)){
				this.errorMsg= '器具码不能为空'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return;
			  }
			  //判断ERP号是否为空
			  if(!this.formData.deviceList.every(s=>s.assemblyList.every(ee=>ee.erpNum))){
				this.errorMsg= 'ERP号不能为空'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return;
			  }
			  //判断获取到的ERP号是否符合当前发货计划
			  const arr = this.formData.tbDeliveryPlanSubList.map(m=>m.erpNum)
			  const arr1 = []
			  this.formData.deviceList.forEach(s=>s.assemblyList.forEach(ee=>arr1.push(ee.erpNum)))
			  if(this.arraysAreDifferent(arr, arr1)){
				this.errorMsg= '获取到的ERP号不符合当前发货计划'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return
			  }
			  //单总成校验
			  for (var i = 0; i < this.formData.tbDeliveryPlanSubList.length; i++) {
				const item = this.formData.tbDeliveryPlanSubList[i]
				//数据库中规定数量上线
			  	const num = item.num;
				const erpN = item.erpNum
				//输入的数量
				var numNew = 0
				this.formData.deviceList.forEach(s=>s.assemblyList.filter(f=> erpN == f.erpNum).forEach(ee=> numNew = numNew + ee.num*1))
				if(numNew*1 > num*1){
					this.errorMsg= `ERP号为${erpN}数量大于实际数量`
					this.speak(this.errorMsg)
					this.msg('popup1')
					return;
				}
			  }
			  
			  //总数量校验
			  var num = 0
			  this.formData.deviceList.forEach(s=>s.assemblyList.forEach(ee=> num = num + ee.num*1))
			  console.log('num', num)
			  if(num > this.formData.actualLoadingQuantity*1){
				this.errorMsg= '输入的总数量不能大于实际总数量'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return;
			  }
			  console.log( 'this.formData', this.formData )
			  
			  const resList = []
			  this.formData.deviceList.forEach(e => {
				 e.assemblyList.forEach(ee => {
					 resList.push({ orderNum: this.formData.orderNum, deviceCode: e.deviceCode, barCode: ee.barCode ,erpNum: ee.erpNum , num: ee.num });
				 })
			  });
			  
			  console.log( 'resList', resList )
			  savePdaSub(resList).then(r=>{
				  if(r.code == 200){
					this.speak("上报成功")
					this.msg('popup')
					this.reset()
				  }else{
					  this.errorMsg= '不能重复退返'
					  this.speak(this.errorMsg)
					  this.msg('popup1')
				  }
			  }).catch(e=>{
				  this.errorMsg= e
			      this.speak(this.errorMsg)
				  this.msg('popup1')
			  })
		  },
		  //重置
		  reset(){
			  this.formData.deviceList = []
			  this.formData= JSON.parse(JSON.stringify(this.formData))
			  this.tableList()
			  this.listShow= true
			  this.deviceCodeClick();
		  },
		  //消息提示
		  msg(type){
			this.$refs[type].open('center')
			setTimeout(() => {
				this.$refs[type].close()
			}, 3000);
			this.$refs.loading.close()
		  },
		  //语音提示
		  speak(text) {
			  if(!text){
				return
			  }
			  var music = null;
			  music = uni.createInnerAudioContext(); //创建播放器对象
			  music.src = `../../../static/video/msg/${text.includes("成功") ? 'czcg.wav' : 'czsb.mp3'}`;
			  music.volume = 1;
			  music.play(); //执行播放
			  music.onEnded(() => {
				  //播放结束
				  music = null;
			  });
		  },
		  //数组对比
		  arraysAreDifferent(arr11, arr22) {
			const arr1 = [ ...new Set(arr11) ]
			const arr2 = [ ...new Set(arr22) ]
		    return !arr2.every(item => arr1.includes(item))
		  },
		  //焦点事件
		  focusClick(id){
			const e = document.getElementById(id)
			console.log(1111, e)
			if(e){
				e.getElementsByTagName('input')[0].focus()
			}
		  },
	  },
	}
</script>

<style scoped lang="scss">
.part-container{
	background-color: #ffffff;
}
.bar-code-css{
	height: 35px;
	text-indent: 10px;
	display: flex;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	color: #000;
	font-size: 14px;
}
.bar-code-css .uni-input-placeholder.input-placeholder{
	color: #999;
}
.bar-code-css-view{
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	/* border-color: rgb(41, 121, 255); */
	background-color: rgb(255, 255, 255);
}
.popup-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #09bb07;
	background-color: #e1f3d8;
}
.popup1-content{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #f56c6c;
	background-color: #fde2e2;
}

.view-border{
	margin: 10px;
	padding: 10px;
	border: 1px solid #ccc;
	border-radius: 6px;
}
	
::v-deep .view-border .section-css uni-view.uni-section-header__slot-right{
	width: 70%!important;
}
</style>