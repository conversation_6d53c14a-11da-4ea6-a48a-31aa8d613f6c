{"_from": "@babel/helper-annotate-as-pure@^7.22.5", "_id": "@babel/helper-annotate-as-pure@7.22.5", "_inBundle": false, "_integrity": "sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==", "_location": "/@babel/helper-annotate-as-pure", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-annotate-as-pure@^7.22.5", "name": "@babel/helper-annotate-as-pure", "escapedName": "@babel%2fhelper-annotate-as-pure", "scope": "@babel", "rawSpec": "^7.22.5", "saveSpec": null, "fetchSpec": "^7.22.5"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-create-regexp-features-plugin", "/@babel/helper-remap-async-to-generator", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-private-property-in-object"], "_resolved": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz", "_shasum": "e7f06737b197d580a01edf75d97e2c8be99d3882", "_spec": "@babel/helper-annotate-as-pure@^7.22.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.5"}, "deprecated": false, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-annotate-as-pure", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-annotate-as-pure"}, "type": "commonjs", "version": "7.22.5"}