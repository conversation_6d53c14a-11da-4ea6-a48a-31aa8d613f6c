{"_from": "@babel/plugin-transform-typeof-symbol@^7.23.3", "_id": "@babel/plugin-transform-typeof-symbol@7.23.3", "_inBundle": false, "_integrity": "sha512-4t15ViVnaFdrPC74be1gXBSMzXk3B4Us9lP7uLRQHTFpV5Dvt33pn+2MyyNxmN3VTTm3oTrZVMUmuw3oBnQ2oQ==", "_location": "/@babel/plugin-transform-typeof-symbol", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-typeof-symbol@^7.23.3", "name": "@babel/plugin-transform-typeof-symbol", "escapedName": "@babel%2fplugin-transform-typeof-symbol", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.23.3.tgz", "_shasum": "9dfab97acc87495c0c449014eb9c547d8966bca4", "_spec": "@babel/plugin-transform-typeof-symbol@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/runtime": "^7.23.2", "@babel/runtime-corejs2": "^7.23.2", "@babel/runtime-corejs3": "^7.23.2"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-typeof-symbol", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-typeof-symbol", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-typeof-symbol"}, "type": "commonjs", "version": "7.23.3"}