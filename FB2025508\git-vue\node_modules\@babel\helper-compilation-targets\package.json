{"_from": "@babel/helper-compilation-targets@^7.23.6", "_id": "@babel/helper-compilation-targets@7.23.6", "_inBundle": false, "_integrity": "sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==", "_location": "/@babel/helper-compilation-targets", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-compilation-targets@^7.23.6", "name": "@babel/helper-compilation-targets", "escapedName": "@babel%2fhelper-compilation-targets", "scope": "@babel", "rawSpec": "^7.23.6", "saveSpec": null, "fetchSpec": "^7.23.6"}, "_requiredBy": ["/@babel/core", "/@babel/helper-define-polyfill-provider", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-function-name", "/@babel/plugin-transform-object-rest-spread", "/@babel/preset-env", "/@vue/babel-preset-app", "/babel-plugin-polyfill-corejs3/@babel/helper-define-polyfill-provider", "/babel-plugin-polyfill-regenerator/@babel/helper-define-polyfill-provider"], "_resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz", "_shasum": "4d79069b16cbcf1461289eccfbbd81501ae39991", "_spec": "@babel/helper-compilation-targets@^7.23.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/compat-data": "^7.23.5", "@babel/helper-validator-option": "^7.23.5", "browserslist": "^4.22.2", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "deprecated": false, "description": "Helper functions on Babel compilation targets", "devDependencies": {"@babel/helper-plugin-test-runner": "^7.22.5", "@types/lru-cache": "^5.1.1", "@types/semver": "^5.5.0"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-compilation-targets", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-compilation-targets"}, "type": "commonjs", "version": "7.23.6"}