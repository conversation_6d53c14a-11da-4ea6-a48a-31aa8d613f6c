<template>
	<view class="container">
		<view class="header">
			<view class="title">门店服务项目统计</view>
		</view>
		
		<!-- 顶部加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-spinner"></view>
			<text>数据加载中...</text>
		</view>
		
		<!-- 当前年度服务数据 -->
		<view v-else class="stats-container">
			<!-- 今日统计 -->
			<view class="stats-section">
				<view class="section-header">
					<text class="section-title">今日服务量 ({{ currentDate }})</text>
				</view>
				<view class="stats-list">
					<view v-if="todayStats.length === 0" class="no-data">
						<text>暂无数据</text>
					</view>
					<view v-else v-for="(item, index) in todayStats" :key="index" class="stats-item">
						<text class="item-name">{{ item.service_name }}</text>
						<text class="item-count">{{ item.count }}</text>
					</view>
				</view>
			</view>
			
			<!-- 本周统计 -->
			<view class="stats-section">
				<view class="section-header">
					<text class="section-title">本周服务量 ({{ currentWeek }})</text>
				</view>
				<view class="stats-list">
					<view v-if="weekStats.length === 0" class="no-data">
						<text>暂无数据</text>
					</view>
					<view v-else v-for="(item, index) in weekStats" :key="index" class="stats-item">
						<text class="item-name">{{ item.service_name }}</text>
						<text class="item-count">{{ item.count }}</text>
					</view>
				</view>
			</view>
			
			<!-- 本月统计 -->
			<view class="stats-section">
				<view class="section-header">
					<text class="section-title">本月服务量 ({{ currentMonth }})</text>
				</view>
				<view class="stats-list">
					<view v-if="monthStats.length === 0" class="no-data">
						<text>暂无数据</text>
					</view>
					<view v-else v-for="(item, index) in monthStats" :key="index" class="stats-item">
						<text class="item-name">{{ item.service_name }}</text>
						<text class="item-count">{{ item.count }}</text>
					</view>
				</view>
			</view>
			
			<!-- 本年统计 -->
			<view class="stats-section">
				<view class="section-header">
					<text class="section-title">本年服务量 ({{ currentYear }})</text>
				</view>
				<view class="stats-list">
					<view v-if="yearStats.length === 0" class="no-data">
						<text>暂无数据</text>
					</view>
					<view v-else v-for="(item, index) in yearStats" :key="index" class="stats-item">
						<text class="item-name">{{ item.service_name }}</text>
						<text class="item-count">{{ item.count }}</text>
					</view>
				</view>
			</view>
			
			<!-- 历史年度数据 -->
			<block v-for="(yearData, yearIndex) in visibleHistoricalYears" :key="yearIndex">
				<view class="stats-section historical">
					<view class="section-header">
						<text class="section-title">{{ yearData.year }}年订单量</text>
					</view>
					<view class="stats-list">
						<view v-if="yearData.stats.length === 0" class="no-data">
							<text>暂无数据</text>
						</view>
						<view v-else v-for="(item, index) in yearData.stats" :key="index" class="stats-item">
							<text class="item-name">{{ item.service_name }}</text>
							<text class="item-count">{{ item.count }}</text>
						</view>
					</view>
				</view>
			</block>
			
			<!-- 显示更多按钮 -->
			<view v-if="hasMoreHistoricalData" class="show-more" @click="showMore">
				<text>显示更多历史数据</text>
				<view class="arrow-down"></view>
			</view>
		</view>
		
		<!-- 底部提示 -->
		<view class="footer-tip" v-if="!loading && !hasMoreHistoricalData && historicalYearlyStats.length > 0">
			<text>已显示全部历史数据</text>
		</view>
		
		<!-- 无数据提示 -->
		<view v-if="!loading && noDataAtAll" class="no-data-container">
			<text>暂无任何服务统计数据</text>
		</view>
	</view>
</template>

<script>
import { getShopServiceStats } from '@/api/work/retrun_part.js';

export default {
	data() {
		return {
			loading: true,
			currentDate: '',
			currentWeek: '',
			currentMonth: '',
			currentYear: '',
			todayStats: [],
			weekStats: [],
			monthStats: [],
			yearStats: [],
			historicalYearlyStats: [],
			visibleHistoricalYears: [],
			displayedYearCount: 1, // 初始显示1年的历史数据
			maxYearsPerPage: 1 // 每次"显示更多"增加的年份数量
		};
	},
	computed: {
		hasMoreHistoricalData() {
			return this.visibleHistoricalYears.length < this.historicalYearlyStats.length;
		},
		noDataAtAll() {
			return this.todayStats.length === 0 && 
				this.weekStats.length === 0 && 
				this.monthStats.length === 0 && 
				this.yearStats.length === 0 && 
				this.historicalYearlyStats.length === 0;
		}
	},
	onLoad() {
		this.fetchServiceStats();
	},
	methods: {
		async fetchServiceStats() {
			this.loading = true;
			
			try {
				const res = await getShopServiceStats();
				
				if (res.code === 200 && res.data) {
					// 更新日期信息
					this.currentDate = res.data.currentDate;
					this.currentWeek = res.data.currentWeek;
					this.currentMonth = res.data.currentMonth;
					this.currentYear = res.data.currentYear;
					
					// 更新统计数据
					this.todayStats = res.data.todayStats || [];
					this.weekStats = res.data.weekStats || [];
					this.monthStats = res.data.monthStats || [];
					this.yearStats = res.data.yearStats || [];
					this.historicalYearlyStats = res.data.historicalYearlyStats || [];
					
					// 初始化显示的历史数据
					this.updateVisibleHistoricalYears();
				} else {
					uni.showToast({
						title: res.msg || '获取统计数据失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取服务项目统计数据失败', error);
				uni.showToast({
					title: '获取统计数据失败，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 更新显示的历史年份数据
		updateVisibleHistoricalYears() {
			this.visibleHistoricalYears = this.historicalYearlyStats.slice(0, this.displayedYearCount);
		},
		
		// 显示更多历史数据
		showMore() {
			this.displayedYearCount += this.maxYearsPerPage;
			// 确保不超出数组范围
			if (this.displayedYearCount > this.historicalYearlyStats.length) {
				this.displayedYearCount = this.historicalYearlyStats.length;
			}
			this.updateVisibleHistoricalYears();
		}
	}
};
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	box-sizing: border-box;
}

.header {
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	color: #333;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300rpx;
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #3c96f3;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
}

.stats-container {
	margin-bottom: 40rpx;
}

.stats-section {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	
	&.historical {
		background-color: #f9f9f9;
	}
}

.section-header {
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 16rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.stats-list {
	padding: 10rpx 0;
}

.stats-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 10rpx;
	border-bottom: 1px solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.item-name {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.item-count {
	font-size: 28rpx;
	color: #3c96f3;
	font-weight: bold;
	margin-left: 20rpx;
}

.no-data {
	padding: 20rpx 0;
	text-align: center;
	color: #999;
	font-size: 28rpx;
}

.show-more {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	margin: 10rpx 0;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	color: #3c96f3;
	font-size: 28rpx;
	
	.arrow-down {
		width: 0;
		height: 0;
		border-left: 12rpx solid transparent;
		border-right: 12rpx solid transparent;
		border-top: 12rpx solid #3c96f3;
		margin-top: 10rpx;
	}
}

.footer-tip {
	text-align: center;
	color: #999;
	font-size: 26rpx;
	margin: 20rpx 0;
}

.no-data-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 300rpx;
	color: #999;
	font-size: 28rpx;
}
</style>
