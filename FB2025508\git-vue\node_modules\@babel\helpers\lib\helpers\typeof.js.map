{"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "exports", "default", "constructor", "prototype"], "sources": ["../../src/helpers/typeof.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\n/* eslint-disable no-func-assign */\n\nexport default function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj &&\n        typeof Symbol === \"function\" &&\n        obj.constructor === Symbol &&\n        obj !== Symbol.prototype\n        ? \"symbol\"\n        : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n"], "mappings": ";;;;;;AAIe,SAASA,OAAOA,CAACC,GAAG,EAAE;EACnC,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEC,OAAA,CAAAC,OAAA,GAAAL,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACvB,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLG,OAAA,CAAAC,OAAA,GAAAL,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACvB,OAAOA,GAAG,IACR,OAAOC,MAAM,KAAK,UAAU,IAC5BD,GAAG,CAACK,WAAW,KAAKJ,MAAM,IAC1BD,GAAG,KAAKC,MAAM,CAACK,SAAS,GACtB,QAAQ,GACR,OAAON,GAAG;IAChB,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB"}