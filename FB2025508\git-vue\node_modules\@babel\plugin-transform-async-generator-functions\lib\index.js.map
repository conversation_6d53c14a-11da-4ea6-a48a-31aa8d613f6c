{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperRemapAsyncToGenerator", "_core", "_forAwait", "_helperEnvironmentVisitor", "_default", "exports", "default", "declare", "api", "assertVersion", "yieldStarVisitor", "traverse", "visitors", "merge", "ArrowFunctionExpression", "path", "skip", "YieldExpression", "node", "state", "delegate", "asyncIter", "t", "callExpression", "addHelper", "argument", "environmentVisitor", "forAwaitVisitor", "ForOfStatement", "file", "await", "build", "rewriteForAwait", "getAsyncIterator", "declar", "loop", "block", "body", "ensureBlock", "push", "length", "blockStatement", "inherits", "p", "replaceParent", "parentPath", "replaceWithMultiple", "scope", "parent", "crawl", "visitor", "Function", "async", "generator", "remapAsyncToGenerator", "wrapAsync", "wrapAwait", "name", "Program"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport remapAsyncToGenerator from \"@babel/helper-remap-async-to-generator\";\nimport type { NodePath, Visitor } from \"@babel/traverse\";\nimport { traverse, types as t, type PluginPass } from \"@babel/core\";\nimport rewriteForAwait from \"./for-await.ts\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\n\nexport default declare(api => {\n  api.assertVersion(\n    process.env.BABEL_8_BREAKING && process.env.IS_PUBLISH\n      ? PACKAGE_JSON.version\n      : 7,\n  );\n\n  const yieldStarVisitor = traverse.visitors.merge<PluginPass>([\n    {\n      ArrowFunctionExpression(path) {\n        path.skip();\n      },\n\n      YieldExpression({ node }, state) {\n        if (!node.delegate) return;\n        const asyncIter = t.callExpression(state.addHelper(\"asyncIterator\"), [\n          node.argument,\n        ]);\n        node.argument = t.callExpression(\n          state.addHelper(\"asyncGeneratorDelegate\"),\n          process.env.BABEL_8_BREAKING\n            ? [asyncIter]\n            : [asyncIter, state.addHelper(\"awaitAsyncGenerator\")],\n        );\n      },\n    },\n    environmentVisitor,\n  ]);\n\n  const forAwaitVisitor = traverse.visitors.merge<PluginPass>([\n    {\n      ArrowFunctionExpression(path) {\n        path.skip();\n      },\n\n      ForOfStatement(path: NodePath<t.ForOfStatement>, { file }) {\n        const { node } = path;\n        if (!node.await) return;\n\n        const build = rewriteForAwait(path, {\n          getAsyncIterator: file.addHelper(\"asyncIterator\"),\n        });\n\n        const { declar, loop } = build;\n        const block = loop.body as t.BlockStatement;\n\n        // ensure that it's a block so we can take all its statements\n        path.ensureBlock();\n\n        // add the value declaration to the new loop body\n        if (declar) {\n          block.body.push(declar);\n          if (path.node.body.body.length) {\n            block.body.push(t.blockStatement(path.node.body.body));\n          }\n        } else {\n          block.body.push(...path.node.body.body);\n        }\n\n        t.inherits(loop, node);\n        t.inherits(loop.body, node.body);\n\n        const p = build.replaceParent ? path.parentPath : path;\n        p.replaceWithMultiple(build.node);\n\n        // TODO: Avoid crawl\n        p.scope.parent.crawl();\n      },\n    },\n    environmentVisitor,\n  ]);\n\n  const visitor: Visitor<PluginPass> = {\n    Function(path, state) {\n      if (!path.node.async) return;\n\n      path.traverse(forAwaitVisitor, state);\n\n      if (!path.node.generator) return;\n\n      path.traverse(yieldStarVisitor, state);\n\n      // We don't need to pass the noNewArrows assumption, since\n      // async generators are never arrow functions.\n      remapAsyncToGenerator(path, {\n        wrapAsync: state.addHelper(\"wrapAsyncGenerator\"),\n        wrapAwait: state.addHelper(\"awaitAsyncGenerator\"),\n      });\n    },\n  };\n\n  return {\n    name: \"transform-async-generator-functions\",\n    inherits: USE_ESM\n      ? undefined\n      : IS_STANDALONE\n        ? undefined\n        : // eslint-disable-next-line no-restricted-globals\n          require(\"@babel/plugin-syntax-async-generators\").default,\n\n    visitor: {\n      Program(path, state) {\n        // We need to traverse the ast here (instead of just vising Function\n        // in the top level visitor) because for-await needs to run before the\n        // async-to-generator plugin. This is because for-await is transpiled\n        // using \"await\" expressions, which are then converted to \"yield\".\n        //\n        // This is bad for performance, but plugin ordering will allow as to\n        // directly visit Function in the top level visitor.\n        path.traverse(visitor, state);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,4BAAA,GAAAD,OAAA;AAEA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,yBAAA,GAAAJ,OAAA;AAAmE,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEpD,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAGX,CACN,CAAC;EAED,MAAMC,gBAAgB,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAa,CAC3D;IACEC,uBAAuBA,CAACC,IAAI,EAAE;MAC5BA,IAAI,CAACC,IAAI,CAAC,CAAC;IACb,CAAC;IAEDC,eAAeA,CAAC;MAAEC;IAAK,CAAC,EAAEC,KAAK,EAAE;MAC/B,IAAI,CAACD,IAAI,CAACE,QAAQ,EAAE;MACpB,MAAMC,SAAS,GAAGC,WAAC,CAACC,cAAc,CAACJ,KAAK,CAACK,SAAS,CAAC,eAAe,CAAC,EAAE,CACnEN,IAAI,CAACO,QAAQ,CACd,CAAC;MACFP,IAAI,CAACO,QAAQ,GAAGH,WAAC,CAACC,cAAc,CAC9BJ,KAAK,CAACK,SAAS,CAAC,wBAAwB,CAAC,EAGrC,CAACH,SAAS,EAAEF,KAAK,CAACK,SAAS,CAAC,qBAAqB,CAAC,CACxD,CAAC;IACH;EACF,CAAC,EACDE,iCAAkB,CACnB,CAAC;EAEF,MAAMC,eAAe,GAAGhB,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAa,CAC1D;IACEC,uBAAuBA,CAACC,IAAI,EAAE;MAC5BA,IAAI,CAACC,IAAI,CAAC,CAAC;IACb,CAAC;IAEDY,cAAcA,CAACb,IAAgC,EAAE;MAAEc;IAAK,CAAC,EAAE;MACzD,MAAM;QAAEX;MAAK,CAAC,GAAGH,IAAI;MACrB,IAAI,CAACG,IAAI,CAACY,KAAK,EAAE;MAEjB,MAAMC,KAAK,GAAG,IAAAC,iBAAe,EAACjB,IAAI,EAAE;QAClCkB,gBAAgB,EAAEJ,IAAI,CAACL,SAAS,CAAC,eAAe;MAClD,CAAC,CAAC;MAEF,MAAM;QAAEU,MAAM;QAAEC;MAAK,CAAC,GAAGJ,KAAK;MAC9B,MAAMK,KAAK,GAAGD,IAAI,CAACE,IAAwB;MAG3CtB,IAAI,CAACuB,WAAW,CAAC,CAAC;MAGlB,IAAIJ,MAAM,EAAE;QACVE,KAAK,CAACC,IAAI,CAACE,IAAI,CAACL,MAAM,CAAC;QACvB,IAAInB,IAAI,CAACG,IAAI,CAACmB,IAAI,CAACA,IAAI,CAACG,MAAM,EAAE;UAC9BJ,KAAK,CAACC,IAAI,CAACE,IAAI,CAACjB,WAAC,CAACmB,cAAc,CAAC1B,IAAI,CAACG,IAAI,CAACmB,IAAI,CAACA,IAAI,CAAC,CAAC;QACxD;MACF,CAAC,MAAM;QACLD,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC,GAAGxB,IAAI,CAACG,IAAI,CAACmB,IAAI,CAACA,IAAI,CAAC;MACzC;MAEAf,WAAC,CAACoB,QAAQ,CAACP,IAAI,EAAEjB,IAAI,CAAC;MACtBI,WAAC,CAACoB,QAAQ,CAACP,IAAI,CAACE,IAAI,EAAEnB,IAAI,CAACmB,IAAI,CAAC;MAEhC,MAAMM,CAAC,GAAGZ,KAAK,CAACa,aAAa,GAAG7B,IAAI,CAAC8B,UAAU,GAAG9B,IAAI;MACtD4B,CAAC,CAACG,mBAAmB,CAACf,KAAK,CAACb,IAAI,CAAC;MAGjCyB,CAAC,CAACI,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC;IACxB;EACF,CAAC,EACDvB,iCAAkB,CACnB,CAAC;EAEF,MAAMwB,OAA4B,GAAG;IACnCC,QAAQA,CAACpC,IAAI,EAAEI,KAAK,EAAE;MACpB,IAAI,CAACJ,IAAI,CAACG,IAAI,CAACkC,KAAK,EAAE;MAEtBrC,IAAI,CAACJ,QAAQ,CAACgB,eAAe,EAAER,KAAK,CAAC;MAErC,IAAI,CAACJ,IAAI,CAACG,IAAI,CAACmC,SAAS,EAAE;MAE1BtC,IAAI,CAACJ,QAAQ,CAACD,gBAAgB,EAAES,KAAK,CAAC;MAItC,IAAAmC,oCAAqB,EAACvC,IAAI,EAAE;QAC1BwC,SAAS,EAAEpC,KAAK,CAACK,SAAS,CAAC,oBAAoB,CAAC;QAChDgC,SAAS,EAAErC,KAAK,CAACK,SAAS,CAAC,qBAAqB;MAClD,CAAC,CAAC;IACJ;EACF,CAAC;EAED,OAAO;IACLiC,IAAI,EAAE,qCAAqC;IAC3Cf,QAAQ,EAKF3C,OAAO,CAAC,uCAAuC,CAAC,CAACO,OAAO;IAE9D4C,OAAO,EAAE;MACPQ,OAAOA,CAAC3C,IAAI,EAAEI,KAAK,EAAE;QAQnBJ,IAAI,CAACJ,QAAQ,CAACuC,OAAO,EAAE/B,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;AACH,CAAC,CAAC"}