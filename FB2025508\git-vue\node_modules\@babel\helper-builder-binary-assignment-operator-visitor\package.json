{"_from": "@babel/helper-builder-binary-assignment-operator-visitor@^7.22.15", "_id": "@babel/helper-builder-binary-assignment-operator-visitor@7.22.15", "_inBundle": false, "_integrity": "sha512-QkBXwGgaoC2GtGZRoma6kv7Szfv06khvhFav67ZExau2RaXzy8MpHSMO2PNoP2XtmQphJQRHFfg77Bq731Yizw==", "_location": "/@babel/helper-builder-binary-assignment-operator-visitor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-builder-binary-assignment-operator-visitor@^7.22.15", "name": "@babel/helper-builder-binary-assignment-operator-visitor", "escapedName": "@babel%2fhelper-builder-binary-assignment-operator-visitor", "scope": "@babel", "rawSpec": "^7.22.15", "saveSpec": null, "fetchSpec": "^7.22.15"}, "_requiredBy": ["/@babel/plugin-transform-exponentiation-operator"], "_resolved": "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.22.15.tgz", "_shasum": "5426b109cf3ad47b91120f8328d8ab1be8b0b956", "_spec": "@babel/helper-builder-binary-assignment-operator-visitor@^7.22.15", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\plugin-transform-exponentiation-operator", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.15"}, "deprecated": false, "description": "Helper function to build binary assignment operator visitors", "devDependencies": {"@babel/traverse": "^7.22.15"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-builder-binary-assignment-operator-visitor", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-builder-binary-assignment-operator-visitor", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-builder-binary-assignment-operator-visitor"}, "type": "commonjs", "version": "7.22.15"}