{"_from": "@babel/plugin-transform-for-of@^7.23.6", "_id": "@babel/plugin-transform-for-of@7.23.6", "_inBundle": false, "_integrity": "sha512-aYH4ytZ0qSuBbpfhuofbg/e96oQ7U2w1Aw/UQmKT+1l39uEhUPoFS3fHevDc1G0OvewyDudfMKY1OulczHzWIw==", "_location": "/@babel/plugin-transform-for-of", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-for-of@^7.23.6", "name": "@babel/plugin-transform-for-of", "escapedName": "@babel%2fplugin-transform-for-of", "scope": "@babel", "rawSpec": "^7.23.6", "saveSpec": null, "fetchSpec": "^7.23.6"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.23.6.tgz", "_shasum": "81c37e24171b37b370ba6aaffa7ac86bcb46f94e", "_spec": "@babel/plugin-transform-for-of@^7.23.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "deprecated": false, "description": "Compile ES2015 for...of to ES5", "devDependencies": {"@babel/core": "^7.23.6", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-for-of", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-for-of", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-for-of"}, "type": "commonjs", "version": "7.23.6"}