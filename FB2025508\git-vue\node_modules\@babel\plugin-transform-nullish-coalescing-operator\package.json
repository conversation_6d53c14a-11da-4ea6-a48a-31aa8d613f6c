{"_from": "@babel/plugin-transform-nullish-coalescing-operator@^7.23.4", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.23.4", "_inBundle": false, "_integrity": "sha512-jHE9EVVqHKAQx+VePv5LLGHjmHSJR76vawFPTdlxR/LVJPfOEGxREQwQfjuZEOPTwG92X3LINSh3M40Rv4zpVA==", "_location": "/@babel/plugin-transform-nullish-coalescing-operator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-nullish-coalescing-operator@^7.23.4", "name": "@babel/plugin-transform-nullish-coalescing-operator", "escapedName": "@babel%2fplugin-transform-nullish-coalescing-operator", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.23.4.tgz", "_shasum": "45556aad123fc6e52189ea749e33ce090637346e", "_spec": "@babel/plugin-transform-nullish-coalescing-operator@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "deprecated": false, "description": "Remove nullish coalescing operator", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-nullish-coalescing-operator", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "type": "commonjs", "version": "7.23.4"}