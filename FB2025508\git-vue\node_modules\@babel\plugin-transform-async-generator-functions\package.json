{"_from": "@babel/plugin-transform-async-generator-functions@^7.23.9", "_id": "@babel/plugin-transform-async-generator-functions@7.23.9", "_inBundle": false, "_integrity": "sha512-8Q3veQEDGe14dTYuwagbRtwxQDnytyg1JFu4/HwEMETeofocrB0U0ejBJIXoeG/t2oXZ8kzCyI0ZZfbT80VFNQ==", "_location": "/@babel/plugin-transform-async-generator-functions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-async-generator-functions@^7.23.9", "name": "@babel/plugin-transform-async-generator-functions", "escapedName": "@babel%2fplugin-transform-async-generator-functions", "scope": "@babel", "rawSpec": "^7.23.9", "saveSpec": null, "fetchSpec": "^7.23.9"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.23.9.tgz", "_shasum": "9adaeb66fc9634a586c5df139c6240d41ed801ce", "_spec": "@babel/plugin-transform-async-generator-functions@^7.23.9", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-remap-async-to-generator": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "deprecated": false, "description": "Turn async generator functions into ES2015 generators", "devDependencies": {"@babel/core": "^7.23.9", "@babel/helper-plugin-test-runner": "^7.22.5", "babel-plugin-polyfill-corejs3": "^0.9.0", "core-js-pure": "^3.30.2"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-async-generator-functions", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "type": "commonjs", "version": "7.23.9"}