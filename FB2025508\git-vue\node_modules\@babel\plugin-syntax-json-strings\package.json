{"_from": "@babel/plugin-syntax-json-strings@^7.8.3", "_id": "@babel/plugin-syntax-json-strings@7.8.3", "_inBundle": false, "_integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==", "_location": "/@babel/plugin-syntax-json-strings", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-json-strings@^7.8.3", "name": "@babel/plugin-syntax-json-strings", "escapedName": "@babel%2fplugin-syntax-json-strings", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@babel/plugin-transform-json-strings", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "_shasum": "01ca21b668cd8218c9e640cb6dd88c5412b2c96a", "_spec": "@babel/plugin-syntax-json-strings@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "deprecated": false, "description": "Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "devDependencies": {"@babel/core": "^7.8.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-json-strings", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings"}, "version": "7.8.3"}