package com.ruoyi.faty.controller;

import com.ruoyi.common.core.utils.uuid.UUID;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.faty.mapper.UserAppMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.core.web.domain.AjaxResult.success;

@RestController
@RequestMapping("/userApp")
public class BizUserAppController {

    @Autowired
    private UserAppMapper userAppMapper;

    /**
     * 获取当前店员信息和所属城市
     */
    @GetMapping("/currentStaff")
    public AjaxResult getCurrentStaffInfo() {
        Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
        String sql = "SELECT s.staff_id, s.staff_name, s.staff_code, s.shop_id, s.gender, " +
                "sh.shop_name, sh.shop_code, r.region_id, r.region_name " +
                "FROM biz_staff s " +
                "JOIN biz_shop sh ON s.shop_id = sh.shop_id " +
                "JOIN biz_region r ON sh.region_id = r.region_id " +
                "WHERE s.user_id = " + userId;




        Map<String, Object> staffInfo = userAppMapper.selectOneBySql(sql);



        String sqlUser = "SELECT DISTINCT\n" +
                "    a.user_id,\n" +
//                "    a.user_name,\n" +
//                "    a.user_code,\n" +
//                "    a.phone,\n" +
//                "    a.gender,\n" +
//                "    a.user_status,\n" +
//                "    o.order_id,\n" +
                "    o.order_no \n" +
//                "    o.order_time,\n" +
//                "    o.current_stage_id,\n" +
//                "    ua.assignment_id,\n" +
//                "    ua.assignment_time\n" +
                "FROM\n" +
                "    biz_app_user a\n" +
                "JOIN\n" +
                "    biz_order o ON a.user_id = o.user_id\n" +
                "JOIN\n" +
                "    biz_user_assignment ua ON a.user_id = ua.user_id\n" +
                "WHERE\n" +
                "    o.status = '0'\n" +
                "    AND ua.staff_id = '"+staffInfo.get("staff_id").toString()+"' \n" +
                "    AND ua.status = '0' \n" +
                "    AND a.isShow = '1' \n" +
                "ORDER BY\n" +
                "    o.order_time DESC;";
        Map<String, Object> appuser =  userAppMapper.selectOneBySql(sqlUser);




        staffInfo.put("order_no", appuser.get("order_no"));

        return success(staffInfo);
    }

    /**
     * 获取当前店员信息和所属城市
     */
    @GetMapping("/currentUser")
    public AjaxResult getCurrentUserInfo() {
        Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
        String sqluser = "select user_id from biz_app_user where sys_userid= "+userId;
        Map<String, Object> userInfo = userAppMapper.selectOneBySql(sqluser);
        String sqlorder = "select  staff_id,order_no  from biz_order where status= '0' and current_stage_id is null and user_id= "+userInfo.get("user_id").toString();
        Map<String, Object> orderInfo = userAppMapper.selectOneBySql(sqlorder);

        String sql = "SELECT s.staff_id, s.staff_name, s.staff_code, s.shop_id, s.gender, " +
                "sh.shop_name, sh.shop_code, r.region_id, r.region_name " +
                "FROM biz_staff s " +
                "JOIN biz_shop sh ON s.shop_id = sh.shop_id " +
                "JOIN biz_region r ON sh.region_id = r.region_id " +
                "WHERE s.staff_id = " + orderInfo.get("staff_id").toString();




        Map<String, Object> staffInfo = userAppMapper.selectOneBySql(sql);




        staffInfo.put("order_no", orderInfo.get("order_no"));

        return success(staffInfo);
    }


    /**
     * 根据首字母获取店铺列表
     */
    @GetMapping("/shops")
    public AjaxResult getShopsByLetter(@RequestParam String letter, @RequestParam Long regionId) {
        String sql = "SELECT shop_id, shop_name, shop_code " +
                "FROM biz_shop " +
                "WHERE region_id = " + regionId + " " +
                "AND shop_code LIKE '" + letter + "%' " +
                "ORDER BY shop_code";

        List<Map<String, Object>> shops = userAppMapper.selectListBySql(sql);
        return success(shops);
    }

    /**
     * 根据店铺ID获取店员列表
     */
    @GetMapping("/staffs")
    public AjaxResult getStaffsByShopId(@RequestParam Long shopId) {
        String sql = "SELECT staff_id, staff_name, staff_code, gender " +
                "FROM biz_staff " +
                "WHERE  loginstatus = '1' and shop_id = " + shopId + " " +
                "ORDER BY staff_code";

        List<Map<String, Object>> staffs = userAppMapper.selectListBySql(sql);
        return success(staffs);
    }

    /**
     * 根据店员ID获取用户列表
     */
    @GetMapping("/users")
    public AjaxResult getUsersByStaffId(@RequestParam Long staffId) {
        String sql = "SELECT u.user_id, u.user_name, u.user_code, u.gender " +
                "FROM biz_app_user u " +
                "JOIN biz_user_assignment a ON u.user_id = a.user_id " +
                "WHERE a.staff_id = " + staffId + " " +
                "AND a.status = '0' " + // 正常状态的分配
                "ORDER BY u.user_code";

        List<Map<String, Object>> users = userAppMapper.selectListBySql(sql);
        return success(users);
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/user/{userId}")
    public AjaxResult getUserDetail(@PathVariable("userId") Long userId) {
        String sql = "SELECT u.*, o.occupation_name " +
                "FROM biz_app_user u " +
                "LEFT JOIN biz_occupation o ON u.occupation_id = o.occupation_id " +
                "WHERE u.user_id = " + userId;

        Map<String, Object> userDetail = userAppMapper.selectOneBySql(sql);
        return success(userDetail);
    }

    /**
     * 验证是否可以创建订单
     */
    @GetMapping("/validateOrder")
    public AjaxResult validateOrder(@RequestParam Long maleUserId, @RequestParam Long femaleUserId) {
        // 检查这两个用户是否有正在执行中的订单
        String activeOrderSql = "SELECT COUNT(*) AS count FROM biz_order " +
                "WHERE (user_id = " + maleUserId + " OR user_id = " + femaleUserId + ") " +
                "AND (status = '0' or status = '2')"; // 0表示进行中

        Map<String, Object> activeOrderCount = userAppMapper.selectOneBySql(activeOrderSql);
        if (Integer.parseInt(activeOrderCount.get("count").toString()) > 0) {
            return AjaxResult.error("存在正在执行中的或者冻结中的订单，无法创建新订单");
        }

        // 检查这两个用户是否之前匹配过
        String matchedBeforeSql = "SELECT COUNT(*) AS count FROM biz_order " +
                "WHERE user_id = " + maleUserId + " " +
                "AND EXISTS (SELECT 1 FROM biz_order WHERE user_id = " + femaleUserId + ")";

        Map<String, Object> matchedBefore = userAppMapper.selectOneBySql(matchedBeforeSql);
        if (Integer.parseInt(matchedBefore.get("count").toString()) > 0) {
            return AjaxResult.error("这两位用户之前已经匹配过，无法创建新订单");
        }

        return success("可以创建订单");
    }

    /**
     * 创建订单
     */
    @PostMapping("/createOrder")
    public AjaxResult createOrder(@RequestBody Map<String, Object> orderData) {
        try {
            Long currentUserId =  Long.parseLong(orderData.get("currentStaffId").toString());
            Long maleUserId = Long.parseLong(orderData.get("maleUserId").toString());
            Long femaleUserId = Long.parseLong(orderData.get("femaleUserId").toString());
            Long maleStaffId = Long.parseLong(orderData.get("maleStaffId").toString());
            Long femaleStaffId = Long.parseLong(orderData.get("femaleStaffId").toString());
            Long maleShopId = Long.parseLong(orderData.get("maleShopId").toString());
            Long femaleShopId = Long.parseLong(orderData.get("femaleShopId").toString());
            Long regionId = Long.parseLong(orderData.get("regionId").toString());

            // 获取订单编号 - 使用时间戳加随机数
            String orderNo = "HZ" + System.currentTimeMillis() + (int)(Math.random() * 1000);

            // 为男性用户创建订单
            String maleOrderSql = "INSERT INTO biz_order (order_no, user_id, staff_id, shop_id, region_id, " +
                    "order_time, status, create_time, isdelete) VALUES ('" +
                    orderNo + "', " + maleUserId + ", " + maleStaffId + ", " + maleShopId + ", " +
                    regionId + ", NOW(), '0', NOW(), '0')";

            userAppMapper.insertBySql(maleOrderSql);

            // 为女性用户创建订单
            // String femaleOrderNo = "HZ" + System.currentTimeMillis() + (int)(Math.random() * 1000);
            String femaleOrderSql = "INSERT INTO biz_order (order_no, user_id, staff_id, shop_id, region_id, " +
                    "order_time, status, create_time, isdelete) VALUES ('" +
                    orderNo + "', " + femaleUserId + ", " + femaleStaffId + ", " + femaleShopId + ", " +
                    regionId + ", NOW(), '0', NOW(), '0')";

            userAppMapper.insertBySql(femaleOrderSql);

            // 查询订单详细信息
            String orderDetailSql = "SELECT order_id, order_no, order_time, status, " +
                    "DATEDIFF(NOW(), order_time) as days_elapsed " +
                    "FROM biz_order WHERE order_no = '" + orderNo + "' LIMIT 1";

            Map<String, Object> orderDetail = userAppMapper.selectOneBySql(orderDetailSql);

            // 计算订单周期
            int daysElapsed = Integer.parseInt(orderDetail.get("days_elapsed").toString());
            double monthsElapsed = daysElapsed / 30.0;

            // 返回创建的订单信息
            HashMap<String, Object> result = new HashMap<>();
            result.put("orderNo", orderNo);
            result.put("orderTime", orderDetail.get("order_time"));
            result.put("status", "0".equals(orderDetail.get("status").toString()) ? "执行中" :
                    "1".equals(orderDetail.get("status").toString()) ? "已完成" : "已取消");
            result.put("orderDays", daysElapsed);
            result.put("orderMonths", String.format("%.2f", monthsElapsed));

            return success(result);
        } catch (Exception e) {
            return AjaxResult.error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 创建订单阶段
     */
    @PostMapping("/createOrderStage")
    public AjaxResult createOrderStage(@RequestBody Map<String, Object> stageData) {
        try {
            String orderNo = stageData.get("orderNo").toString();
            Long stageId = Long.parseLong(stageData.get("stageId").toString());

            // 更新订单的当前阶段
            String updateOrderSql = "UPDATE biz_order SET current_stage_id = " + stageId +
                    " WHERE order_no = '" + orderNo + "'";

            userAppMapper.updateBySql(updateOrderSql);

            return success("订单阶段创建成功");
        } catch (Exception e) {
            return AjaxResult.error("创建订单阶段失败: " + e.getMessage());
        }
    }


    /**
     * 获取当前登录用户的订单信息
     */
    @GetMapping("/getCurrentOrderInfoExe")
    public AjaxResult getCurrentOrderInfoExe(@RequestParam String  orderNoSig) {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();

            // 查询店员信息
            String staffSql = "SELECT staff_id FROM biz_staff WHERE user_id = " + userId;
            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);

            if (staffInfo == null || !staffInfo.containsKey("staff_id")) {
                return AjaxResult.error("未找到店员信息");
            }

            Long staffId = Long.parseLong(staffInfo.get("staff_id").toString());

            // 查询最近的订单信息
            String orderSql = "SELECT o.* FROM biz_order o " +
                    "WHERE o.staff_id = " + staffId + " " +
                    "AND (o.status = '0' or o.status = '2') AND order_no = '"+orderNoSig+"'" + // 状态为执行中
                    "ORDER BY o.order_time DESC LIMIT 1";

            Map<String, Object> orderInfo = userAppMapper.selectOneBySql(orderSql);

            if (orderInfo == null || !orderInfo.containsKey("order_no")) {
                return AjaxResult.error("未找到有效订单");
            }

            String orderNo = orderInfo.get("order_no").toString();

            // 查询同一订单号的所有订单（男女用户的订单）
            String allOrdersSql = "SELECT o.*, u.user_id, u.gender, u.user_code, o.isdelete " +
                    "FROM biz_order o " +
                    "JOIN biz_app_user u ON o.user_id = u.user_id " +
                    "WHERE o.order_no = '" + orderNo + "'";

            List<Map<String, Object>> allOrders = userAppMapper.selectListBySql(allOrdersSql);

            if (allOrders.size() < 2) {
                return AjaxResult.error("订单数据不完整");
            }

            Map<String, Object> result = new HashMap<>();
            Map<String, Object> maleOrder = null;
            Map<String, Object> femaleOrder = null;

            // 区分男女用户订单
            for (Map<String, Object> order : allOrders) {
                if ("0".equals(order.get("gender").toString())) {
                    maleOrder = order;
                } else {
                    femaleOrder = order;
                }
            }

            if (maleOrder == null || femaleOrder == null) {
                return AjaxResult.error("未找到男女用户完整订单");
            }

            // 计算订单周期
            int daysElapsed = 0;
            if (orderInfo.containsKey("order_time")) {
                String daysSql = "SELECT DATEDIFF(NOW(), '" + orderInfo.get("order_time") + "') as days_elapsed";
                Map<String, Object> daysResult = userAppMapper.selectOneBySql(daysSql);
                if (daysResult != null && daysResult.containsKey("days_elapsed")) {
                    daysElapsed = Integer.parseInt(daysResult.get("days_elapsed").toString());
                }
            }

            double monthsElapsed = daysElapsed / 30.0;

            // 订单基本信息
            result.put("orderNo", orderNo);
            result.put("orderTime", orderInfo.get("order_time"));
            result.put("status", "0".equals(orderInfo.get("status").toString()) ? "执行中" :
                    "1".equals(orderInfo.get("status").toString()) ? "已完成" : "已取消");
            result.put("orderDays", daysElapsed);
            result.put("orderMonths", String.format("%.2f", monthsElapsed));

            // 男女用户信息
            result.put("maleUserId", maleOrder.get("user_id"));
            result.put("maleUserCode", maleOrder.get("user_code"));
            result.put("femaleUserId", femaleOrder.get("user_id"));
            result.put("femaleUserCode", femaleOrder.get("user_code"));
            result.put("maleStaffId", maleOrder.get("staff_id"));
            result.put("femaleStaffId", femaleOrder.get("staff_id"));
            result.put("maleShopId", maleOrder.get("shop_id"));
            result.put("femaleShopId", femaleOrder.get("shop_id"));

            // 添加订单阶段信息
            result.put("maleOrder", maleOrder);
            result.put("femaleOrder", femaleOrder);

            return success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取订单信息失败: " + e.getMessage());
        }
    }


    /**
     * 获取当前登录用户的订单信息
     */
    @GetMapping("/getCurrentUserInfoExe")
    public AjaxResult getCurrentUserInfoExe(@RequestParam String  orderNoSig) {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
            String sqluser = "select user_id from biz_app_user where sys_userid= "+userId;
            Map<String, Object> userInfo = userAppMapper.selectOneBySql(sqluser);
            String sqlorder = "select  staff_id,order_no  from biz_order where status= '0' and current_stage_id is null and user_id= "+userInfo.get("user_id").toString();
            Map<String, Object> userorderInfo = userAppMapper.selectOneBySql(sqlorder);


            // 查询店员信息
//            String staffSql = "SELECT staff_id FROM biz_staff WHERE user_id = " + userId;
//            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);
//
//            if (staffInfo == null || !staffInfo.containsKey("staff_id")) {
//                return AjaxResult.error("未找到店员信息");
//            }

            Long staffId = Long.parseLong(userorderInfo.get("staff_id").toString());

            // 查询最近的订单信息
            String orderSql = "SELECT o.* FROM biz_order o " +
                    "WHERE o.staff_id = " + staffId + " " +
                    "AND  o.status = '0'  AND current_stage_id is null  AND order_no = '"+orderNoSig+"'" + // 状态为执行中
                    "ORDER BY o.order_time DESC LIMIT 1";

            Map<String, Object> orderInfo = userAppMapper.selectOneBySql(orderSql);

            if (orderInfo == null || !orderInfo.containsKey("order_no")) {
                return AjaxResult.error("未找到有效订单");
            }

            String orderNo = userorderInfo.get("order_no").toString();

            // 查询同一订单号的所有订单（男女用户的订单）
            String allOrdersSql = "SELECT o.*, u.user_id, u.gender, u.user_code, o.isdelete " +
                    "FROM biz_order o " +
                    "JOIN biz_app_user u ON o.user_id = u.user_id " +
                    "WHERE  current_stage_id is null and o.order_no = '" + orderNo + "'";

            List<Map<String, Object>> allOrders = userAppMapper.selectListBySql(allOrdersSql);

            if (allOrders.size() < 2) {
                return AjaxResult.error("订单数据不完整");
            }

            Map<String, Object> result = new HashMap<>();
            Map<String, Object> maleOrder = null;
            Map<String, Object> femaleOrder = null;
            String isShow ="";
            // 区分男女用户订单
            for (Map<String, Object> order : allOrders) {

                if ("0".equals(order.get("gender").toString())) {
                    if (userInfo.get("user_id").toString().equals(order.get("user_id").toString())) {
                        isShow = "0";
                    }
                    maleOrder = order;
                } else {
                    if (userInfo.get("user_id").toString().equals(order.get("user_id").toString())) {
                        isShow = "1";
                    }
                    femaleOrder = order;
                }
            }

            if (maleOrder == null || femaleOrder == null) {
                return AjaxResult.error("未找到男女用户完整订单");
            }

            // 计算订单周期
            int daysElapsed = 0;
            if (orderInfo.containsKey("order_time")) {
                String daysSql = "SELECT DATEDIFF(NOW(), '" + orderInfo.get("order_time") + "') as days_elapsed";
                Map<String, Object> daysResult = userAppMapper.selectOneBySql(daysSql);
                if (daysResult != null && daysResult.containsKey("days_elapsed")) {
                    daysElapsed = Integer.parseInt(daysResult.get("days_elapsed").toString());
                }
            }

            double monthsElapsed = daysElapsed / 30.0;

            // 订单基本信息
            result.put("orderNo", orderNo);
            result.put("orderTime", orderInfo.get("order_time"));
            result.put("status", "0".equals(orderInfo.get("status").toString()) ? "执行中" :
                    "1".equals(orderInfo.get("status").toString()) ? "已完成" : "已取消");
            result.put("orderDays", daysElapsed);
            result.put("orderMonths", String.format("%.2f", monthsElapsed));

            // 男女用户信息
            result.put("maleUserId", maleOrder.get("user_id"));
            result.put("maleUserCode", maleOrder.get("user_code"));
            result.put("femaleUserId", femaleOrder.get("user_id"));
            result.put("femaleUserCode", femaleOrder.get("user_code"));
            result.put("maleStaffId", maleOrder.get("staff_id"));
            result.put("femaleStaffId", femaleOrder.get("staff_id"));
            result.put("maleShopId", maleOrder.get("shop_id"));
            result.put("femaleShopId", femaleOrder.get("shop_id"));
            result.put("isShow",isShow);
            // 添加订单阶段信息
            result.put("maleOrder", maleOrder);
            result.put("femaleOrder", femaleOrder);

            return success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取订单信息失败: " + e.getMessage());
        }
    }
    /**
     * 获取当前登录用户的订单信息
     */
    @GetMapping("/getCurrentOrderInfoFinish")
    public AjaxResult getCurrentOrderInfoFinish(@RequestParam String  orderNoSig) {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();

            // 查询店员信息
            String staffSql = "SELECT staff_id FROM biz_staff WHERE user_id = " + userId;
            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);

            if (staffInfo == null || !staffInfo.containsKey("staff_id")) {
                return AjaxResult.error("未找到店员信息");
            }

            Long staffId = Long.parseLong(staffInfo.get("staff_id").toString());

            // 查询最近的订单信息
            String orderSql = "SELECT o.* FROM biz_order o " +
                    "WHERE o.staff_id = " + staffId + " " +
                    "AND (o.status = '1') AND order_no = '"+orderNoSig+"'" + // 状态为执行中
                    "ORDER BY o.order_time DESC LIMIT 1";

            Map<String, Object> orderInfo = userAppMapper.selectOneBySql(orderSql);

            if (orderInfo == null || !orderInfo.containsKey("order_no")) {
                return AjaxResult.error("未找到有效订单");
            }

            String orderNo = orderInfo.get("order_no").toString();

            // 查询同一订单号的所有订单（男女用户的订单）
            String allOrdersSql = "SELECT o.*, u.user_id, u.gender, u.user_code, o.isdelete " +
                    "FROM biz_order o " +
                    "JOIN biz_app_user u ON o.user_id = u.user_id " +
                    "WHERE o.order_no = '" + orderNo + "'";

            List<Map<String, Object>> allOrders = userAppMapper.selectListBySql(allOrdersSql);

            if (allOrders.size() < 2) {
                return AjaxResult.error("订单数据不完整");
            }

            Map<String, Object> result = new HashMap<>();
            Map<String, Object> maleOrder = null;
            Map<String, Object> femaleOrder = null;

            // 区分男女用户订单
            for (Map<String, Object> order : allOrders) {
                if ("0".equals(order.get("gender").toString())) {
                    maleOrder = order;
                } else {
                    femaleOrder = order;
                }
            }

            if (maleOrder == null || femaleOrder == null) {
                return AjaxResult.error("未找到男女用户完整订单");
            }

            // 计算订单周期
            int daysElapsed = 0;
            if (orderInfo.containsKey("order_time")) {
                String daysSql = "SELECT DATEDIFF(NOW(), '" + orderInfo.get("order_time") + "') as days_elapsed";
                Map<String, Object> daysResult = userAppMapper.selectOneBySql(daysSql);
                if (daysResult != null && daysResult.containsKey("days_elapsed")) {
                    daysElapsed = Integer.parseInt(daysResult.get("days_elapsed").toString());
                }
            }

            double monthsElapsed = daysElapsed / 30.0;

            // 订单基本信息
            result.put("orderNo", orderNo);
            result.put("orderTime", orderInfo.get("order_time"));
            result.put("status", "0".equals(orderInfo.get("status").toString()) ? "执行中" :
                    "1".equals(orderInfo.get("status").toString()) ? "已完成" : "已取消");
            result.put("orderDays", daysElapsed);
            result.put("orderMonths", String.format("%.2f", monthsElapsed));

            // 男女用户信息
            result.put("maleUserId", maleOrder.get("user_id"));
            result.put("maleUserCode", maleOrder.get("user_code"));
            result.put("femaleUserId", femaleOrder.get("user_id"));
            result.put("femaleUserCode", femaleOrder.get("user_code"));
            result.put("maleStaffId", maleOrder.get("staff_id"));
            result.put("femaleStaffId", femaleOrder.get("staff_id"));
            result.put("maleShopId", maleOrder.get("shop_id"));
            result.put("femaleShopId", femaleOrder.get("shop_id"));

            // 添加订单阶段信息
            result.put("maleOrder", maleOrder);
            result.put("femaleOrder", femaleOrder);

            return success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取订单信息失败: " + e.getMessage());
        }
    }





    /**
     * 获取当前登录用户的订单信息
     */
    @GetMapping("/getCurrentOrderInfo")
    public AjaxResult getCurrentOrderInfo() {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();

            // 查询店员信息
            String staffSql = "SELECT staff_id FROM biz_staff WHERE user_id = " + userId;
            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);

            if (staffInfo == null || !staffInfo.containsKey("staff_id")) {
                return AjaxResult.error("未找到店员信息");
            }

            Long staffId = Long.parseLong(staffInfo.get("staff_id").toString());

            // 查询最近的订单信息
            String orderSql = "SELECT o.* FROM biz_order o " +
                    "WHERE o.staff_id = " + staffId + " " +
                    "AND o.status = '0' " + // 状态为执行中
                    "ORDER BY o.order_time DESC LIMIT 1";

            Map<String, Object> orderInfo = userAppMapper.selectOneBySql(orderSql);

            if (orderInfo == null || !orderInfo.containsKey("order_no")) {
                return AjaxResult.error("未找到有效订单");
            }

            String orderNo = orderInfo.get("order_no").toString();

            // 查询同一订单号的所有订单（男女用户的订单）
            String allOrdersSql = "SELECT o.*, u.user_id, u.gender, u.user_code, o.isdelete " +
                    "FROM biz_order o " +
                    "JOIN biz_app_user u ON o.user_id = u.user_id " +
                    "WHERE o.order_no = '" + orderNo + "'";

            List<Map<String, Object>> allOrders = userAppMapper.selectListBySql(allOrdersSql);

            if (allOrders.size() < 2) {
                return AjaxResult.error("订单数据不完整");
            }

            Map<String, Object> result = new HashMap<>();
            Map<String, Object> maleOrder = null;
            Map<String, Object> femaleOrder = null;

            // 区分男女用户订单
            for (Map<String, Object> order : allOrders) {
                if ("0".equals(order.get("gender").toString())) {
                    maleOrder = order;
                } else {
                    femaleOrder = order;
                }
            }

            if (maleOrder == null || femaleOrder == null) {
                return AjaxResult.error("未找到男女用户完整订单");
            }

            // 计算订单周期
            int daysElapsed = 0;
            if (orderInfo.containsKey("order_time")) {
                String daysSql = "SELECT DATEDIFF(NOW(), '" + orderInfo.get("order_time") + "') as days_elapsed";
                Map<String, Object> daysResult = userAppMapper.selectOneBySql(daysSql);
                if (daysResult != null && daysResult.containsKey("days_elapsed")) {
                    daysElapsed = Integer.parseInt(daysResult.get("days_elapsed").toString());
                }
            }

            double monthsElapsed = daysElapsed / 30.0;

            // 订单基本信息
            result.put("orderNo", orderNo);
            result.put("orderTime", orderInfo.get("order_time"));
            result.put("status", "0".equals(orderInfo.get("status").toString()) ? "执行中" :
                    "1".equals(orderInfo.get("status").toString()) ? "已完成" : "已取消");
            result.put("orderDays", daysElapsed);
            result.put("orderMonths", String.format("%.2f", monthsElapsed));

            // 男女用户信息
            result.put("maleUserId", maleOrder.get("user_id"));
            result.put("maleUserCode", maleOrder.get("user_code"));
            result.put("femaleUserId", femaleOrder.get("user_id"));
            result.put("femaleUserCode", femaleOrder.get("user_code"));
            result.put("maleStaffId", maleOrder.get("staff_id"));
            result.put("femaleStaffId", femaleOrder.get("staff_id"));
            result.put("maleShopId", maleOrder.get("shop_id"));
            result.put("femaleShopId", femaleOrder.get("shop_id"));

            // 添加订单阶段信息
            result.put("maleOrder", maleOrder);
            result.put("femaleOrder", femaleOrder);

            return success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取订单信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取店铺和店员信息
     */
    @GetMapping("/getShopAndStaffInfo")
    public AjaxResult getShopAndStaffInfo(@RequestParam Long staffId) {
        try {
            // 查询店员信息
            String staffSql = "SELECT s.*, sh.shop_id, sh.shop_code, sh.shop_name " +
                    "FROM biz_staff s " +
                    "JOIN biz_shop sh ON s.shop_id = sh.shop_id " +
                    "WHERE s.staff_id = " + staffId;

            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);

            if (staffInfo == null) {
                return AjaxResult.error("未找到店员信息");
            }

            return success(staffInfo);
        } catch (Exception e) {
            return AjaxResult.error("获取店铺和店员信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单阶段列表
     */
    @GetMapping("/getOrderStages")
    public AjaxResult getOrderStages(@RequestParam(required = false) Long currentStageId) {
        try {
            // 如果没有当前阶段ID，获取所有阶段
            if (currentStageId == null) {
                String stagesSql = "SELECT stage_id, stage_name, stage_code, order_num " +
                        "FROM biz_order_stage " +
                        "ORDER BY order_num";

                List<Map<String, Object>> stages = userAppMapper.selectListBySql(stagesSql);
                return success(stages);
            } else {
                // 查询当前阶段的order_num
                String currentStageSql = "SELECT order_num FROM biz_order_stage WHERE stage_id = " + currentStageId;
                Map<String, Object> currentStage = userAppMapper.selectOneBySql(currentStageSql);

                if (currentStage == null || !currentStage.containsKey("order_num")) {
                    return AjaxResult.error("未找到当前阶段信息");
                }

                int currentOrderNum = Integer.parseInt(currentStage.get("order_num").toString());

                // 查询大于当前order_num的阶段
                String nextStagesSql = "SELECT stage_id, stage_name, stage_code, order_num " +
                        "FROM biz_order_stage " +
                        "WHERE order_num > " + currentOrderNum + " " +
                        "ORDER BY order_num";

                List<Map<String, Object>> nextStages = userAppMapper.selectListBySql(nextStagesSql);
                return success(nextStages);
            }
        } catch (Exception e) {
            return AjaxResult.error("获取订单阶段列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定阶段信息
     */
    @GetMapping("/getStageInfo/{stageId}")
    public AjaxResult getStageInfo(@PathVariable("stageId") Long stageId) {
        try {
            String stageSql = "SELECT stage_id, stage_name, stage_code, order_num " +
                    "FROM biz_order_stage WHERE stage_id = " + stageId;

            Map<String, Object> stageInfo = userAppMapper.selectOneBySql(stageSql);

            if (stageInfo == null) {
                return AjaxResult.error("未找到阶段信息");
            }

            return success(stageInfo);
        } catch (Exception e) {
            return AjaxResult.error("获取阶段信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单相关的店员信息
     */
    @GetMapping("/getOrderStaffs")
    public AjaxResult getOrderStaffs(@RequestParam String orderNo) {
        try {
//            String staffsSql = "SELECT o.staff_id, s.staff_name, s.nickname, s.phone " +
//                    "FROM biz_order o " +
//                    "JOIN biz_staff s ON o.staff_id = s.staff_id " +
//                    "WHERE o.order_no = '" + orderNo + "'";

            String staffsSql = "SELECT DISTINCT \n" +
                    "    o.staff_id, \n" +
                    "    s.staff_name, \n" +
                    "    s.nickname, \n" +
                    "    s.phone \n" +
                    "FROM \n" +
                    "    biz_order o \n" +
                    "JOIN \n" +
                    "    biz_staff s ON o.staff_id = s.staff_id \n" +
                    "WHERE \n" +
                    "    o.order_no = '" + orderNo + "'";



            List<Map<String, Object>> staffs = userAppMapper.selectListBySql(staffsSql);

            if (staffs.isEmpty()) {
                return AjaxResult.error("未找到订单相关的店员信息");
            }

            return success(staffs);
        } catch (Exception e) {
            return AjaxResult.error("获取订单店员信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存订单阶段
     */
    @PostMapping("/saveOrderStage")
    public AjaxResult saveOrderStage(@RequestBody Map<String, Object> stageData) {
        try {
            Long currentUserId =  Long.parseLong(stageData.get("currentStaffId").toString());
            String orderNo = stageData.get("orderNo").toString();
            Long stageId = Long.parseLong(stageData.get("stageId").toString());
            Long staffId = Long.parseLong(stageData.get("staffId").toString());
            Long maleUserId = Long.parseLong(stageData.get("maleUserId").toString());
            Long femaleUserId = Long.parseLong(stageData.get("femaleUserId").toString());
            Long maleStaffId = Long.parseLong(stageData.get("maleStaffId").toString());
            Long femaleStaffId = Long.parseLong(stageData.get("femaleStaffId").toString());
            Long maleShopId = Long.parseLong(stageData.get("maleShopId").toString());
            Long femaleShopId = Long.parseLong(stageData.get("femaleShopId").toString());
            Long regionId = Long.parseLong(stageData.get("regionId").toString());
            // 更新特定店员对应的订单阶段
            String updateOrderSql = "Select * from  biz_order WHERE order_no = '" + orderNo + "' AND staff_id = " + staffId+" LIMIT 1";

            List<Map<String, Object>> rows = userAppMapper.selectListBySql(updateOrderSql);
            if (rows.size() == 0) {
                return AjaxResult.error("未找到匹配的订单记录");
            }
            // 为男性用户创建订单
            String maleOrderSql="";
            String femaleOrderSql="";
            String struuid = UUID.randomUUID().toString().replace("-","");
            if(currentUserId.equals(maleStaffId))
            {
                maleOrderSql = "INSERT INTO biz_order (order_no,current_stage_id, user_id, staff_id, shop_id, region_id, " +
                        "order_time, status, create_time,uuidsign,isBuild) VALUES ('" +
                        orderNo + "'," + stageId + ", " + maleUserId + ", " + maleStaffId + ", " + maleShopId + ", " +
                        regionId + ", NOW(), '0', NOW(),'" + struuid +"','1')";
            }else
            {
                maleOrderSql = "INSERT INTO biz_order (order_no, current_stage_id,user_id, staff_id, shop_id, region_id, " +
                        "order_time, status, create_time,uuidsign,isBuild) VALUES ('" +
                        orderNo + "'," + stageId + "," + maleUserId + ", " + maleStaffId + ", " + maleShopId + ", " +
                        regionId + ", NOW(), '0', NOW(),'" + struuid +"','0')";
            }


            userAppMapper.insertBySql(maleOrderSql);

            if(currentUserId.equals(femaleStaffId)) {
                femaleOrderSql = "INSERT INTO biz_order (order_no,current_stage_id, user_id, staff_id, shop_id, region_id, " +
                        "order_time, status, create_time,uuidsign,isBuild) VALUES ('" +
                        orderNo + "'," + stageId + ",  " + femaleUserId + ", " + femaleStaffId + ", " + femaleShopId + ", " +
                        regionId + ", NOW(), '0', NOW() ,'" + struuid +"','1')";
            }else
            {
                femaleOrderSql = "INSERT INTO biz_order (order_no,current_stage_id, user_id, staff_id, shop_id, region_id, " +
                        "order_time, status, create_time,uuidsign,isBuild) VALUES ('" +
                        orderNo + "'," + stageId + "," + femaleUserId + ", " + femaleStaffId + ", " + femaleShopId + ", " +
                        regionId + ", NOW(), '0', NOW(),'" + struuid +"','0')";
            }

            userAppMapper.insertBySql(femaleOrderSql);

            // 获取阶段名称
            String stageSql = "SELECT stage_name FROM biz_order_stage WHERE stage_id = " + stageId;
            Map<String, Object> stageInfo = userAppMapper.selectOneBySql(stageSql);

            // 检查是否是"交往阶段"
            if (stageInfo != null && stageInfo.containsKey("stage_name") && "交往阶段".equals(stageInfo.get("stage_name"))) {
                // 如果是交往阶段，获取第一个服务项目
                String sqlshop = "SELECT shop_id FROM biz_staff WHERE staff_id = " + staffId;
                Map<String, Object> staffmap = userAppMapper.selectOneBySql(sqlshop);

                // 先查询店铺服务
                String serviceSql = "SELECT service_id, service_name, service_code, price " +
                        "FROM biz_service_item WHERE is_system = '0' " +
                        "AND shop_id = '" + staffmap.get("shop_id").toString() + "' " +
                        "ORDER BY service_code LIMIT 1";

                List<Map<String, Object>> services = userAppMapper.selectListBySql(serviceSql);

                // 如果没有店铺服务，则获取系统服务
                if (services == null || services.isEmpty()) {
                    serviceSql = "SELECT service_id, service_name, service_code, price " +
                            "FROM biz_service_item WHERE is_system = '1' " +
                            "ORDER BY service_code LIMIT 1";
                    services = userAppMapper.selectListBySql(serviceSql);
                }

                // 如果有服务项目，自动创建第一个服务
                if (services != null && !services.isEmpty()) {
                    Map<String, Object> firstService = services.get(0);
                    Long serviceId = Long.parseLong(firstService.get("service_id").toString());
                    String serviceName = firstService.get("service_name").toString();
                    Double price = Double.parseDouble(firstService.get("price").toString());

                    // 获取订单ID
                    String orderIdSql = "SELECT * FROM biz_order WHERE order_no = '" + orderNo + "' and staff_id = " + staffId + " and current_stage_id = " + stageId;
                    List<Map<String, Object>> orderIds = userAppMapper.selectListBySql(orderIdSql);

                    // 为所有相关订单创建服务记录
                    if (!orderIds.isEmpty()) {
                        for (Map<String, Object> orderIdMap : orderIds) {
                            Long order_id = Long.parseLong(orderIdMap.get("order_id").toString());
                            String insertSql = "INSERT INTO biz_order_service (order_id,order_no, service_id, service_name, price, staff_id, service_time, create_time,current_stage_id) " +
                                    "VALUES (" + order_id + ",'" + orderNo + "', " + serviceId + ", '" + serviceName + "', " + price + ", " + staffId + ", NOW(), NOW(),'" + stageId + "')";

                            userAppMapper.insertBySql(insertSql);
                        }
                    }
                }
            }

            HashMap<String, Object> result = new HashMap<>();
            result.put("stageId", stageId);
            result.put("stageName", stageInfo.get("stage_name"));

            return success(result);
        } catch (Exception e) {
            return AjaxResult.error("保存订单阶段失败: " + e.getMessage());
        }
    }

    /**
     * 检查阶段是否可以删除（没有服务及收费）
     */
    @GetMapping("/checkStageCanDelete")
    public AjaxResult checkStageCanDelete(@RequestParam Long stageId, @RequestParam String orderNo,@RequestParam String staffId) {
        try {
            // 检查是否有服务及收费
            String serviceSql = "SELECT *  FROM biz_order where current_stage_id = '" + stageId + "' and order_no = '" + orderNo + "' and staff_id = " + staffId;

            Map<String, Object> serviceCount = userAppMapper.selectOneBySql(serviceSql);
            if (serviceCount == null) {
                return AjaxResult.error("该阶段存不存在，不能删除");
            }
            if (serviceCount.get("isBuild").toString().equals("0")) {
                return AjaxResult.error("该阶段不是此用户创建，不能删除");
            }
            return success(true);
        } catch (Exception e) {
            return AjaxResult.error("检查阶段是否可删除失败: " + e.getMessage());
        }
    }

    /**
     * 删除订单阶段
     */
    @PostMapping("/deleteOrderStage")
    public AjaxResult deleteOrderStage(@RequestBody Map<String, Object> stageData) {
        try {
            String orderNo = stageData.get("orderNo").toString();
            Long stageId = Long.parseLong(stageData.get("stageId").toString());
            Long staffId = Long.parseLong(stageData.get("staffId").toString());

            // 首先检查是否有权限删除（是否是创建者）
            String checkPermissionSql = "SELECT * FROM biz_order " +
                    "WHERE order_no = '" + orderNo + "' " +
                    "AND staff_id = " + staffId + " " +
                    "AND current_stage_id = " + stageId;

            Map<String, Object> permissionCount = userAppMapper.selectOneBySql(checkPermissionSql);

//            if (permissionCount == null || Integer.parseInt(permissionCount.get("count").toString()) == 0) {
//                return AjaxResult.error("您没有权限删除此阶段");
//            }

            // 检查是否有服务及收费
//            String serviceSql = "SELECT COUNT(*) AS count FROM biz_order_service s " +
//                    "JOIN biz_order o ON s.order_id = o.order_id " +
//                    "WHERE o.order_no = '" + orderNo + "' " +
//                    "AND s.service_id = " + stageId;
//
//            Map<String, Object> serviceCount = userAppMapper.selectOneBySql(serviceSql);
//
//            if (serviceCount != null && Integer.parseInt(serviceCount.get("count").toString()) > 0) {
//                return AjaxResult.error("该阶段存在服务及收费记录，不能删除");
//            }

            // 删除阶段（将current_stage_id设为null）
            String deleteOrderSql = "DELETE FROM biz_order " +
                    "WHERE order_no = '" + orderNo + "' " +
                    "AND uuidsign = '"+permissionCount.get("uuidsign").toString()+"'";

            int rows = userAppMapper.deleteBySql(deleteOrderSql);

            if (rows == 0) {
                return AjaxResult.error("删除阶段失败");
            }

            return success("阶段删除成功");
        } catch (Exception e) {
            return AjaxResult.error("删除阶段失败: " + e.getMessage());
        }
    }

    /**
     * 获取服务项目列表
     */
    @GetMapping("/getServiceItems")
    public AjaxResult getServiceItems(@RequestParam String staffId) {
        try {

            String sqlshop = "SELECT shop_id FROM biz_staff WHERE staff_id = " + staffId;
            Map<String, Object> staffmap = userAppMapper.selectOneBySql(sqlshop);

            String sql = "SELECT service_id, service_name, price, remark " +
                    "FROM biz_service_item where is_system = '0' " +
                    "AND shop_id = '"+staffmap.get("shop_id").toString()+"' " +
                    " ORDER BY service_id";

            List<Map<String, Object>> services = userAppMapper.selectListBySql(sql);
            if (services==null||services.size()==0) {
                sql = "SELECT service_id, service_name, price, remark " +
                        "FROM biz_service_item where is_system = '1' " +
                        " ORDER BY service_id";
                services = userAppMapper.selectListBySql(sql);
            }
            return success(services);
        } catch (Exception e) {
            return AjaxResult.error("获取服务项目列表失败: " + e.getMessage());
        }
    }

    /**
     * 保存订单服务
     */
    @PostMapping("/saveOrderService")
    public AjaxResult saveOrderService(@RequestBody Map<String, Object> serviceData) {
        try {
            String currentStaffId = serviceData.get("currentStaffId").toString();
            String currentStageId = serviceData.get("currentStageId").toString();
            String orderNo = serviceData.get("orderNo").toString();
            Long serviceId = Long.parseLong(serviceData.get("serviceId").toString());
            String serviceName = serviceData.get("serviceName").toString();
            Double price = Double.parseDouble(serviceData.get("price").toString());
            Long staffId = Long.parseLong(serviceData.get("staffId").toString());
            Double totalprice =0.0;
            // 获取订单ID
            String orderIdSql = "SELECT * FROM biz_order WHERE  (current_stage_id is null or current_stage_id ='') and   order_no = '" + orderNo + "' and staff_id = " + currentStaffId + "";
            List<Map<String, Object>> orderIds = userAppMapper.selectListBySql(orderIdSql);

            if (orderIds.isEmpty()) {
                return AjaxResult.error("未找到订单信息");
            }
            totalprice =  price+Double.valueOf(orderIds.get(0).get("order_amount").toString());
            BigDecimal bigDecimal = BigDecimal.valueOf(totalprice);

            String strsqlupdte="UPDATE biz_order \n" +
                    "SET order_amount = "+bigDecimal+", \n" +
                    "    update_time = NOW()  \n" +
                    "WHERE order_no = '"+orderNo+"' and (current_stage_id is null or current_stage_id ='')";
            int inum = userAppMapper.updateBySql(strsqlupdte);
            // 为所有相关订单创建服务记录
            for (Map<String, Object> orderIdMap : orderIds) {
                // Long staff_id = Long.parseLong(orderIdMap.get("staff_id").toString());
                Long order_id = Long.parseLong(orderIdMap.get("order_id").toString());
                String insertSql = "INSERT INTO biz_order_service (order_id,order_no, service_id, service_name, price, staff_id, service_time, create_time,current_stage_id) " +
                        "VALUES (" + order_id + ",'" + orderNo + "', " + serviceId + ", '" + serviceName + "', " + price + ", " + staffId + ", NOW(), NOW(),'"+currentStageId+"')";

                userAppMapper.insertBySql(insertSql);
            }

            return success("服务创建成功");
        } catch (Exception e) {
            return AjaxResult.error("保存订单服务失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单服务列表
     */
    @GetMapping("/getOrderServices")
    public AjaxResult getOrderServices(@RequestParam String orderNo) {
        try {

            String sql ="SELECT s.id, \n" +
                    "       s.current_stage_id, \n" +
                    "       s.service_id, \n" +
                    "       s.service_name, \n" +
                    "       s.price, \n" +
                    "       s.staff_id, \n" +
                    "       DATE_FORMAT(s.service_time, '%Y-%m-%d %H:%i:%s') as service_time, \n" +
                    "       s.notify, \n" +
                    "       st.nickname as staff_nickname \n" +
                    "FROM biz_order_service s \n" +
                    "JOIN biz_order o ON s.order_id = o.order_id \n" +
                    "JOIN biz_staff st ON s.staff_id = st.staff_id \n" +
                    "WHERE o.order_no = '" + orderNo + "' \n" +
//                    "GROUP BY s.staff_id \n" +
                    "ORDER BY s.service_time DESC";


            List<Map<String, Object>> services = userAppMapper.selectListBySql(sql);

            // 处理返回数据
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> service : services) {
                Map<String, Object> serviceInfo = new HashMap<>();
                serviceInfo.put("id", service.get("id"));
                serviceInfo.put("serviceId", service.get("service_id"));
                serviceInfo.put("serviceName", service.get("service_name"));
                serviceInfo.put("price", service.get("price"));
                serviceInfo.put("staffId", service.get("staff_id"));
                serviceInfo.put("staffNickname", service.get("staff_nickname"));
                serviceInfo.put("serviceTime", service.get("service_time"));
                serviceInfo.put("notify", service.get("notify"));
                serviceInfo.put("current_stage_id", service.get("current_stage_id"));

                result.add(serviceInfo);
            }

            return success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取订单服务列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/deleteOrderService")
    public AjaxResult deleteOrderService(@RequestBody Map<String, Object> serviceData) {
        try {

            String currentStaffId = serviceData.get("currentStaffId").toString();
           // String currentStageId = serviceData.get("currentStageId").toString();
            String orderNo = serviceData.get("orderNo").toString();
            Long serviceId = Long.parseLong(serviceData.get("serviceId").toString());
          //  String serviceName = serviceData.get("serviceName").toString();
            Double price = Double.parseDouble(serviceData.get("price").toString());
            //Long staffIds = Long.parseLong(serviceData.get("staffId").toString());
            Double totalprice =0.0;


            // 获取当前登录用户ID
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();

            // 查询店员信息
            String staffSql = "SELECT staff_id FROM biz_staff WHERE user_id = " + userId;
            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);

            if (staffInfo == null || !staffInfo.containsKey("staff_id")) {
                return AjaxResult.error("未找到店员信息");
            }

            Long staffId = Long.parseLong(staffInfo.get("staff_id").toString());

            // 检查是否有权限删除（是否是创建者）
            String checkPermissionSql = "SELECT COUNT(*) AS count FROM biz_order_service " +
                    "WHERE id = " + serviceId + " AND staff_id = " + staffId;

            Map<String, Object> permissionCount = userAppMapper.selectOneBySql(checkPermissionSql);

            if (permissionCount == null || Integer.parseInt(permissionCount.get("count").toString()) == 0) {
                return AjaxResult.error("您没有权限删除此服务");
            }
            String orderIdSql = "SELECT * FROM biz_order WHERE  (current_stage_id is null or current_stage_id ='') and   order_no = '" + orderNo + "' and staff_id = " + currentStaffId + "";
            List<Map<String, Object>> orderIds = userAppMapper.selectListBySql(orderIdSql);

            totalprice =  price-Double.valueOf(orderIds.get(0).get("order_amount").toString());
            BigDecimal bigDecimal = BigDecimal.valueOf(totalprice);

            String strsqlupdte="UPDATE biz_order \n" +
                    "SET order_amount = "+bigDecimal+", \n" +
                    "    update_time = NOW()  \n" +
                    "WHERE order_no = '"+orderNo+"' and (current_stage_id is null or current_stage_id ='')";
            int inum = userAppMapper.updateBySql(strsqlupdte);


            // 删除服务
            String deleteSql = "DELETE FROM biz_order_service WHERE id = " + serviceId;
            int rows = userAppMapper.updateBySql(deleteSql);

            if (rows == 0) {
                return AjaxResult.error("删除服务失败");
            }

            return success("服务删除成功");
        } catch (Exception e) {
            return AjaxResult.error("删除服务失败: " + e.getMessage());
        }
    }

    /**
     * 通知同事
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/notifyColleague/{serviceId}")
    public AjaxResult notifyColleague(@PathVariable("serviceId") Long serviceId) {
        try {
            // 更新通知状态
            String updateSql = "UPDATE biz_order_service SET notify = '1' WHERE id = " + serviceId;
            int rows = userAppMapper.updateBySql(updateSql);

            if (rows == 0) {
                return AjaxResult.error("发送通知失败");
            }
            String sqlorder= "SELECT \n" +
                    "    bo.user_id, bo.order_no\n" +
                    "FROM \n" +
                    "    biz_order bo \n" +
                    "INNER JOIN \n" +
                    "    biz_order_service bos \n" +
                    "ON \n" +
                    "    bo.order_id = bos.order_id \n" +
                    "WHERE   bos.id =  "+serviceId+" \n";
            Map<String, Object> maporder =  userAppMapper.selectOneBySql(sqlorder);

            String sqluser =  "Select DISTINCT user_id from biz_order where order_no =  '"+maporder.get("order_no").toString()+"' and  user_id !='"+Integer.valueOf(maporder.get("user_id").toString())+"'";
            Map<String, Object> mapuser =  userAppMapper.selectOneBySql(sqluser);

            String sqlupuser ="UPDATE biz_app_user\n" +
                    " SET notify = '1' \n" +
                    " WHERE user_id = '"+Integer.valueOf(mapuser.get("user_id").toString())+"'";
            rows = userAppMapper.updateBySql(sqlupuser);
            if (rows == 0) {
                return AjaxResult.error("发送通知失败");
            }
            return success("通知发送成功");
        } catch (Exception e) {
            return AjaxResult.error("发送通知失败: " + e.getMessage());
        }
    }

    /**
     * 取消通知同事
     */
    @PostMapping("/cancelNotifyColleague/{serviceId}")
    public AjaxResult cancelNotifyColleague(@PathVariable("serviceId") Long serviceId) {
        try {
            // 更新通知状态为0
            String updateSql = "UPDATE biz_order_service SET notify = '0' WHERE id = " + serviceId;
            int rows = userAppMapper.updateBySql(updateSql);

            if (rows == 0) {
                return AjaxResult.error("取消通知失败");
            }

            return success("取消通知成功");
        } catch (Exception e) {
            return AjaxResult.error("取消通知失败: " + e.getMessage());
        }
    }

    /**
     * 重置所有通知状态
     */
    @PostMapping("/resetAllNotifications")
    public AjaxResult resetAllNotifications(@RequestBody Map<String, Object> data) {
        try {
            String orderNo = data.get("orderNo").toString();

            // 重置该订单所有服务的通知状态
            String updateSql = "UPDATE biz_order_service SET notify = '0' " +
                    "WHERE order_no = '" + orderNo + "'";

            int rows = userAppMapper.updateBySql(updateSql);

            return success("重置通知状态成功，影响" + rows + "条记录");
        } catch (Exception e) {
            return AjaxResult.error("重置通知状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单的所有阶段
     */
    @GetMapping("/getAllOrderStages")
    public AjaxResult getAllOrderStages(@RequestParam String orderNo,@RequestParam String staffId) {
        try {
            // 查询该订单关联的所有阶段
            String sql = "SELECT DISTINCT o.current_stage_id as stage_id, s.stage_name,o.isBuild,o.staff_id,o.malestatus,o.femalestatus,o.male_summary,o.female_summary" +
                    " FROM biz_order o " +
                    "JOIN biz_order_stage s ON o.current_stage_id = s.stage_id " +
                    "WHERE o.order_no = '" + orderNo + "' and o.staff_id = '" + staffId +"'";
            //+ " AND s.stage_name !='订单结束总结'";
            //" AND o.current_stage_id IS NOT NULL AND s.stage_name !='订单结束总结'";

            List<Map<String, Object>> stages = userAppMapper.selectListBySql(sql);

            // 还可以查询服务表中的阶段（历史阶段）
//            String serviceSql = "SELECT DISTINCT s.service_id as stage_id, s.service_name as stage_name, s.staff_id " +
//                    "FROM biz_order_service s " +
//                    "WHERE s.order_no = '" + orderNo + "' " +
//                    "AND s.service_id NOT IN (SELECT current_stage_id FROM biz_order WHERE order_no = '" + orderNo + "' AND current_stage_id IS NOT NULL)";
//
//            List<Map<String, Object>> serviceStages = userAppMapper.selectListBySql(serviceSql);
//
//            // 合并两个列表
//            stages.addAll(serviceStages);

            return success(stages);
        } catch (Exception e) {
            return AjaxResult.error("获取订单阶段失败: " + e.getMessage());
        }
    }

    /**
     * 检查服务是否可以删除
     */
    @GetMapping("/checkServiceCanDelete")
    public AjaxResult checkServiceCanDelete(@RequestParam Long serviceId) {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();

            // 查询店员信息
            String staffSql = "SELECT staff_id FROM biz_staff WHERE user_id = " + userId;
            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);

            if (staffInfo == null || !staffInfo.containsKey("staff_id")) {
                return AjaxResult.error("未找到店员信息");
            }

            Long staffId = Long.parseLong(staffInfo.get("staff_id").toString());

            // 检查是否有权限删除（是否是创建者）
            String checkPermissionSql = "SELECT COUNT(*) AS count FROM biz_order_service " +
                    "WHERE id = " + serviceId + " AND staff_id = " + staffId;

            Map<String, Object> permissionCount = userAppMapper.selectOneBySql(checkPermissionSql);

            if (permissionCount == null || Integer.parseInt(permissionCount.get("count").toString()) == 0) {
                return AjaxResult.success(false); // 没有权限删除
            }

            return AjaxResult.success(true); // 有权限删除
        } catch (Exception e) {
            return AjaxResult.error("检查删除权限失败: " + e.getMessage());
        }
    }

    /**
     * 批量检查服务是否可以删除
     */
    @PostMapping("/batchCheckServiceCanDelete")
    public AjaxResult batchCheckServiceCanDelete(@RequestBody List<Long> serviceIds) {
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();

            // 查询店员信息
            String staffSql = "SELECT staff_id FROM biz_staff WHERE user_id = " + userId;
            Map<String, Object> staffInfo = userAppMapper.selectOneBySql(staffSql);

            if (staffInfo == null || !staffInfo.containsKey("staff_id")) {
                return AjaxResult.error("未找到店员信息");
            }

            Long staffId = Long.parseLong(staffInfo.get("staff_id").toString());

            // 构建结果Map
            Map<Long, Boolean> permissionMap = new HashMap<>();

            // 检查每个服务的权限
            for (Long serviceId : serviceIds) {
                String checkPermissionSql = "SELECT COUNT(*) AS count FROM biz_order_service " +
                        "WHERE id = " + serviceId + " AND staff_id = " + staffId;

                Map<String, Object> permissionCount = userAppMapper.selectOneBySql(checkPermissionSql);

                boolean canDelete = permissionCount != null &&
                        Integer.parseInt(permissionCount.get("count").toString()) > 0;

                permissionMap.put(serviceId, canDelete);
            }

            return AjaxResult.success(permissionMap);
        } catch (Exception e) {
            return AjaxResult.error("批量检查删除权限失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单总结内容
     */
    @GetMapping("/getOrderSummary")
    public AjaxResult getOrderSummary(@RequestParam String orderNo) {
        try {
            // 参数校验
            if (orderNo == null || orderNo.isEmpty()) {
                return AjaxResult.error("订单号不能为空");
            }

            // 获取订单结束总结阶段ID
            String stageSql = "SELECT stage_id FROM biz_order_stage WHERE stage_name LIKE '%订单结束总结%' LIMIT 1";
            Map<String, Object> stageInfo = userAppMapper.selectOneBySql(stageSql);

            if (stageInfo == null || !stageInfo.containsKey("stage_id")) {
                return AjaxResult.error("未找到订单结束总结阶段");
            }

            Long endStageId = Long.parseLong(stageInfo.get("stage_id").toString());

            // 查询订单总结内容
//            String summarySql = "SELECT o.male_summary, o.female_summary " +
//                    "FROM biz_order o " +
//                    "WHERE o.order_no = '" + orderNo + "' " +
//                    "AND o.current_stage_id = " + endStageId;

            String summarySql = "SELECT \n" +
                    "    o.order_id,\n" +
                    "    o.summary,\n" +
                    "    o.order_no,\n" +
                    "    o.order_amount,\n" +
                    "    o.order_time,\n" +
                    "    o.status,\n" +
                    "    u.user_id,\n" +
                    "    u.gender,\n" +
                    "    u.user_name,\n" +
                    "    u.user_code,\n" +
                    "    u.nickname,\n" +
                    "    u.phone\n" +
                    "FROM \n" +
                    "    biz_order o\n" +
                    "JOIN \n" +
                    "    biz_app_user u ON o.user_id = u.user_id "+
                    "WHERE o.order_no = '" + orderNo + "' " +
                    "AND o.current_stage_id = " + endStageId;


            List<Map<String, Object>> summaryInfo = userAppMapper.selectListBySql(summarySql);

            // 如果没有找到记录，返回空内容
            if (summaryInfo == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("maleSummary", "");
                emptyResult.put("femaleSummary", "");
                return AjaxResult.success(emptyResult);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            for (Map<String, Object> summary : summaryInfo) {
                if(summary.get("gender").equals("0")){
                    if(summary.containsKey("summary"))
                    {
                        result.put("maleSummary", summary.get("summary").toString());
                    }else
                    {
                        result.put("maleSummary", "");
                    }

                }
                if(summary.get("gender").equals("1")){
                    if(summary.containsKey("summary"))
                    {
                        result.put("femaleSummary", summary.get("summary").toString());
                    }else
                    {
                        result.put("femaleSummary", "");
                    }
                }
            }
            //   result.put("maleSummary", summaryInfo.get("male_summary") != null ? summaryInfo.get("male_summary") : "");
            // result.put("femaleSummary", summaryInfo.get("female_summary") != null ? summaryInfo.get("female_summary") : "");

            return AjaxResult.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("获取订单总结失败: " + e.getMessage());
        }
    }

    /**
     * 保存订单总结内容
     */
    @PostMapping("/saveOrderSummary")
    public AjaxResult saveOrderSummary(@RequestBody Map<String, Object> data) {
        try {
            // 参数校验
            if (!data.containsKey("orderNo") || data.get("orderNo") == null) {
                return AjaxResult.error("订单号不能为空");
            }

            String orderNo = data.get("orderNo").toString();
            String summary = data.get("summary").toString();
//            String maleSummary = data.containsKey("maleSummary") ? data.get("maleSummary").toString() : "";
//            String femaleSummary = data.containsKey("femaleSummary") ? data.get("femaleSummary").toString() : "";

            // 获取订单结束总结阶段ID
            String stageSql = "SELECT stage_id FROM biz_order_stage WHERE stage_name LIKE '%订单结束总结%' LIMIT 1";
            Map<String, Object> stageInfo = userAppMapper.selectOneBySql(stageSql);

            if (stageInfo == null || !stageInfo.containsKey("stage_id")) {
                return AjaxResult.error("未找到订单结束总结阶段");
            }

            Long endStageId = Long.parseLong(stageInfo.get("stage_id").toString());

            // 保存订单总结内容
            String updateSummarySql = "UPDATE biz_order SET " +
                    "summary = '" + summary.replace("'", "''") + "' " +
//                    "female_summary = '" + femaleSummary.replace("'", "''") + "' " +
                    "WHERE order_no = '" + orderNo + "' AND  staff_id = '" +  data.get("staffid").toString() + "' " +
                    "AND current_stage_id = " + endStageId;

            int rows = userAppMapper.updateBySql(updateSummarySql);

            if (rows > 0) {
                return AjaxResult.success("订单总结保存成功");
            } else {
                return AjaxResult.error("未找到相关订单或订单阶段不匹配");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("保存订单总结失败: " + e.getMessage());
        }
    }

    /**
     * 锁定订单，标记为已完成
     */
    @PostMapping("/lockOrderFinish")
    public AjaxResult lockOrderFinish(@RequestBody Map<String, Object> data) {
        try {
            // 参数校验
            if (!data.containsKey("orderNo") || data.get("orderNo") == null) {
                return AjaxResult.error("订单号不能为空");
            }

            String orderNo = data.get("orderNo").toString();
            String maleSummary = data.containsKey("maleSummary") ? data.get("maleSummary").toString() : "";
            String femaleSummary = data.containsKey("femaleSummary") ? data.get("femaleSummary").toString() : "";
            Long staffId = data.containsKey("staffId") ? Long.parseLong(data.get("staffId").toString()) : null;
            Long endStageId = data.containsKey("endStageId") ? Long.parseLong(data.get("endStageId").toString()) : null;

            if (staffId == null) {
                return AjaxResult.error("店员ID不能为空");
            }

            if (endStageId == null) {
                // 获取订单结束总结阶段ID
                String stageSql = "SELECT stage_id FROM biz_order_stage WHERE stage_name LIKE '%订单结束总结%' LIMIT 1";
                Map<String, Object> stageInfo = userAppMapper.selectOneBySql(stageSql);

                if (stageInfo == null || !stageInfo.containsKey("stage_id")) {
                    return AjaxResult.error("未找到订单结束总结阶段");
                }

                endStageId = Long.parseLong(stageInfo.get("stage_id").toString());
            }

            // 保存订单总结内容
//            String updateSummarySql = "UPDATE biz_order SET " +
//                    "male_summary = '" + maleSummary.replace("'", "''") + "', " +
//                    "female_summary = '" + femaleSummary.replace("'", "''") + "' " +
//                    "WHERE order_no = '" + orderNo + "' " +
//                    "AND current_stage_id = " + endStageId;
//
//            userAppMapper.updateBySql(updateSummarySql);

            // 更新订单状态为已完成
            String updateStatusSql = "UPDATE biz_order SET " +
                    "status = '1' ,update_time= NOW() " +
                    "WHERE order_no = '" + orderNo + "'";

            userAppMapper.updateBySql(updateStatusSql);

            // 查询更新后的订单信息
//            String orderSql = "SELECT o.*, u.user_name, u.gender " +
//                    "FROM biz_order o " +
//                    "JOIN biz_app_user u ON o.user_id = u.user_id " +
//                    "WHERE o.order_no = '" + orderNo + "' " +
//                    "AND o.staff_id = " + staffId +" LIMIT 1";
//
//            Map<String, Object> orderInfo = userAppMapper.selectOneBySql(orderSql);

            return AjaxResult.success("订单已锁定并标记为已完成", null);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("锁定订单失败: " + e.getMessage());
        }
    }

    /**
     * 定时任务：自动为交往阶段创建后续服务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    //  @Scheduled(fixedRate = 5000)
    public void autoCreateDatingStageServices() {
        try {
            // 查询所有处于"交往阶段"的订单
//            String orderSql = "SELECT o.order_id, o.order_no, o.staff_id, o.shop_id, s.stage_name, o.current_stage_id " +
//                    "FROM biz_order o " +
//                    "JOIN biz_order_stage s ON o.current_stage_id = s.stage_id " +
//                    "WHERE s.stage_name = '交往阶段' AND o.status = '0'";

            String orderSql = "SELECT \n" +
                    "    MIN(o.order_id) AS order_id,\n" +
                    "    o.order_no,\n" +
                    "    MIN(o.staff_id) AS staff_id,\n" +
                    "    MIN(o.shop_id) AS shop_id,\n" +
                    "    s.stage_name,\n" +
                    "    MIN(o.current_stage_id) AS current_stage_id,\n" +
                    "    MIN(sv.service_id) AS service_id,\n" +
                    "    MIN(sv.service_name) AS service_name,\n" +
                    "    MIN(sv.price) AS price,\n" +
                    "    MIN(sv.service_time) AS service_time,\n" +
                    "    MIN(sv.remark) AS service_remark\n" +
                    "FROM \n" +
                    "    biz_order o\n" +
                    "JOIN \n" +
                    "    biz_order_stage s ON o.current_stage_id = s.stage_id\n" +
                    "JOIN \n" +
                    "    biz_order_service sv ON o.order_id = sv.order_id\n" +
                    "WHERE \n" +
                    "    s.stage_name = '交往阶段' \n" +
                    "    AND o.status = '0'\n" +
                    "GROUP BY \n" +
                    "    o.order_no, s.stage_name";


            List<Map<String, Object>> datingOrders = userAppMapper.selectListBySql(orderSql);

            for (Map<String, Object> order : datingOrders) {
                String orderNo = order.get("order_no").toString();
                Long orderId = Long.parseLong(order.get("order_id").toString());
                Long staffId = Long.parseLong(order.get("staff_id").toString());
                Long shopId = Long.parseLong(order.get("shop_id").toString());
                Long stageId = Long.parseLong(order.get("current_stage_id").toString());

                // 检查是否有下一阶段（如果有下一阶段，不再自动生成服务）
                String nextStageSql = "SELECT os.stage_id " +
                        "FROM biz_order o " +
                        "JOIN biz_order_stage os ON o.current_stage_id = os.stage_id " +
                        "WHERE o.order_no = '" + orderNo + "' " +
                        "AND os.order_num > (SELECT order_num FROM biz_order_stage WHERE stage_id = " + stageId + ") " +
                        "LIMIT 1";

                Map<String, Object> nextStage = userAppMapper.selectOneBySql(nextStageSql);
                if (nextStage != null) {
                    // 已经有下一阶段，跳过处理
                    continue;
                }

                // 查询该订单现有的服务数量
                String serviceCountSql = "SELECT COUNT(*) as count FROM biz_order_service " +
                        "WHERE order_id = " + orderId + " AND current_stage_id = " + stageId;

                Map<String, Object> serviceCount = userAppMapper.selectOneBySql(serviceCountSql);
                int count = Integer.parseInt(serviceCount.get("count").toString());

                // 如果已经有服务，检查最新的服务创建时间
                if (count > 0) {
                    String lastServiceSql = "SELECT service_time FROM biz_order_service " +
                            "WHERE order_id = " + orderId + " AND current_stage_id = " + stageId + " " +
                            "ORDER BY service_time DESC LIMIT 1";

                    Map<String, Object> lastService = userAppMapper.selectOneBySql(lastServiceSql);

                    if (lastService != null && lastService.containsKey("service_time")) {
                        // 解析上次服务时间
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime dt = LocalDateTime.parse(lastService.get("service_time").toString());
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        String formattedStr = dt.format(formatter);
                        Date lastServiceTime = sdf.parse(formattedStr);
                        Date now = new Date();

                        // 计算时间差
                        long diffInMillies = now.getTime() - lastServiceTime.getTime();
                        long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);

                        // 第一个服务后29天创建第二个服务，之后每28天创建一个服务
                        int daysToWait = (count == 1) ? 29 : 28;

                        if (diffInDays < daysToWait) {
                            // 还未到达创建新服务的时间
                            continue;
                        }
                    }
                } else {
                    // 没有服务，跳过（第一个服务应该在创建阶段时已经创建）
                    continue;
                }

                // 获取店铺服务项目（按照service_code排序）
                String serviceSql = "SELECT service_id, service_name, service_code, price " +
                        "FROM biz_service_item WHERE is_system = '0' " +
                        "AND shop_id = '" + shopId + "' " +
                        "ORDER BY service_code";

                List<Map<String, Object>> shopServices = userAppMapper.selectListBySql(serviceSql);

                // 如果没有店铺服务，则获取系统服务
                List<Map<String, Object>> services;
                if (shopServices == null || shopServices.isEmpty()) {
                    serviceSql = "SELECT service_id, service_name, service_code, price " +
                            "FROM biz_service_item WHERE is_system = '1' " +
                            "ORDER BY service_code";
                    services = userAppMapper.selectListBySql(serviceSql);
                } else {
                    services = shopServices;
                }

                // 如果没有服务项目，跳过
                if (services == null || services.isEmpty()) {
                    continue;
                }

                // 确定使用哪个服务项目
                // 第1个已经在创建阶段时使用，第2次收费使用排第二个，第3次使用排第三个，第4次使用排第一个，依此循环
                int serviceIndex = count % services.size();
                if (serviceIndex == 0 && count > 0) {
                    serviceIndex = services.size() - 1;
                } else if (count > 0) {
                    serviceIndex = serviceIndex - 1;
                }

                // 创建新服务
                Map<String, Object> serviceToCreate = services.get(serviceIndex);
                Long serviceId = Long.parseLong(serviceToCreate.get("service_id").toString());
                String serviceName = serviceToCreate.get("service_name").toString();
                Double price = Double.parseDouble(serviceToCreate.get("price").toString());

                // 插入新服务
                String insertSql = "INSERT INTO biz_order_service (order_id, order_no, service_id, service_name, price, staff_id, service_time, create_time, current_stage_id) " +
                        "VALUES (" + orderId + ", '" + orderNo + "', " + serviceId + ", '" + serviceName + "', " + price + ", " + staffId + ", NOW(), NOW(), " + stageId + ")";

                userAppMapper.insertBySql(insertSql);
            }
        } catch (Exception e) {
            // 记录错误日志，但不抛出异常
            e.printStackTrace();
        }
    }

    /**
     * 更新订单删除同意状态
     */
    @PostMapping("/updateDeleteAgreement")
    public AjaxResult updateDeleteAgreement(@RequestBody Map<String, Object> data) {
        String orderNo = data.get("orderNo").toString();
        String staffId = data.get("staffId").toString();
        String gender = data.get("gender").toString();
        String isAgree = data.get("isAgree").toString();

        try {
            // 根据性别确定更新哪个订单
            String orderType = "male".equals(gender) ? "male" : "female";

            // 构建SQL更新语句
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE biz_order SET isdelete = '").append(isAgree).append("' ");
            sql.append("WHERE order_no = '").append(orderNo).append("' ");
            sql.append("AND staff_id = '").append(staffId).append("' ");
//            sql.append("AND order_type = '").append(orderType).append("'");

            // 执行更新操作
            int result = userAppMapper.updateBySql(sql.toString());

            if (result > 0) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败，未找到匹配的订单或您无权更新");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 冻结订单
     */
    @PostMapping("/freezeOrder")
    public AjaxResult freezeOrder(@RequestBody Map<String, Object> data) {
        String orderNo = data.get("orderNo").toString();
        String staffId = data.get("staffId").toString();

        try {
            // 首先检查男女双方是否都已同意删除
            String checkSql = "SELECT COUNT(*) as count FROM biz_order " +
                    "WHERE order_no = '" + orderNo + "' " +
                    "AND isdelete != '1'";

            Map<String, Object> checkResult = userAppMapper.selectOneBySql(checkSql);
            int count = Integer.parseInt(checkResult.get("count").toString());

            if (count > 0) {
                return AjaxResult.error("男女双方必须都同意删除，才能冻结订单");
            }

            // 更新订单状态为已冻结(2)
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE biz_order SET status = '2' ");
            sql.append("WHERE order_no = '").append(orderNo).append("'");

            // 执行更新操作
            int result = userAppMapper.updateBySql(sql.toString());

            if (result > 0) {
                return AjaxResult.success("订单已冻结");
            } else {
                return AjaxResult.error("冻结失败，未找到匹配的订单");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("冻结失败: " + e.getMessage());
        }
    }
}
