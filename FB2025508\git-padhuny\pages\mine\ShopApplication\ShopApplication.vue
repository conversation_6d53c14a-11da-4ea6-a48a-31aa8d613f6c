<template>
	<view class="container">
		<!-- 头部标题栏 -->
		<view class="header">
			<view class="back-icon" @click="goBack">
				<text class="iconfont icon-left"></text>
			</view>
			<view class="title">加盟申请</view>
		</view>
		
		<!-- 申请表单 -->
		<view class="form-container">
			<form @submit.prevent="submitForm">
				<!-- 门店名称 -->
				<view class="form-item">
					<view class="label">
						<text>门店名称</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						v-model="formData.shopName" 
						placeholder="请输入门店名称" 
						:disabled="formSubmitted && !isEditing"
						class="input-field"
					/>
				</view>
				
				<!-- 所属城市 -->
				<view class="form-item">
					<view class="label">
						<text>所属城市</text>
						<text class="required">*</text>
					</view>
					<view class="picker-view" @click="showRegionPicker">
						<text v-if="formData.city" class="picker-text">{{ formData.city }}</text>
						<text v-else class="picker-placeholder">请选择所属城市</text>
						<text class="picker-arrow">▼</text>
					</view>
				</view>
				
				<!-- 联系人姓名 -->
				<view class="form-item">
					<view class="label">
						<text>联系人姓名</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						v-model="formData.contactName" 
						placeholder="请输入联系人姓名" 
						:disabled="formSubmitted && !isEditing"
						class="input-field"
					/>
				</view>
				
				<!-- 联系电话 -->
				<view class="form-item">
					<view class="label">
						<text>联系电话</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						v-model="formData.contactPhone" 
						placeholder="请输入联系电话" 
						:disabled="formSubmitted && !isEditing"
						class="input-field"
					/>
				</view>
				
				<!-- 门店简介 -->
				<view class="form-item description-item">
					<view class="label">
						<text>门店简介</text>
						<text class="required">*</text>
						<text class="limit">(限100字)</text>
					</view>
					<textarea 
						v-model="formData.shopDesc" 
						placeholder="请介绍门店成立时间、现有员工、现有业务等" 
						:disabled="formSubmitted && !isEditing"
						maxlength="100"
						class="textarea-field"
					></textarea>
					<view class="char-count">{{formData.shopDesc.length}}/100</view>
				</view>
				
				<!-- 日期时间 -->
				<view class="form-item">
					<view class="label">
						<text>日期时间</text>
						<text class="required">*</text>
					</view>
					<input 
						type="text" 
						:value="formData.applyTime" 
						disabled 
						class="datetime input-field"
					/>
				</view>
				
				<!-- 提交按钮区域 -->
				<view class="button-group">
					<button
						type="default"
						class="reset-btn"
						@click="resetForm"
					>重置</button>
					<button
						type="default"
						class="modify-btn"
						@click="toggleEdit"
						v-if="formSubmitted"
					>{{ isEditing ? '取消修改' : '修改' }}</button>
					<button
						type="primary"
						class="submit-btn"
						@click="submitForm"
						:disabled="formSubmitted && !isEditing"
					>{{ isEditing && formSubmitted ? '保存修改' : '提交' }}</button>
				</view>
			</form>
		</view>
		
		<!-- 三级区域选择器弹窗 -->
		<view class="popup-mask" v-if="showRegionPickerDialog" @click="closeRegionPicker"></view>
		<view class="popup-content" v-if="showRegionPickerDialog">
			<view class="picker-container">
				<view class="picker-header">
					<text class="cancel" @click="closeRegionPicker">取消</text>
					<text class="confirm" @click="confirmRegion">确定</text>
				</view>
				<picker-view :indicator-style="indicatorStyle" style="height: 240px;" @change="onRegionChange">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in provinceList" :key="index">
							{{ item.regioninfo_name }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in cityList" :key="index">
							{{ item.regioninfo_name }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in districtList" :key="index">
							{{ item.regioninfo_name }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
	</view>
</template>

<script>
import { submitFranchiseApplication, updateFranchiseApplication, getRegionInfoList } from '@/api/work/retrun_part.js'

export default {
	data() {
		return {
			formData: {
				shopName: '',
				city: '',
				cityId: null, 
				contactName: '',
				contactPhone: '',
				shopDesc: '',
				applyTime: this.formatDateTime(new Date()),
				applicationId: null,
				// 新增区域ID字段
				provinceId: null,
				provinceName: '',
				cityId: null,
				cityName: '',
				districtId: null,
				districtName: ''
			},
			formSubmitted: false,  // 是否已提交
			isEditing: false,      // 是否处于编辑状态
			originalFormData: null, // 保存原始表单数据用于比较
			
			// 区域选择相关
			allRegionData: {
				provinces: [],
				cities: [],
				districts: []
			},
			provinceList: [],
			cityList: [],
			districtList: [],
			selectedIndices: [0, 0, 0],  // [省索引, 市索引, 区索引]
			selectedRegion: {
				province: null,
				city: null,
				district: null
			},
			
			indicatorStyle: 'height: 40px;',
			showRegionPickerDialog: false
		}
	},
	onLoad() {
		// 页面加载时生成当前日期时间
		this.formData.applyTime = this.formatDateTime(new Date());
		// 获取区域列表
		this.loadRegionData();
		// 加载本地存储的表单数据
		this.loadFormDataFromStorage();
	},
	methods: {
		// 加载区域数据
		loadRegionData() {
			uni.showLoading({
				title: '加载中...'
			});
			
			getRegionInfoList().then(res => {
				uni.hideLoading();
				if (res.code === 200 && res.data) {
					this.allRegionData = res.data;
					
					// 初始化省列表
					this.provinceList = this.allRegionData.provinces || [];
					
					// 初始化市列表
					this.updateCityList(0);
					
					// 初始化区县列表
					this.updateDistrictList(0);
				} else {
					uni.showToast({
						title: '获取区域列表失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				uni.hideLoading();
				console.error('获取区域列表失败:', err);
				uni.showToast({
					title: '网络错误，请稍后再试',
					icon: 'none'
				});
			});
		},
		
		// 根据选中的省更新市列表
		updateCityList(provinceIndex) {
			if (!this.provinceList || this.provinceList.length === 0) {
				this.cityList = [];
				this.districtList = [];
				return;
			}
			
			const selectedProvince = this.provinceList[provinceIndex];
			if (!selectedProvince) {
				this.cityList = [];
				this.districtList = [];
				return;
			}
			
			// 根据省的ID筛选获取其下的市列表
			this.cityList = (this.allRegionData.cities || []).filter(city => 
				city.parentinfo_id === selectedProvince.regioninfo_id
			);
			
			// 更新区县列表
			this.updateDistrictList(0);
		},
		
		// 根据选中的市更新区县列表
		updateDistrictList(cityIndex) {
			if (!this.cityList || this.cityList.length === 0) {
				this.districtList = [];
				return;
			}
			
			const selectedCity = this.cityList[cityIndex];
			if (!selectedCity) {
				this.districtList = [];
				return;
			}
			
			// 根据市的ID筛选获取其下的区县列表
			this.districtList = (this.allRegionData.districts || []).filter(district => 
				district.parentinfo_id === selectedCity.regioninfo_id
			);
		},
		
		// 区域选择器相关方法
		showRegionPicker() {
			// 如果表单已提交且不在编辑状态，则不允许修改
			if (this.formSubmitted && !this.isEditing) {
				return;
			}
			this.showRegionPickerDialog = true;
		},
		
		closeRegionPicker() {
			this.showRegionPickerDialog = false;
		},
		
		onRegionChange(e) {
			const values = e.detail.value;
			const [provinceIndex, cityIndex, districtIndex] = values;
			
			// 如果省份变更，更新市和区县
			if (provinceIndex !== this.selectedIndices[0]) {
				this.updateCityList(provinceIndex);
				values[1] = 0; // 重置市索引
				values[2] = 0; // 重置区县索引
			} 
			// 如果市变更，更新区县
			else if (cityIndex !== this.selectedIndices[1]) {
				this.updateDistrictList(cityIndex);
				values[2] = 0; // 重置区县索引
			}
			
			this.selectedIndices = values;
		},
		
		confirmRegion() {
			const [provinceIndex, cityIndex, districtIndex] = this.selectedIndices;
			
			const selectedProvince = this.provinceList[provinceIndex];
			const selectedCity = this.cityList[cityIndex];
			const selectedDistrict = this.districtList[districtIndex];
			
			if (!selectedProvince || !selectedCity || !selectedDistrict) {
				uni.showToast({
					title: '请选择完整的省市区',
					icon: 'none'
				});
				return;
			}
			
			// 更新选中的区域
			this.selectedRegion = {
				province: selectedProvince,
				city: selectedCity,
				district: selectedDistrict
			};
			
			// 更新formData中的区域数据
			this.formData.provinceId = selectedProvince.regioninfo_id;
			this.formData.provinceName = selectedProvince.regioninfo_name;
			this.formData.cityId = selectedCity.regioninfo_id;
			this.formData.cityName = selectedCity.regioninfo_name;
			this.formData.districtId = selectedDistrict.regioninfo_id;
			this.formData.districtName = selectedDistrict.regioninfo_name;
			
			// 设置显示的完整地址和最终提交的区域ID(district的ID)
			this.formData.city = `${selectedProvince.regioninfo_name}${selectedCity.regioninfo_name}${selectedDistrict.regioninfo_name}`;
			this.formData.cityId = selectedDistrict.regioninfo_id;
			
			this.showRegionPickerDialog = false;
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 提交表单
		submitForm() {
			// 表单验证
			if (!this.validateForm()) {
				return;
			}

			// 格式化日期时间
			this.formData.applyTime = this.formatDateTime(new Date());

			// 根据是否有ID决定是新增还是更新
			const submitMethod = this.formData.applicationId
				? updateFranchiseApplication
				: submitFranchiseApplication;

			// 发送请求
			submitMethod(this.formData).then(res => {
				if (res.code === 200) {
					// 如果是首次提交
					if (!this.formData.applicationId && res.data) {
						this.formData.applicationId = res.data;
					}

					uni.showToast({
						title: this.isEditing ? '修改成功' : '提交成功',
						icon: 'success'
					});

					// 提交后更新状态，禁用表单
					this.formSubmitted = true;
					this.isEditing = false;

					// 保存原始数据
					this.originalFormData = JSON.parse(JSON.stringify(this.formData));

					// 保存表单数据到本地存储
					this.saveFormDataToStorage();
				} else {
					uni.showToast({
						title: res.msg || '提交失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				console.error('提交失败:', err);
				uni.showToast({
					title: '网络错误，请稍后再试',
					icon: 'none'
				});
			});
		},
		
		// 表单验证
		validateForm() {
			if (!this.formData.shopName.trim()) {
				uni.showToast({
					title: '请输入门店名称',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.city.trim()) {
				uni.showToast({
					title: '请选择所属区域',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.contactName.trim()) {
				uni.showToast({
					title: '请输入联系人姓名',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.contactPhone.trim()) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return false;
			}
			
			// 验证手机号格式
			const phoneReg = /^1[3-9]\d{9}$/;
			if (!phoneReg.test(this.formData.contactPhone)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.shopDesc.trim()) {
				uni.showToast({
					title: '请输入门店简介',
					icon: 'none'
				});
				return false;
			}
			
			return true;
		},
		
		// 重置表单
		resetForm() {
			uni.showModal({
				title: '提示',
				content: '确定要重置表单吗？这将清空所有已填写的内容。',
				success: res => {
					if (res.confirm) {
						// 重置表单数据
						this.formData = {
							shopName: '',
							city: '',
							cityId: null,
							contactName: '',
							contactPhone: '',
							shopDesc: '',
							applyTime: this.formatDateTime(new Date()),
							applicationId: null,
							// 新增区域ID字段
							provinceId: null,
							provinceName: '',
							cityId: null,
							cityName: '',
							districtId: null,
							districtName: ''
						};
						this.selectedIndices = [0, 0, 0];
						this.selectedRegion = {
							province: null,
							city: null,
							district: null
						};

						// 重置状态，允许编辑
						this.formSubmitted = false;
						this.isEditing = true;
						this.originalFormData = null;

						// 清除本地存储
						this.clearFormDataFromStorage();

						uni.showToast({
							title: '表单已重置',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 切换编辑状态
		toggleEdit() {
			if (!this.isEditing) {
				// 进入编辑状态
				this.isEditing = true;
				uni.showToast({
					title: '现在可以修改表单内容',
					icon: 'none'
				});
			} else {
				// 退出编辑状态，恢复原始数据
				uni.showModal({
					title: '提示',
					content: '确定要取消修改吗？未保存的修改将丢失。',
					success: res => {
						if (res.confirm) {
							this.isEditing = false;
							if (this.originalFormData) {
								// 恢复原始数据
								this.formData = JSON.parse(JSON.stringify(this.originalFormData));

								// 恢复区域选择状态
								this.restoreRegionSelection();
							}

							uni.showToast({
								title: '已取消修改',
								icon: 'none'
							});
						}
					}
				});
			}
		},
		
		// 格式化日期时间
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');

			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},

		// 保存表单数据到本地存储
		saveFormDataToStorage() {
			try {
				const storageData = {
					formData: this.formData,
					formSubmitted: this.formSubmitted,
					selectedIndices: this.selectedIndices,
					selectedRegion: this.selectedRegion,
					timestamp: new Date().getTime()
				};
				uni.setStorageSync('shopApplicationForm', JSON.stringify(storageData));
			} catch (error) {
				console.error('保存表单数据失败:', error);
			}
		},

		// 从本地存储加载表单数据
		loadFormDataFromStorage() {
			try {
				const storageData = uni.getStorageSync('shopApplicationForm');
				if (storageData) {
					const data = JSON.parse(storageData);

					// 检查数据是否过期（7天）
					const now = new Date().getTime();
					const daysDiff = (now - data.timestamp) / (1000 * 60 * 60 * 24);

					if (daysDiff <= 7) {
						// 恢复表单数据
						this.formData = { ...this.formData, ...data.formData };
						this.formSubmitted = data.formSubmitted || false;
						this.selectedIndices = data.selectedIndices || [0, 0, 0];
						this.selectedRegion = data.selectedRegion || { province: null, city: null, district: null };

						// 如果表单已提交，保存原始数据
						if (this.formSubmitted) {
							this.originalFormData = JSON.parse(JSON.stringify(this.formData));
							this.isEditing = false;
						} else {
							this.isEditing = true;
						}

						// 延迟恢复区域选择状态，等待区域数据加载完成
						setTimeout(() => {
							this.restoreRegionSelection();
						}, 1000);
					} else {
						// 数据过期，清除存储
						this.clearFormDataFromStorage();
					}
				}
			} catch (error) {
				console.error('加载表单数据失败:', error);
			}
		},

		// 清除本地存储的表单数据
		clearFormDataFromStorage() {
			try {
				uni.removeStorageSync('shopApplicationForm');
			} catch (error) {
				console.error('清除表单数据失败:', error);
			}
		},

		// 恢复区域选择状态
		restoreRegionSelection() {
			if (this.formData.provinceId && this.provinceList.length > 0) {
				// 查找省份索引
				const provinceIndex = this.provinceList.findIndex(
					p => p.regioninfo_id === this.formData.provinceId
				);

				if (provinceIndex !== -1) {
					this.selectedIndices[0] = provinceIndex;
					this.updateCityList(provinceIndex);

					// 延迟处理市和区的选择
					setTimeout(() => {
						// 查找城市索引
						const cityIndex = this.cityList.findIndex(
							c => c.regioninfo_id === this.formData.cityId
						);

						if (cityIndex !== -1) {
							this.selectedIndices[1] = cityIndex;
							this.updateDistrictList(cityIndex);

							// 再次延迟处理区的选择
							setTimeout(() => {
								// 查找区县索引
								const districtIndex = this.districtList.findIndex(
									d => d.regioninfo_id === this.formData.districtId
								);

								if (districtIndex !== -1) {
									this.selectedIndices[2] = districtIndex;

									// 更新选中的区域对象
									this.selectedRegion = {
										province: this.provinceList[provinceIndex],
										city: this.cityList[cityIndex],
										district: this.districtList[districtIndex]
									};
								}
							}, 100);
						}
					}, 100);
				}
			}
		},

		// 页面显示时保存当前状态
		onShow() {
			// 如果表单已提交，保存当前状态到本地存储
			if (this.formSubmitted) {
				this.saveFormDataToStorage();
			}
		},

		// 页面隐藏时保存当前状态
		onHide() {
			// 保存当前编辑状态
			if (this.formData.shopName || this.formData.contactName || this.formData.contactPhone || this.formData.shopDesc) {
				this.saveFormDataToStorage();
			}
		}
	}
}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 90rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #eeeeee;
}

.back-icon {
	position: absolute;
	left: 30rpx;
	font-size: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: 500;
}

.form-container {
	padding: 20rpx;
	margin-bottom: 40rpx;
}

.form-item {
	position: relative;
	margin-bottom: 30rpx; /* 增加表单项之间的间距 */
	background-color: #ffffff;
	border-radius: 8rpx;
	padding: 20rpx; /* 增加内边距 */
}

.description-item {
	min-height: 260rpx; /* 为门店简介增加更多高度 */
}

.label {
	font-size: 30rpx; /* 增加标签文字大小 */
	color: #333;
	margin-bottom: 15rpx; /* 增加标签与输入框的间距 */
	font-weight: 500;
}

.required {
	color: #ff0000;
	margin-left: 5rpx;
}

.limit {
	font-size: 24rpx;
	color: #999;
	margin-left: 10rpx;
}

/* 输入框样式优化 */
.input-field {
	width: 100%;
	font-size: 30rpx;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #eee;
	height: 70rpx; /* 增加输入框高度 */
	line-height: 1.5;
}

.textarea-field {
	width: 100%;
	font-size: 30rpx;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #eee;
	height: 180rpx; /* 增加文本区域高度 */
	line-height: 1.5;
}

/* picker组件样式 */
.picker-view {
	width: 100%;
	height: 70rpx; /* 与输入框高度一致 */
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #eee;
	padding: 15rpx 0;
}

.picker-text {
	font-size: 30rpx;
	color: #333;
}

.picker-placeholder {
	font-size: 30rpx;
	color: #999;
}

.picker-arrow {
	font-size: 24rpx;
	color: #999;
}

.datetime {
	color: #ff0000;
}

.char-count {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	font-size: 24rpx;
	color: #999;
}

.button-group {
	display: flex;
	justify-content: space-between;
	padding: 30rpx 0 60rpx; /* 增加底部按钮区域的下边距 */
}

button {
	flex: 1;
	margin: 0 10rpx;
	height: 90rpx; /* 增加按钮高度 */
	line-height: 90rpx;
	font-size: 32rpx; /* 增加按钮文字大小 */
	border-radius: 8rpx;
}

.reset-btn {
	background-color: #f2f2f2;
	color: #666;
}

.modify-btn {
	background-color: #e6f7ff;
	color: #1890ff;
}

.submit-btn {
	background-color: #1890ff;
	color: #ffffff;
}

.submit-btn[disabled] {
	background-color: #cccccc;
	color: #ffffff;
	opacity: 0.7;
}

/* 选择器样式 */
.picker-container {
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
}

.picker-header {
	display: flex;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #eeeeee;
}

.cancel {
	color: #999999;
	font-size: 32rpx;
}

.confirm {
	color: #1890ff;
	font-size: 32rpx;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 32rpx;
}

.popup-mask {
	position: fixed;
	z-index: 998;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.5);
}

.popup-content {
	position: fixed;
	z-index: 999;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}
</style>
