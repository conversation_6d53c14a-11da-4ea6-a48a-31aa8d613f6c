<template>
	<view class="page-container">
		<!-- 头部用户信息 -->
		<view class="user-info">
			<view class="avatar-container">
				<view class="avatar">
					<image src="/static/images/avatar.png" mode="aspectFill"></image>
				</view>
			</view>
			<view class="info-container">
				<view class="info-item">
					<text class="info-label">姓名</text>
					<text class="info-value">{{staffInfo.name}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">手机号</text>
					<text class="info-value">{{staffInfo.phone}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">密码</text>
					<text class="info-value">{{staffInfo.password || '******'}}</text>
					<view class="edit-btn" @click="showEditPasswordDialog">修改密码</view>
				</view>
			</view>
		</view>
		
		<!-- 统计数据 -->
		<view class="stats-section">
			<view class="section-title">今日/本周/本月统计</view>
			<view class="stats-row">
				<view class="stats-card">
					<view class="stats-title">今日订单</view>
					<view class="stats-value">{{statistics.todayOrderCount || 0}}</view>
				</view>
				<view class="stats-card">
					<view class="stats-title">今日收入</view>
					<view class="stats-value">¥{{statistics.todayIncome || 0}}</view>
				</view>
			</view>
			<view class="stats-row">
				<view class="stats-card">
					<view class="stats-title">本周订单</view>
					<view class="stats-value">{{statistics.weekOrderCount || 0}}</view>
				</view>
				<view class="stats-card">
					<view class="stats-title">本周收入</view>
					<view class="stats-value">¥{{statistics.weekIncome || 0}}</view>
				</view>
			</view>
			<view class="stats-row">
				<view class="stats-card">
					<view class="stats-title">本月订单</view>
					<view class="stats-value">{{statistics.monthOrderCount || 0}}</view>
				</view>
				<view class="stats-card">
					<view class="stats-title">本月收入</view>
					<view class="stats-value">¥{{statistics.monthIncome || 0}}</view>
				</view>
			</view>
		</view>
		
		<view class="stats-section">
			<view class="section-title">年度统计</view>
			<view class="stats-row">
				<view class="stats-card">
					<view class="stats-title">本年订单</view>
					<view class="stats-value">{{statistics.yearOrderCount || 0}}</view>
				</view>
				<view class="stats-card">
					<view class="stats-title">本年收入</view>
					<view class="stats-value">¥{{statistics.yearIncome || 0}}</view>
				</view>
			</view>
			
			<!-- 历年数据(折叠) -->
			<view class="history-section" v-if="historicalStats && historicalStats.length > 0">
				<view class="history-toggle" @click="toggleHistoricalData">
					<text>历年数据</text>
					<text class="toggle-icon">{{showHistorical ? '▲' : '▼'}}</text>
				</view>
				<view class="history-content" v-if="showHistorical">
					<view class="history-item" v-for="(item, index) in historicalStats" :key="index">
						<view class="history-year">{{item.year}}年</view>
						<view class="history-data">
							<text>订单: {{item.orderCount}}</text>
							<text>收入: ¥{{item.income}}</text>
						</view>
					</view>
					<view class="more-history" v-if="historicalStats.length > 3" @click="showMoreHistory">
						显示更多
					</view>
				</view>
			</view>
		</view>
		
		<view class="stats-section">
			<view class="section-title">近6个月统计</view>
			<view class="stats-row">
				<view class="stats-card full-width">
					<view class="stats-title">订单量</view>
					<view class="stats-value">{{statistics.sixMonthOrderCount || 0}}单</view>
				</view>
			</view>
			<view class="stats-row">
				<view class="stats-card full-width">
					<view class="stats-title">服务顾客数</view>
					<view class="stats-value">{{statistics.sixMonthCustomerCount || 0}}位</view>
				</view>
			</view>
		</view>
		
		<!-- 修改密码弹窗 -->
		<uni-popup ref="passwordPopup" type="center">
			<view class="popup-container">
				<view class="popup-title">修改密码</view>
				<view class="form-item">
					<view class="form-label">新密码</view>
					<input type="password" v-model="newPassword.password" class="form-input" placeholder="请输入新密码"/>
				</view>
				<view class="form-item">
					<view class="form-label">确认密码</view>
					<input type="password" v-model="newPassword.confirmPassword" class="form-input" placeholder="请再次输入新密码"/>
				</view>
				<view class="popup-actions">
					<view class="cancel-btn" @click="hidePasswordPopup">取消</view>
					<view class="confirm-btn" @click="updatePassword">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { getStaffDetail, updateStaffPassword, getStaffStatistics } from '@/api/work/retrun_part.js';
	
	export default {
		data() {
			return {
				staffId: null,
				staffInfo: {
					id: '',
					name: '',
					phone: '',
					password: '',
					userId: ''
				},
				statistics: {
					todayOrderCount: 0,
					todayIncome: 0,
					weekOrderCount: 0,
					weekIncome: 0,
					monthOrderCount: 0,
					monthIncome: 0,
					yearOrderCount: 0,
					yearIncome: 0,
					sixMonthOrderCount: 0,
					sixMonthCustomerCount: 0
				},
				historicalStats: [],
				showHistorical: false,
				newPassword: {
					password: '',
					confirmPassword: ''
				},
				loading: false
			}
		},
		onLoad(options) {
			if (options.id) {
				console.log(options.id);
				this.staffId = options.id;
				this.fetchStaffDetail();
				this.fetchStaffStatistics();
			} else {
				uni.showToast({
					title: '员工信息不存在',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 获取员工详情
			async fetchStaffDetail() {
				uni.showLoading({
					title: '加载中'
				});
				
				try {
					const res = await getStaffDetail({ staffId: this.staffId });
					if (res.code === 200) {
						this.staffInfo = res.data;
					} else {
						uni.showToast({
							title: res.msg || '获取员工信息失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取员工信息失败', error);
					uni.showToast({
						title: '获取员工信息失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			
			// 获取员工统计数据
			async fetchStaffStatistics() {
				try {
					const res = await getStaffStatistics({ staffId: this.staffId });
					if (res.code === 200) {
						this.statistics = res.data.current;
						this.historicalStats = res.data.historical || [];
					} else {
						uni.showToast({
							title: res.msg || '获取统计数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取统计数据失败', error);
				}
			},
			
			// 显示/隐藏历史数据
			toggleHistoricalData() {
				this.showHistorical = !this.showHistorical;
			},
			
			// 显示更多历史数据
			showMoreHistory() {
				// 实现显示更多历史数据的逻辑
				// 可以跳转到新页面或加载更多数据
				uni.showToast({
					title: '加载更多历史数据',
					icon: 'none'
				});
			},
			
			// 显示修改密码弹窗
			showEditPasswordDialog() {
				this.newPassword = {
					password: '',
					confirmPassword: ''
				};
				this.$refs.passwordPopup.open();
			},
			
			// 隐藏修改密码弹窗
			hidePasswordPopup() {
				this.$refs.passwordPopup.close();
			},
			
			// 更新密码
			async updatePassword() {
				if (!this.newPassword.password) {
					uni.showToast({
						title: '请输入新密码',
						icon: 'none'
					});
					return;
				}
				
				if (this.newPassword.password.length < 6) {
					uni.showToast({
						title: '密码长度至少为6位',
						icon: 'none'
					});
					return;
				}
				
				if (this.newPassword.password !== this.newPassword.confirmPassword) {
					uni.showToast({
						title: '两次输入的密码不一致',
						icon: 'none'
					});
					return;
				}
				
				uni.showLoading({
					title: '更新中'
				});
				
				try {
					const res = await updateStaffPassword({
						staffId: this.staffId,
						userId: this.staffInfo.userId,
						password: this.newPassword.password
					});
					
					if (res.code === 200) {
						uni.showToast({
							title: '密码更新成功',
							icon: 'success'
						});
						this.hidePasswordPopup();
					} else {
						uni.showToast({
							title: res.msg || '更新密码失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('更新密码失败', error);
					uni.showToast({
						title: '更新密码失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.user-info {
		padding: 30rpx;
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.avatar-container {
		margin-right: 40rpx;
		
		.avatar {
			width: 120rpx;
			height: 120rpx;
			border-radius: 60rpx;
			overflow: hidden;
			background-color: #f0f0f0;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
	}
	
	.info-container {
		flex: 1;
		
		.info-item {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.info-label {
				font-size: 28rpx;
				color: #666;
				width: 120rpx;
			}
			
			.info-value {
				font-size: 30rpx;
				color: #333;
				flex: 1;
			}
			
			.edit-btn {
				padding: 8rpx 20rpx;
				background-color: #007AFF;
				color: #fff;
				border-radius: 30rpx;
				font-size: 24rpx;
			}
		}
	}
	
	.stats-section {
		padding: 30rpx;
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
			padding-bottom: 15rpx;
			border-bottom: 1px solid #f0f0f0;
		}
		
		.stats-row {
			display: flex;
			margin-bottom: 20rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.stats-card {
				flex: 1;
				background-color: #f8f8f8;
				border-radius: 8rpx;
				padding: 20rpx;
				margin: 0 10rpx;
				
				&:first-child {
					margin-left: 0;
				}
				
				&:last-child {
					margin-right: 0;
				}
				
				&.full-width {
					margin: 0;
				}
				
				.stats-title {
					font-size: 26rpx;
					color: #666;
					margin-bottom: 10rpx;
				}
				
				.stats-value {
					font-size: 36rpx;
					color: #333;
					font-weight: bold;
				}
			}
		}
	}
	
	.history-section {
		margin-top: 20rpx;
		
		.history-toggle {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 0;
			font-size: 28rpx;
			color: #666;
			
			.toggle-icon {
				color: #007AFF;
			}
		}
		
		.history-content {
			background-color: #f8f8f8;
			border-radius: 8rpx;
			padding: 20rpx;
			
			.history-item {
				display: flex;
				justify-content: space-between;
				padding: 15rpx 0;
				border-bottom: 1px solid #eee;
				
				&:last-child {
					border-bottom: none;
				}
				
				.history-year {
					font-size: 28rpx;
					color: #333;
					font-weight: bold;
				}
				
				.history-data {
					display: flex;
					
					text {
						font-size: 26rpx;
						color: #666;
						margin-left: 30rpx;
					}
				}
			}
			
			.more-history {
				text-align: center;
				padding: 20rpx 0 10rpx;
				font-size: 26rpx;
				color: #007AFF;
			}
		}
	}
	
	.popup-container {
		width: 600rpx;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
		}
		
		.form-item {
			margin-bottom: 20rpx;
			
			.form-label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.form-input {
				width: 100%;
				height: 80rpx;
				border: 1px solid #e0e0e0;
				border-radius: 4rpx;
				padding: 0 20rpx;
				font-size: 28rpx;
				box-sizing: border-box;
			}
		}
		
		.popup-actions {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
			
			.cancel-btn, .confirm-btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 6rpx;
				font-size: 30rpx;
			}
			
			.cancel-btn {
				background-color: #f0f0f0;
				color: #666;
				margin-right: 15rpx;
			}
			
			.confirm-btn {
				background-color: #007AFF;
				color: #fff;
				margin-left: 15rpx;
			}
		}
	}
</style> 