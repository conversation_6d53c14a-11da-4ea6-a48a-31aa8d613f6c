<template>
	<view class="user-list-container">
		<!-- 页面头部 -->
		<view class="header">
			<view class="city-select">
				<text>{{cityName}}</text>
				<text class="iconfont icon-arrow-down"></text>
			</view>
		</view>
		
		<!-- 用户统计信息 -->
		<view class="user-stats">
			<view class="stats-item">
				<text class="stats-label">用户总数：{{total}}人</text>
				<text class="stats-value">当前订单：{{currentOrderCount}}个</text>
				<text class="stats-value">通知：{{notificationCount}}个</text>
			</view>
		</view>
		
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-wrapper">
				<text class="label">热表人数</text>
				<input type="number" v-model="hotUserCount" class="number-input" />
			</view>
			<button class="search-btn" @click="searchUsers">复原</button>
		</view>
		
		<!-- 用户列表 -->
		<scroll-view class="user-scroll-view" scroll-y="true" refresher-enabled="true" 
			:refresher-triggered="isRefreshing" @refresherrefresh="onRefresh">
			<view class="user-list">
				<view class="user-card" v-for="(user, index) in userList" :key="index" @click="viewUserDetail(user)">
					<!-- 用户状态背景色 -->
					<view class="user-status-bg" :class="user.isflow ? 'flow-active' : 'flow-inactive'">
						<picker :range="userCodeOptions" range-key="user_code" :value="getUserCodeIndex(user.user_code)" @change="e => onUserCodeChange(e, user)">
							<view class="user-id">{{user.user_code || '请选择编号'}}</view>
						</picker>
						<text class="user-status-text">{{getFlowText(user.isflow)}}</text>
					</view>
					
					<!-- 用户基本信息 -->
					<view class="user-basic-info">
						<!-- 用户信息头部 -->
						<view class="user-info-header">
							<view class="user-main-info">
								<view class="user-name-row">
									<text class="user-name">{{user.user_name}}</text>
									<view class="user-gender-age">
										<text class="user-gender">{{user.gender === '0' ? '男' : '女'}}</text>
										<text class="user-age">{{calculateAge(user.birth_date)}}岁</text>
									</view>
								</view>
								<view class="user-detail-row">
									<text class="user-height">{{user.user_height}}cm</text>
									<text class="user-weight">{{user.user_weight}}kg</text>
									<text class="user-phone">{{user.phone}}</text>
								</view>
								<!-- 熟悉人数选择器 -->
								<view class="acquaintance-row">
									<text class="acquaintance-label">熟悉人数：</text>
									<picker class="acquaintance-picker" :value="getAcquaintanceIndex(user.acqNo)" :range="acquaintanceOptions" @change="e => updateAcquaintance(e, user)">
										<view class="acquaintance-value">{{user.acqNo || 0}}</view>
									</picker>
								</view>
							</view>
						</view>
						
						<!-- 订单信息和备注 -->
						<view class="user-order-section">
							<view class="order-info">
								<view class="order-counts">
									<text class="user-orders" @click="toggleCurrentOrder(user)">当前订单：{{getLocalCurrentOrder(user) || 0}}</text>
									<view class="history-order-input">
										<text>历史订单：</text>
										<input type="number" :value="getLocalHistoryOrder(user)" @input="updateHistoryOrder(user, $event)" class="history-input" />
									</view>
								</view>
								
								<!-- 自定义Switch滑块 -->
								<view class="custom-switch-wrapper">
									<view class="custom-switch" :class="{'switch-active': user.isShow==1}" @click.stop="toggleUserStatus(user)">
										<view class="switch-handle"></view>
									</view>
								</view>
							</view>
							
							<!-- 备注输入区域 -->
							<view class="user-note-row">
								<text class="note-label">备注：</text>
								<input type="text" :value="getLocalNotes(user)" @blur="updateNotes(user, $event)" placeholder="请输入备注" class="note-input" maxlength="50" />
							</view>
						</view>
						
						<!-- 用户状态和日期控制 -->
						<view class="user-controls-section">
							<!-- 用户状态控制区域 -->
							<view class="user-status-controls">
								<view class="status-label">用户状态：</view>
								<view class="status-buttons">
									<picker :value="getUserStatusIndex(user.user_status)" :range="userStatusOptions" range-key="label" @change="e => updateUserStatus(e, user)">
										<view class="status-btn active">{{getStatusText(user.user_status)}} <text class="iconfont icon-arrow-down"></text></view>
									</picker>
									<button class="status-btn" :class="user.notify ? 'notify-active' : 'notify-inactive'" @click.stop="updateNotifyStatus(user)">
										{{getNotificationText(user.notify)}}
									</button>
								</view>
							</view>
							
							<!-- 日期和开关区域 -->
							<view class="user-date-controls">
								<view class="date-input-container">
									<picker mode="date" :value="getLocalDate(user)" @change="e => updateDate(e, user)" 
										:disabled="user.dateStatus">
										<view class="date-input" :class="{'date-active': user.dateStatus}">
											{{getFormattedDate(user)}}
										</view>
									</picker>
								</view>
								<view class="count-label">
									<text>{{getDateDifference(user)}}天/{{getMonthDifference(user)}}月</text>
								</view>
								<view class="custom-switch-wrapper small">
									<view class="custom-switch" :class="{'switch-active': user.dateStatus}" @click.stop="toggleDateStatus(user)">
										<view class="switch-handle"></view>
									</view>
								</view>
							</view>
						</view>
						
						<!-- 操作按钮区域 -->
						<view class="action-buttons">
							<picker class="score-picker" :value="getEvaluateIndex(user.evaluate)" :range="evaluateOptions" @change="e => updateUserEvaluate(e, user)">
								<view class="action-btn comment-btn">评价 <text class="btn-value">{{user.evaluate || 60}}分</text></view>
							</picker>
							<button class="action-btn" :class="isUserProcessed(user) ? 'processed-btn' : 'unprocessed-btn'" @click.stop="toggleProcessedStatus(user)">
								{{isUserProcessed(user) ? '已处理' : '未处理'}}
							</button>
							<button class="action-btn save-btn">保存</button>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more-container" v-if="userList.length > 0">
				<button class="btn-load-more" @click="loadMore" :disabled="loading || !hasMore">
					<text v-if="loading">加载中...</text>
					<text v-else-if="hasMore">点击加载更多</text>
					<text v-else>没有更多数据了</text>
				</button>
			</view>
			
			<!-- 无数据提示 -->
			<view class="no-data" v-if="userList.length === 0 && !loading">
				<text>暂无用户数据</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { getUserList, getCurrentStaffInfo, getAvailableUserCodes, updateUserCode, setUserShowStatus, updateUserEvaluate, updateUserStatus, updateUserNotify, updateUserAcquaintance } from '@/api/work/centerorder.js'
	
	export default {
		data() {
			return {
				cityName: '',
				userList: [],
				currentPage: 1,
				pageSize: 10,
				total: 0,
				loading: false,
				isRefreshing: false,
				hasMore: true,
				searchKeyword: '',
				currentOrderCount: 0,
				notificationCount: 0,
				hotUserCount: 50,
				userCodeOptions: [],
				evaluateOptions: [],
				userStatusOptions: [
					{ value: '0', label: '单身' },
					{ value: '1', label: '进行中' },
					{ value: '2', label: '已结婚' }
				],
				today: new Date().toISOString().split('T')[0], // 今日日期，格式为 YYYY-MM-DD
				acquaintanceOptions: [] // 熟悉人数选项
			}
		},
		onLoad() {
			this.getStaffInfo()
			this.loadUserList()
			this.loadUserCodeOptions()
			this.initEvaluateOptions()
			this.initAcquaintanceOptions() // 初始化熟悉人数选项
		},
		methods: {
			// 获取用户状态在选项中的索引
			getUserStatusIndex(status) {
				if (!status && status !== '0') return 0 // 默认单身
				const idx = this.userStatusOptions.findIndex(option => option.value === status)
				return idx === -1 ? 0 : idx
			},
			
			// 更新用户状态
			async updateUserStatus(e, user) {
				const idx = e.detail.value
				const status = this.userStatusOptions[idx].value
				const oldStatus = user.user_status
				
				try {
					// 先更新本地数据，提升用户体验
					user.user_status = status
					
					const res = await updateUserStatus({ user_id: user.user_id, user_status: status })
					if (res.code === 200) {
						uni.showToast({ title: '状态更新成功', icon: 'success' })
					} else {
						throw new Error(res.msg || '状态更新失败')
					}
				} catch (err) {
					// 出错时还原
					user.user_status = oldStatus
					this.$forceUpdate()
					uni.showToast({ title: err.message || '状态更新失败', icon: 'none' })
				}
			},
			
			// 初始化评分选项
			initEvaluateOptions() {
				// 初始化评分选项，从50-100分，每5分一个选项
				this.evaluateOptions = []
				for (let i = 50; i <= 100; i += 5) {
					this.evaluateOptions.push(i)
				}
			},
			
			// 获取评分在选项中的索引
			getEvaluateIndex(evaluate) {
				if (!evaluate) return 2 // 默认60分（索引2对应60分）
				const idx = this.evaluateOptions.findIndex(score => score === parseInt(evaluate))
				return idx === -1 ? 2 : idx
			},
			
			// 更新用户评分
			async updateUserEvaluate(e, user) {
				const idx = e.detail.value
				const score = this.evaluateOptions[idx]
				const oldScore = user.evaluate
				
				try {
					// 先更新本地数据，提升用户体验
					user.evaluate = score
					
					const res = await updateUserEvaluate({ user_id: user.user_id, evaluate: score })
					if (res.code === 200) {
						uni.showToast({ title: '评分更新成功', icon: 'success' })
						
						// 强制更新视图
						this.$forceUpdate()
					} else {
						throw new Error(res.msg || '评分更新失败')
					}
				} catch (err) {
					// 出错时还原
					user.evaluate = oldScore
					// 强制更新视图
					this.$forceUpdate()
					uni.showToast({ title: err.message || '评分更新失败', icon: 'none' })
				}
			},
			
			// 获取当前店员信息
			async getStaffInfo() {
				try {
					const res = await getCurrentStaffInfo()
					if (res.code === 200) {
						this.cityName = res.data.region_name || '未知城市'
					}
				} catch (error) {
					console.error('获取店员信息失败', error)
					uni.showToast({
						title: '获取店员信息失败',
						icon: 'none'
					})
				}
			},
			
			// 加载用户列表
			async loadUserList(isRefresh = false) {
				if (this.loading) return
				
				this.loading = true
				
				if (isRefresh) {
					this.currentPage = 1
					this.userList = []
				}
				
				try {
					const params = {
						pageNum: this.currentPage,
						pageSize: this.pageSize,
						hotCount: this.hotUserCount || undefined
					}
					
					const res = await getUserList(params)
					
					if (res.code === 200) {
						const { list, total } = res.data
						
						// 为每个用户添加属性
						const processedList = list.map(user => {
							// 从本地存储获取日期状态
							const dateStatusKey = `date_status_${user.user_id}`;
							const dateStatus = uni.getStorageSync(dateStatusKey);
							
							return {
								...user,
								switchStatus: user.user_status === '1',
								dateStatus: dateStatus === 1, // 从本地存储中读取日期状态
								isShow: user.isShow === 1 || user.isShow === '1' ? 1 : 0
							}
						})
						
						if (isRefresh) {
							this.userList = processedList
						} else {
							this.userList = [...this.userList, ...processedList]
						}
						
						this.total = total
						this.hasMore = list.length >= this.pageSize
						
						// 统计当前订单总数
						this.currentOrderCount = list.reduce((sum, user) => sum + (parseInt(user.current_order_count) || 0), 0)
					}
				} catch (error) {
					console.error('加载用户列表失败', error)
					uni.showToast({
						title: '加载用户列表失败',
						icon: 'none'
					})
				} finally {
					this.loading = false
					if (isRefresh) {
						this.isRefreshing = false
					}
				}
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true
				this.loadUserList(true)
			},
			
			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.currentPage++
					this.loadUserList()
				}
			},
			
			// 搜索用户
			searchUsers() {
				// 复原所有用户的处理状态
				this.userList.forEach(user => {
					const key = `processed_${user.user_id}`;
					uni.setStorageSync(key, 0); // 将所有用户设为未处理
				});
				
				// 强制更新视图
				this.$forceUpdate();
				
				// 提示用户
				uni.showToast({
					title: '已复原所有用户状态',
					icon: 'success'
				});
			},
			
			// 查看用户详情
			viewUserDetail(user) {
				// 跳转到用户详情页面
				uni.navigateTo({
					url: `/pages/work/UserList/UserDetails?userId=${user.user_id}`
				})
			},
			
			// 拨打电话
			callUser(user) {
				if (user.phone) {
					uni.makePhoneCall({
						phoneNumber: user.phone,
						fail(err) {
							console.error('拨打电话失败', err)
						}
					})
				} else {
					uni.showToast({
						title: '用户没有电话号码',
						icon: 'none'
					})
				}
			},
			
			// 新建订单
			createOrder(user) {
				// 跳转到新建订单页面
				uni.navigateTo({
					url: `/pages/work/CreateOrder/CreateOrder?userId=${user.user_id}`
				})
			},
			
			// 计算年龄
			calculateAge(birthDate) {
				if (!birthDate) return '未知'
				
				try {
					const birth = new Date(birthDate)
					const now = new Date()
					
					// 检查日期是否有效
					if (isNaN(birth.getTime())) return '未知'
					
					let age = now.getFullYear() - birth.getFullYear()
					
					// 检查是否已过生日
					if (now.getMonth() < birth.getMonth() || 
						(now.getMonth() === birth.getMonth() && now.getDate() < birth.getDate())) {
						age--
					}
					
					return age > 0 ? age : 0 // 确保年龄不为负数
				} catch (e) {
					console.error('计算年龄出错:', e)
					return '未知'
				}
			},
			
			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return ''
				
				const date = new Date(dateString)
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
			},
			
			// 获取状态文本
			getStatusText(status) {
				switch(status) {
					case '0': return '单身'
					case '1': return '进行中'
					case '2': return '已结婚'
					default: return '单身'
				}
			},
			
			// 获取流程状态背景样式类
			getFlowBgClass(isflow) {
				switch(isflow) {
					case '1': return 'status-bg-ongoing' // 进行中 - 绿色
					case '0': return 'status-bg-paused' // 暂停 - 橘红色
					default: return 'status-bg-paused' // 默认暂停
				}
			},
			
			// 获取流程状态文本
			getFlowText(isflow) {
				switch(isflow) {
					case '1': return '进行中'
					case '0': return '已暂停'
					default: return '已暂停'
				}
			},
			
			// 格式化短日期 (MM-DD)
			formatShortDate(dateString) {
				if (!dateString) return ''
				
				const date = new Date(dateString)
				return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
			},
			
			// 切换用户状态
			async toggleUserStatus(user) {
				if (user.isShow === 1) return; // 已经是蓝色不重复操作
				try {
					const res = await setUserShowStatus({ user_id: user.user_id })
					if (res.code === 200) {
						uni.showToast({ title: '设置成功', icon: 'success' })
						
						// 立即更新UI：将所有用户isShow设为0，当前用户设为1
						this.userList.forEach(u => {
							// 先将所有用户设为不显示
							u.isShow = 0
						})
						// 将当前用户设为显示
						user.isShow = 1
						
						// 可选：如果担心后端数据不一致，也可以刷新列表
						// this.loadUserList(true)
					} else {
						throw new Error(res.msg || '设置失败')
					}
				} catch (err) {
					uni.showToast({ title: err.message || '设置失败', icon: 'none' })
				}
			},
			
			// 获取用户的本地保存日期
			getLocalDate(user) {
				const key = `date_${user.user_id}`;
				const storedDate = uni.getStorageSync(key);
				return storedDate || this.today; // 如果没有保存，返回今日日期
			},
			
			// 获取格式化的日期显示
			getFormattedDate(user) {
				// 如果开关是蓝色（激活状态），则显示今日日期
				if (user.dateStatus) {
					return this.today;
				} else {
					// 否则显示保存的日期
					return this.getLocalDate(user);
				}
			},
			
			// 更新日期
			updateDate(e, user) {
				const key = `date_${user.user_id}`;
				const newDate = e.detail.value;
				// 保存到本地存储
				uni.setStorageSync(key, newDate);
				// 强制更新视图
				this.$forceUpdate();
			},
			
			// 计算选择日期与当前日期的天数差
			getDateDifference(user) {
				const selectedDate = new Date(this.getLocalDate(user));
				const currentDate = new Date();
				
				// 计算时间差
				const diffTime = Math.abs(currentDate - selectedDate);
				// 转换为天数
				const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
				
				return diffDays;
			},
			
			// 计算选择日期与当前日期的月数差
			getMonthDifference(user) {
				const selectedDate = new Date(this.getLocalDate(user));
				const currentDate = new Date();
				
				// 计算月份差
				const diffMonths = (currentDate.getFullYear() - selectedDate.getFullYear()) * 12 + 
					(currentDate.getMonth() - selectedDate.getMonth());
				
				return Math.abs(diffMonths);
			},
			
			// 切换日期状态
			toggleDateStatus(user) {
				// 切换日期状态
				user.dateStatus = !user.dateStatus;
				
				// 存储到本地存储
				const key = `date_status_${user.user_id}`;
				uni.setStorageSync(key, user.dateStatus ? 1 : 0);
				
				// 强制更新视图
				this.$forceUpdate();
			},
			
			// 检查用户是否已处理
			isUserProcessed(user) {
				const key = `processed_${user.user_id}`;
				const processed = uni.getStorageSync(key);
				return processed === 1;
			},
			
			// 切换处理状态
			toggleProcessedStatus(user) {
				const key = `processed_${user.user_id}`;
				const currentStatus = this.isUserProcessed(user);
				
				// 切换状态并保存
				uni.setStorageSync(key, currentStatus ? 0 : 1);
				
				// 强制更新视图
				this.$forceUpdate();
			},
			
			// 加载user_code下拉选项
			async loadUserCodeOptions() {
				try {
					const res = await getAvailableUserCodes()
					if (res.code === 200 && Array.isArray(res.data)) {
						this.userCodeOptions = res.data
					}
				} catch (e) {
					console.error('获取user_code下拉选项失败', e)
				}
			},
			
			// 获取当前user_code在下拉选项中的索引
			getUserCodeIndex(user_code) {
				if (!user_code) return 0
				const idx = this.userCodeOptions.findIndex(item => item.user_code === user_code)
				return idx === -1 ? 0 : idx
			},
			
			// 处理user_code选择变化
			async onUserCodeChange(e, user) {
				const idx = e.detail.value
				const selected = this.userCodeOptions[idx]
				if (!selected) return
				const oldUserCode = user.user_code
				user.user_code = selected.user_code
				user.code_id = selected.code_id
				try {
					const res = await updateUserCode({ user_id: user.user_id, user_code: selected.user_code })
					if (res.code === 200) {
						uni.showToast({ title: '编号更新成功', icon: 'success' })
						this.loadUserList(true)
					} else {
						throw new Error(res.msg || '更新失败')
					}
				} catch (err) {
					user.user_code = oldUserCode
					uni.showToast({ title: err.message || '用户编号重复，请选择其他编号', icon: 'none' })
				}
			},
			
			// 获取本地存储的当前订单数
			getLocalCurrentOrder(user) {
				const key = `current_order_${user.user_id}`
				const storedValue = uni.getStorageSync(key)
				return storedValue !== '' ? parseInt(storedValue) : (user.current_order_count || 0)
			},
			
			// 切换当前订单状态（0/1）
			toggleCurrentOrder(user) {
				const key = `current_order_${user.user_id}`
				const currentValue = this.getLocalCurrentOrder(user)
				// 在0和1之间切换
				const newValue = currentValue === 0 ? 1 : 0
				// 保存到本地存储
				uni.setStorageSync(key, newValue)
				// 强制更新视图
				this.$forceUpdate()
			},
			
			// 获取本地存储的历史订单数
			getLocalHistoryOrder(user) {
				const key = `history_order_${user.user_id}`
				const storedValue = uni.getStorageSync(key)
				return storedValue !== '' ? storedValue : (user.history_order_count || 0)
			},
			
			// 更新历史订单数
			updateHistoryOrder(user, event) {
				const key = `history_order_${user.user_id}`
				const value = event.detail.value
				// 保存到本地存储
				uni.setStorageSync(key, value)
				// 强制更新视图
				this.$forceUpdate()
			},
			
			// 获取本地存储的备注
			getLocalNotes(user) {
				const key = `notes_${user.user_id}`
				const storedValue = uni.getStorageSync(key)
				return storedValue !== '' ? storedValue : (user.notes || '')
			},
			
			// 更新备注
			updateNotes(user, event) {
				const key = `notes_${user.user_id}`
				const value = event.detail.value
				// 保存到本地存储
				uni.setStorageSync(key, value)
				// 强制更新视图
				this.$forceUpdate()
			},
			
			// 获取通知按钮文本
			getNotificationText(notify) {
				switch(notify) {
					case '1': return '有通知'
					case '2': return '已通知'
					default: return '无通知'
				}
			},
			
			// 获取通知按钮样式类
			getNotificationClass(notify) {
				switch(notify) {
					case '1': return 'notification-active' // 红色
					case '2': return 'notification-processed' // 灰色
					default: return 'notification-inactive' // 黄色
				}
			},
			
			// 更新通知状态
			async updateNotifyStatus(user) {
				// 只有当状态为"有通知"(1)时才能点击更新为"已通知"(2)
				if (user.notify !== '1') {
					return
				}
				
				const oldNotify = user.notify
				
				try {
					// 先更新本地数据，提升用户体验
					user.notify = '2'
					
					const res = await updateUserNotify({ user_id: user.user_id })
					if (res.code === 200) {
						uni.showToast({ title: '通知状态已更新', icon: 'success' })
					} else {
						throw new Error(res.msg || '更新通知状态失败')
					}
				} catch (err) {
					// 出错时还原
					user.notify = oldNotify
					this.$forceUpdate()
					uni.showToast({ title: err.message || '更新通知状态失败', icon: 'none' })
				}
			},
			
			// 初始化熟悉人数选项
			initAcquaintanceOptions() {
				// 初始化熟悉人数选项，从1-60
				this.acquaintanceOptions = []
				for (let i = 0; i <= 60; i++) {
					this.acquaintanceOptions.push(i)
				}
			},
			
			// 获取熟悉人数在选项中的索引
			getAcquaintanceIndex(acqNo) {
				if (!acqNo) return 0 // 默认0
				const idx = this.acquaintanceOptions.findIndex(num => num === parseInt(acqNo))
				return idx === -1 ? 0 : idx
			},
			
			// 更新熟悉人数
			async updateAcquaintance(e, user) {
				const idx = e.detail.value
				const acqNo = this.acquaintanceOptions[idx]
				const oldAcqNo = user.acqNo
				
				try {
					// 先更新本地数据，提升用户体验
					user.acqNo = acqNo
					
					const res = await updateUserAcquaintance({ user_id: user.user_id, acqNo: acqNo })
					if (res.code === 200) {
						uni.showToast({ title: '熟悉人数更新成功', icon: 'success' })
						
						// 强制更新视图
						this.$forceUpdate()
					} else {
						throw new Error(res.msg || '熟悉人数更新失败')
					}
				} catch (err) {
					// 出错时还原
					user.acqNo = oldAcqNo
					// 强制更新视图
					this.$forceUpdate()
					uni.showToast({ title: err.message || '熟悉人数更新失败', icon: 'none' })
				}
			}
		}
	}
</script>

<style>
	.user-list-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f7fa;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
	}
	
	.header {
		padding: 12px 16px;
		background-color: #fff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #eaeef3;
		box-shadow: 0 1px 3px rgba(0,0,0,0.05);
	}
	
	.city-select {
		display: flex;
		align-items: center;
		font-size: 16px;
		font-weight: 600;
		color: #333;
	}
	
	.icon-arrow-down {
		margin-left: 5px;
		font-size: 12px;
		color: #666;
	}
	
	.user-stats {
		background-color: #fff;
		padding: 10px 16px;
		margin-bottom: 10px;
		border-bottom: 1px solid #eaeef3;
	}
	
	.stats-item {
		display: flex;
		justify-content: space-between;
		font-size: 14px;
		color: #555;
	}
	
	.stats-label {
		font-weight: 600;
		color: #333;
	}
	
	.search-bar {
		padding: 10px 16px;
		display: flex;
		background-color: #fff;
		margin-bottom: 10px;
		align-items: center;
		border-radius: 8px;
		box-shadow: 0 1px 3px rgba(0,0,0,0.05);
	}
	
	.search-input-wrapper {
		flex: 1;
		display: flex;
		align-items: center;
	}
	
	.label {
		font-size: 14px;
		margin-right: 12px;
		color: #555;
	}
	
	.number-input {
		width: 80px;
		height: 36px;
		border: 1px solid #dbe0e8;
		border-radius: 6px;
		padding: 0 10px;
		font-size: 14px;
		color: #333;
		background-color: #f9fafc;
	}
	
	.search-btn {
		margin-left: 12px;
		height: 36px;
		line-height: 36px;
		padding: 0 18px;
		background-color: #007aff;
		color: #fff;
		border-radius: 6px;
		font-size: 14px;
		font-weight: 500;
		box-shadow: 0 2px 5px rgba(0,122,255,0.2);
		transition: all 0.2s;
	}
	
	.search-btn:active {
		transform: translateY(1px);
		box-shadow: 0 1px 3px rgba(0,122,255,0.2);
	}
	
	.user-scroll-view {
		flex: 1;
		padding: 0 10px;
	}
	
	.user-list {
		padding-bottom: 10px;
	}
	
	.user-card {
		background-color: #fff;
		margin-bottom: 12px;
		display: flex;
		position: relative;
		border-radius: 10px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0,0,0,0.08);
		transition: all 0.2s;
	}
	
	.user-card:active {
		transform: translateY(1px);
		box-shadow: 0 1px 5px rgba(0,0,0,0.05);
	}
	
	.user-status-bg {
		width: 70px;
		padding: 15px 5px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #fff;
	}
	
	.status-bg-ongoing {
		background-color: #4cd964;
		background-image: linear-gradient(to bottom, #4cd964, #43c158);
	}
	
	.status-bg-paused {
		background-color: #ff9500;
		background-image: linear-gradient(to bottom, #ff9500, #f08300);
	}
	
	.user-id {
		font-size: 16px;
		font-weight: 700;
		margin-bottom: 6px;
		text-shadow: 0 1px 2px rgba(0,0,0,0.1);
	}
	
	.user-status-text {
		font-size: 12px;
		font-weight: 500;
	}
	
	.user-basic-info {
		flex: 1;
		padding: 0;
		display: flex;
		flex-direction: column;
	}
	
	.user-info-header {
		padding: 12px 15px 10px;
		border-bottom: 1px solid #f0f2f5;
	}
	
	.user-main-info {
		display: flex;
		flex-direction: column;
	}
	
	.user-name-row {
		display: flex;
		align-items: center;
		margin-bottom: 5px;
		justify-content: space-between;
	}
	
	.user-name {
		font-weight: 600;
		font-size: 16px;
		color: #333;
	}
	
	.user-gender-age {
		display: flex;
		align-items: center;
	}
	
	.user-gender, .user-age {
		font-size: 14px;
		color: #666;
		margin-left: 10px;
	}
	
	.user-detail-row {
		display: flex;
		margin-bottom: 0;
		font-size: 13px;
		color: #666;
	}
	
	.user-height, .user-weight {
		margin-right: 12px;
	}
	
	.user-phone {
		color: #007aff;
		font-weight: 500;
	}
	
	.user-order-section {
		padding: 10px 15px;
		background-color: #fafbfc;
		border-bottom: 1px solid #f0f2f5;
	}
	
	.order-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	
	.order-counts {
		display: flex;
		align-items: center;
	}
	
	.user-orders, .user-history {
		font-size: 13px;
		color: #666;
		margin-right: 15px;
	}
	
	.user-orders {
		cursor: pointer;
	}
	
	.history-order-input {
		display: flex;
		align-items: center;
		font-size: 13px;
		color: #666;
	}
	
	.history-input {
		width: 40px;
		height: 24px;
		border: 1px solid #dbe0e8;
		border-radius: 4px;
		text-align: center;
		margin-left: 4px;
		font-size: 13px;
	}
	
	/* 自定义Switch滑块 */
	.custom-switch-wrapper {
		display: flex;
		align-items: center;
	}
	
	.custom-switch-wrapper.small .custom-switch {
		width: 40px;
		height: 20px;
	}
	
	.custom-switch-wrapper.small .switch-handle {
		width: 16px;
		height: 16px;
	}
	
	.custom-switch-wrapper.small .switch-active .switch-handle {
		transform: translateX(20px);
	}
	
	.custom-switch {
		position: relative;
		width: 50px;
		height: 26px;
		background-color: #e5e5ea;
		border-radius: 13px;
		transition: all 0.3s ease;
		cursor: pointer;
		box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
	}
	
	.custom-switch .switch-handle {
		position: absolute;
		left: 2px;
		top: 2px;
		width: 22px;
		height: 22px;
		background-color: #fff;
		border-radius: 50%;
		transition: all 0.3s ease;
		box-shadow: 0 2px 5px rgba(0,0,0,0.2);
	}
	
	.custom-switch.switch-active {
		background-color: #007aff;
		box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
	}
	
	.custom-switch.switch-active .switch-handle {
		transform: translateX(24px);
	}
	
	.user-note-row {
		display: flex;
		align-items: center;
	}
	
	.note-label {
		font-size: 13px;
		color: #555;
		width: 45px;
		font-weight: 500;
	}
	
	.note-input {
		flex: 1;
		height: 32px;
		border: 1px solid #dbe0e8;
		border-radius: 6px;
		font-size: 13px;
		padding: 0 10px;
		background-color: #fff;
		color: #333;
	}
	
	.user-controls-section {
		padding: 10px 15px;
		background-color: #fff;
	}
	
	.user-status-controls {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
	}
	
	.status-label {
		font-size: 13px;
		color: #555;
		width: 65px;
		font-weight: 500;
	}
	
	.status-buttons {
		display: flex;
	}
	
	.status-btn {
		height: 32px;
		line-height: 32px;
		padding: 0 12px;
		font-size: 13px;
		margin-right: 8px;
		border-radius: 6px;
		min-width: 70px;
		text-align: center;
		font-weight: 500;
	}
	
	.status-btn.active {
		background-color: #f0f2f5;
		color: #333;
		border: 1px solid #dbe0e8;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.status-btn .icon-arrow-down {
		font-size: 12px;
		margin-left: 4px;
	}
	
	.status-btn.notification-active {
		background-color: #ff3b30;
		background-image: linear-gradient(to bottom, #ff3b30, #e8352b);
		color: #fff;
		border: none;
		box-shadow: 0 2px 5px rgba(255,59,48,0.2);
	}
	
	.status-btn.notification-inactive {
		background-color: #ff9500;
		background-image: linear-gradient(to bottom, #ff9500, #f08300);
		color: #fff;
		border: none;
		box-shadow: 0 2px 5px rgba(255,149,0,0.2);
	}
	
	.status-btn.notification-processed {
		background-color: #8e8e93;
		background-image: linear-gradient(to bottom, #8e8e93, #7d7d82);
		color: #fff;
		border: none;
		box-shadow: 0 2px 5px rgba(142,142,147,0.2);
	}
	
	.user-date-controls {
		display: flex;
		align-items: center;
	}
	
	.date-input-container {
		width: 110px;
		margin-right: 12px;
	}
	
	.date-input {
		height: 32px;
		border: 1px solid #dbe0e8;
		border-radius: 6px;
		font-size: 13px;
		padding: 0 10px;
		width: 100%;
		background-color: #f0f2f5; /* 默认灰色背景 */
		color: #333;
		text-align: center;
		font-weight: 500;
	}
	
	.date-input.date-active {
		background-color: #ff9500;
		background-image: linear-gradient(to bottom, #ff9500, #f08300);
		color: #fff;
		box-shadow: 0 2px 5px rgba(255,149,0,0.2);
	}
	
	.count-label {
		font-size: 13px;
		color: #555;
		margin-right: 12px;
		width: 65px;
		font-weight: 500;
	}
	
	.action-buttons {
		padding: 10px 15px;
		display: flex;
		justify-content: flex-end;
		border-top: 1px solid #f0f2f5;
	}
	
	.action-btn {
		height: 32px;
		line-height: 32px;
		padding: 0 15px;
		font-size: 13px;
		margin-left: 8px;
		border-radius: 6px;
		font-weight: 500;
		transition: all 0.2s;
	}
	
	.action-btn:active {
		transform: translateY(1px);
	}
	
	.score-picker {
		margin-left: 8px;
	}
	
	.comment-btn {
		background-color: #f0f2f5;
		color: #333;
		border: 1px solid #dbe0e8;
	}
	
	.btn-value {
		color: #007aff;
		font-weight: 600;
	}
	
	.processed-btn {
		background-color: #4cd964;
		background-image: linear-gradient(to bottom, #4cd964, #43c158);
		color: #fff;
		border: none;
		box-shadow: 0 2px 5px rgba(76,217,100,0.2);
	}
	
	.save-btn {
		background-color: #007aff;
		background-image: linear-gradient(to bottom, #007aff, #0070e8);
		color: #fff;
		border: none;
		box-shadow: 0 2px 5px rgba(0,122,255,0.2);
	}
	
	.load-more-container {
		padding: 12px;
		display: flex;
		justify-content: center;
	}
	
	.btn-load-more {
		background-color: #fff;
		border: 1px solid #dbe0e8;
		color: #555;
		font-size: 14px;
		padding: 8px 20px;
		border-radius: 6px;
		box-shadow: 0 2px 5px rgba(0,0,0,0.05);
		transition: all 0.2s;
	}
	
	.btn-load-more:active {
		transform: translateY(1px);
		box-shadow: 0 1px 3px rgba(0,0,0,0.03);
	}
	
	.btn-load-more[disabled] {
		color: #999;
		background-color: #f5f7fa;
	}
	
	.no-data {
		padding: 40px 0;
		text-align: center;
		color: #999;
		font-size: 14px;
	}
	
	.unprocessed-btn {
		background-color: #f0f2f5;
		color: #666;
		border: 1px solid #dbe0e8;
	}
	
	.acquaintance-row {
		display: flex;
		align-items: center;
		margin-top: 6px;
	}
	
	.acquaintance-label {
		font-size: 13px;
		color: #666;
		margin-right: 5px;
	}
	
	.acquaintance-picker {
		background-color: #f0f2f5;
		border-radius: 4px;
		padding: 2px 8px;
		min-width: 40px;
		text-align: center;
	}
	
	.acquaintance-value {
		font-size: 13px;
		color: #007aff;
		font-weight: 500;
	}
</style>
