{"version": 3, "names": ["_core", "require", "_helperEnvironmentVisitor", "findBareSupers", "traverse", "visitors", "merge", "Super", "path", "node", "parentPath", "isCallExpression", "callee", "push", "environmentVisitor", "referenceVisitor", "TSTypeAnnotation|TypeAnnotation", "skip", "ReferencedIdentifier", "scope", "hasOwnBinding", "name", "rename", "handleClassTDZ", "state", "classBinding", "getBinding", "classNameTDZError", "file", "addHelper", "throwNode", "t", "callExpression", "stringLiteral", "replaceWith", "sequenceExpression", "classFieldDefinitionEvaluationTDZVisitor", "injectInitialization", "constructor", "nodes", "renamer", "lastReturnsThis", "length", "isDerived", "superClass", "newConstructor", "classMethod", "identifier", "blockStatement", "params", "restElement", "body", "template", "statement", "ast", "get", "unshiftContainer", "bareSupers", "<PERSON><PERSON><PERSON><PERSON>", "bareSuper", "map", "n", "cloneNode", "isExpressionStatement", "allNodes", "toExpression", "thisExpression", "insertAfter", "extractComputedKeys", "computedPaths", "declarations", "id", "computedPath", "computedKey", "isReferencedIdentifier", "computedNode", "isConstantExpression", "isUidReference", "isIdentifier", "hasUid", "isMemoiseAssignment", "isAssignmentExpression", "operator", "left", "expressionStatement", "key", "ident", "generateUidIdentifierBasedOnNode", "kind", "assignmentExpression"], "sources": ["../src/misc.ts"], "sourcesContent": ["import { template, traverse, types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { NodePath, Scope, Visitor, Binding } from \"@babel/traverse\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\n\nconst findBareSupers = traverse.visitors.merge<NodePath<t.CallExpression>[]>([\n  {\n    Super(path) {\n      const { node, parentPath } = path;\n      if (parentPath.isCallExpression({ callee: node })) {\n        this.push(parentPath);\n      }\n    },\n  },\n  environmentVisitor,\n]);\n\nconst referenceVisitor: Visitor<{ scope: Scope }> = {\n  \"TSTypeAnnotation|TypeAnnotation\"(\n    path: NodePath<t.TSTypeAnnotation | t.TypeAnnotation>,\n  ) {\n    path.skip();\n  },\n\n  ReferencedIdentifier(path: NodePath<t.Identifier>, { scope }) {\n    if (scope.hasOwnBinding(path.node.name)) {\n      scope.rename(path.node.name);\n      path.skip();\n    }\n  },\n};\n\ntype HandleClassTDZState = {\n  classBinding: Binding;\n  file: File;\n};\n\nfunction handleClassTDZ(\n  path: NodePath<t.Identifier>,\n  state: HandleClassTDZState,\n) {\n  if (\n    state.classBinding &&\n    state.classBinding === path.scope.getBinding(path.node.name)\n  ) {\n    const classNameTDZError = state.file.addHelper(\"classNameTDZError\");\n    const throwNode = t.callExpression(classNameTDZError, [\n      t.stringLiteral(path.node.name),\n    ]);\n\n    path.replaceWith(t.sequenceExpression([throwNode, path.node]));\n    path.skip();\n  }\n}\n\nconst classFieldDefinitionEvaluationTDZVisitor: Visitor<HandleClassTDZState> = {\n  ReferencedIdentifier: handleClassTDZ,\n};\n\ninterface RenamerState {\n  scope: Scope;\n}\n\nexport function injectInitialization(\n  path: NodePath<t.Class>,\n  constructor: NodePath<t.ClassMethod> | undefined,\n  nodes: t.ExpressionStatement[],\n  renamer?: (visitor: Visitor<RenamerState>, state: RenamerState) => void,\n  lastReturnsThis?: boolean,\n) {\n  if (!nodes.length) return;\n\n  const isDerived = !!path.node.superClass;\n\n  if (!constructor) {\n    const newConstructor = t.classMethod(\n      \"constructor\",\n      t.identifier(\"constructor\"),\n      [],\n      t.blockStatement([]),\n    );\n\n    if (isDerived) {\n      newConstructor.params = [t.restElement(t.identifier(\"args\"))];\n      newConstructor.body.body.push(template.statement.ast`super(...args)`);\n    }\n\n    [constructor] = path\n      .get(\"body\")\n      .unshiftContainer(\"body\", newConstructor) as NodePath<t.ClassMethod>[];\n  }\n\n  if (renamer) {\n    renamer(referenceVisitor, { scope: constructor.scope });\n  }\n\n  if (isDerived) {\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    constructor.traverse(findBareSupers, bareSupers);\n    let isFirst = true;\n    for (const bareSuper of bareSupers) {\n      if (isFirst) {\n        isFirst = false;\n      } else {\n        nodes = nodes.map(n => t.cloneNode(n));\n      }\n      if (!bareSuper.parentPath.isExpressionStatement()) {\n        const allNodes: t.Expression[] = [\n          bareSuper.node,\n          ...nodes.map(n => t.toExpression(n)),\n        ];\n        if (!lastReturnsThis) allNodes.push(t.thisExpression());\n        bareSuper.replaceWith(t.sequenceExpression(allNodes));\n      } else {\n        bareSuper.insertAfter(nodes);\n      }\n    }\n  } else {\n    constructor.get(\"body\").unshiftContainer(\"body\", nodes);\n  }\n}\n\nexport function extractComputedKeys(\n  path: NodePath<t.Class>,\n  computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[],\n  file: File,\n) {\n  const declarations: t.ExpressionStatement[] = [];\n  const state = {\n    classBinding: path.node.id && path.scope.getBinding(path.node.id.name),\n    file,\n  };\n  for (const computedPath of computedPaths) {\n    const computedKey = computedPath.get(\"key\");\n    if (computedKey.isReferencedIdentifier()) {\n      handleClassTDZ(computedKey, state);\n    } else {\n      computedKey.traverse(classFieldDefinitionEvaluationTDZVisitor, state);\n    }\n\n    const computedNode = computedPath.node;\n    // Make sure computed property names are only evaluated once (upon class definition)\n    // and in the right order in combination with static properties\n    if (!computedKey.isConstantExpression()) {\n      const scope = path.scope;\n      const isUidReference =\n        t.isIdentifier(computedKey.node) && scope.hasUid(computedKey.node.name);\n      const isMemoiseAssignment =\n        computedKey.isAssignmentExpression({ operator: \"=\" }) &&\n        t.isIdentifier(computedKey.node.left) &&\n        scope.hasUid(computedKey.node.left.name);\n      if (isUidReference) {\n        continue;\n      } else if (isMemoiseAssignment) {\n        declarations.push(t.expressionStatement(t.cloneNode(computedNode.key)));\n        computedNode.key = t.cloneNode(\n          (computedNode.key as t.AssignmentExpression).left as t.Identifier,\n        );\n      } else {\n        const ident = path.scope.generateUidIdentifierBasedOnNode(\n          computedNode.key,\n        );\n        // Declaring in the same block scope\n        // Ref: https://github.com/babel/babel/pull/10029/files#diff-fbbdd83e7a9c998721c1484529c2ce92\n        scope.push({\n          id: ident,\n          kind: \"let\",\n        });\n        declarations.push(\n          t.expressionStatement(\n            t.assignmentExpression(\"=\", t.cloneNode(ident), computedNode.key),\n          ),\n        );\n        computedNode.key = t.cloneNode(ident);\n      }\n    }\n  }\n\n  return declarations;\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,yBAAA,GAAAD,OAAA;AAEA,MAAME,cAAc,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAA+B,CAC3E;EACEC,KAAKA,CAACC,IAAI,EAAE;IACV,MAAM;MAAEC,IAAI;MAAEC;IAAW,CAAC,GAAGF,IAAI;IACjC,IAAIE,UAAU,CAACC,gBAAgB,CAAC;MAAEC,MAAM,EAAEH;IAAK,CAAC,CAAC,EAAE;MACjD,IAAI,CAACI,IAAI,CAACH,UAAU,CAAC;IACvB;EACF;AACF,CAAC,EACDI,iCAAkB,CACnB,CAAC;AAEF,MAAMC,gBAA2C,GAAG;EAClD,iCAAiCC,CAC/BR,IAAqD,EACrD;IACAA,IAAI,CAACS,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,oBAAoBA,CAACV,IAA4B,EAAE;IAAEW;EAAM,CAAC,EAAE;IAC5D,IAAIA,KAAK,CAACC,aAAa,CAACZ,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,EAAE;MACvCF,KAAK,CAACG,MAAM,CAACd,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC;MAC5Bb,IAAI,CAACS,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAOD,SAASM,cAAcA,CACrBf,IAA4B,EAC5BgB,KAA0B,EAC1B;EACA,IACEA,KAAK,CAACC,YAAY,IAClBD,KAAK,CAACC,YAAY,KAAKjB,IAAI,CAACW,KAAK,CAACO,UAAU,CAAClB,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,EAC5D;IACA,MAAMM,iBAAiB,GAAGH,KAAK,CAACI,IAAI,CAACC,SAAS,CAAC,mBAAmB,CAAC;IACnE,MAAMC,SAAS,GAAGC,WAAC,CAACC,cAAc,CAACL,iBAAiB,EAAE,CACpDI,WAAC,CAACE,aAAa,CAACzB,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,CAChC,CAAC;IAEFb,IAAI,CAAC0B,WAAW,CAACH,WAAC,CAACI,kBAAkB,CAAC,CAACL,SAAS,EAAEtB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9DD,IAAI,CAACS,IAAI,CAAC,CAAC;EACb;AACF;AAEA,MAAMmB,wCAAsE,GAAG;EAC7ElB,oBAAoB,EAAEK;AACxB,CAAC;AAMM,SAASc,oBAAoBA,CAClC7B,IAAuB,EACvB8B,WAAgD,EAChDC,KAA8B,EAC9BC,OAAuE,EACvEC,eAAyB,EACzB;EACA,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;EAEnB,MAAMC,SAAS,GAAG,CAAC,CAACnC,IAAI,CAACC,IAAI,CAACmC,UAAU;EAExC,IAAI,CAACN,WAAW,EAAE;IAChB,MAAMO,cAAc,GAAGd,WAAC,CAACe,WAAW,CAClC,aAAa,EACbf,WAAC,CAACgB,UAAU,CAAC,aAAa,CAAC,EAC3B,EAAE,EACFhB,WAAC,CAACiB,cAAc,CAAC,EAAE,CACrB,CAAC;IAED,IAAIL,SAAS,EAAE;MACbE,cAAc,CAACI,MAAM,GAAG,CAAClB,WAAC,CAACmB,WAAW,CAACnB,WAAC,CAACgB,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;MAC7DF,cAAc,CAACM,IAAI,CAACA,IAAI,CAACtC,IAAI,CAACuC,cAAQ,CAACC,SAAS,CAACC,GAAI,gBAAe,CAAC;IACvE;IAEA,CAAChB,WAAW,CAAC,GAAG9B,IAAI,CACjB+C,GAAG,CAAC,MAAM,CAAC,CACXC,gBAAgB,CAAC,MAAM,EAAEX,cAAc,CAA8B;EAC1E;EAEA,IAAIL,OAAO,EAAE;IACXA,OAAO,CAACzB,gBAAgB,EAAE;MAAEI,KAAK,EAAEmB,WAAW,CAACnB;IAAM,CAAC,CAAC;EACzD;EAEA,IAAIwB,SAAS,EAAE;IACb,MAAMc,UAAwC,GAAG,EAAE;IACnDnB,WAAW,CAAClC,QAAQ,CAACD,cAAc,EAAEsD,UAAU,CAAC;IAChD,IAAIC,OAAO,GAAG,IAAI;IAClB,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;MAClC,IAAIC,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACLnB,KAAK,GAAGA,KAAK,CAACqB,GAAG,CAACC,CAAC,IAAI9B,WAAC,CAAC+B,SAAS,CAACD,CAAC,CAAC,CAAC;MACxC;MACA,IAAI,CAACF,SAAS,CAACjD,UAAU,CAACqD,qBAAqB,CAAC,CAAC,EAAE;QACjD,MAAMC,QAAwB,GAAG,CAC/BL,SAAS,CAAClD,IAAI,EACd,GAAG8B,KAAK,CAACqB,GAAG,CAACC,CAAC,IAAI9B,WAAC,CAACkC,YAAY,CAACJ,CAAC,CAAC,CAAC,CACrC;QACD,IAAI,CAACpB,eAAe,EAAEuB,QAAQ,CAACnD,IAAI,CAACkB,WAAC,CAACmC,cAAc,CAAC,CAAC,CAAC;QACvDP,SAAS,CAACzB,WAAW,CAACH,WAAC,CAACI,kBAAkB,CAAC6B,QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACLL,SAAS,CAACQ,WAAW,CAAC5B,KAAK,CAAC;MAC9B;IACF;EACF,CAAC,MAAM;IACLD,WAAW,CAACiB,GAAG,CAAC,MAAM,CAAC,CAACC,gBAAgB,CAAC,MAAM,EAAEjB,KAAK,CAAC;EACzD;AACF;AAEO,SAAS6B,mBAAmBA,CACjC5D,IAAuB,EACvB6D,aAA0D,EAC1DzC,IAAU,EACV;EACA,MAAM0C,YAAqC,GAAG,EAAE;EAChD,MAAM9C,KAAK,GAAG;IACZC,YAAY,EAAEjB,IAAI,CAACC,IAAI,CAAC8D,EAAE,IAAI/D,IAAI,CAACW,KAAK,CAACO,UAAU,CAAClB,IAAI,CAACC,IAAI,CAAC8D,EAAE,CAAClD,IAAI,CAAC;IACtEO;EACF,CAAC;EACD,KAAK,MAAM4C,YAAY,IAAIH,aAAa,EAAE;IACxC,MAAMI,WAAW,GAAGD,YAAY,CAACjB,GAAG,CAAC,KAAK,CAAC;IAC3C,IAAIkB,WAAW,CAACC,sBAAsB,CAAC,CAAC,EAAE;MACxCnD,cAAc,CAACkD,WAAW,EAAEjD,KAAK,CAAC;IACpC,CAAC,MAAM;MACLiD,WAAW,CAACrE,QAAQ,CAACgC,wCAAwC,EAAEZ,KAAK,CAAC;IACvE;IAEA,MAAMmD,YAAY,GAAGH,YAAY,CAAC/D,IAAI;IAGtC,IAAI,CAACgE,WAAW,CAACG,oBAAoB,CAAC,CAAC,EAAE;MACvC,MAAMzD,KAAK,GAAGX,IAAI,CAACW,KAAK;MACxB,MAAM0D,cAAc,GAClB9C,WAAC,CAAC+C,YAAY,CAACL,WAAW,CAAChE,IAAI,CAAC,IAAIU,KAAK,CAAC4D,MAAM,CAACN,WAAW,CAAChE,IAAI,CAACY,IAAI,CAAC;MACzE,MAAM2D,mBAAmB,GACvBP,WAAW,CAACQ,sBAAsB,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAC,CAAC,IACrDnD,WAAC,CAAC+C,YAAY,CAACL,WAAW,CAAChE,IAAI,CAAC0E,IAAI,CAAC,IACrChE,KAAK,CAAC4D,MAAM,CAACN,WAAW,CAAChE,IAAI,CAAC0E,IAAI,CAAC9D,IAAI,CAAC;MAC1C,IAAIwD,cAAc,EAAE;QAClB;MACF,CAAC,MAAM,IAAIG,mBAAmB,EAAE;QAC9BV,YAAY,CAACzD,IAAI,CAACkB,WAAC,CAACqD,mBAAmB,CAACrD,WAAC,CAAC+B,SAAS,CAACa,YAAY,CAACU,GAAG,CAAC,CAAC,CAAC;QACvEV,YAAY,CAACU,GAAG,GAAGtD,WAAC,CAAC+B,SAAS,CAC3Ba,YAAY,CAACU,GAAG,CAA4BF,IAC/C,CAAC;MACH,CAAC,MAAM;QACL,MAAMG,KAAK,GAAG9E,IAAI,CAACW,KAAK,CAACoE,gCAAgC,CACvDZ,YAAY,CAACU,GACf,CAAC;QAGDlE,KAAK,CAACN,IAAI,CAAC;UACT0D,EAAE,EAAEe,KAAK;UACTE,IAAI,EAAE;QACR,CAAC,CAAC;QACFlB,YAAY,CAACzD,IAAI,CACfkB,WAAC,CAACqD,mBAAmB,CACnBrD,WAAC,CAAC0D,oBAAoB,CAAC,GAAG,EAAE1D,WAAC,CAAC+B,SAAS,CAACwB,KAAK,CAAC,EAAEX,YAAY,CAACU,GAAG,CAClE,CACF,CAAC;QACDV,YAAY,CAACU,GAAG,GAAGtD,WAAC,CAAC+B,SAAS,CAACwB,KAAK,CAAC;MACvC;IACF;EACF;EAEA,OAAOhB,YAAY;AACrB"}