{"_from": "@babel/plugin-syntax-optional-catch-binding@^7.8.3", "_id": "@babel/plugin-syntax-optional-catch-binding@7.8.3", "_inBundle": false, "_integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==", "_location": "/@babel/plugin-syntax-optional-catch-binding", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-optional-catch-binding@^7.8.3", "name": "@babel/plugin-syntax-optional-catch-binding", "escapedName": "@babel%2fplugin-syntax-optional-catch-binding", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@babel/plugin-transform-optional-catch-binding", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "_shasum": "6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1", "_spec": "@babel/plugin-syntax-optional-catch-binding@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "deprecated": false, "description": "Allow parsing of optional catch bindings", "devDependencies": {"@babel/core": "^7.8.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-optional-catch-binding", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "version": "7.8.3"}