{"TODO": "The @babel/traverse dependency is only needed for the NodePath TS type. We can consider exporting it from @babel/core.", "_from": "@babel/helper-hoist-variables@^7.22.5", "_id": "@babel/helper-hoist-variables@7.22.5", "_inBundle": false, "_integrity": "sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==", "_location": "/@babel/helper-hoist-variables", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-hoist-variables@^7.22.5", "name": "@babel/helper-hoist-variables", "escapedName": "@babel%2fhelper-hoist-variables", "scope": "@babel", "rawSpec": "^7.22.5", "saveSpec": null, "fetchSpec": "^7.22.5"}, "_requiredBy": ["/@babel/plugin-transform-modules-systemjs", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "_shasum": "c01a007dac05c085914e8fb652b339db50d823bb", "_spec": "@babel/helper-hoist-variables@^7.22.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\traverse", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.5"}, "deprecated": false, "description": "Helper function to hoist variables", "devDependencies": {"@babel/traverse": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-hoist-variables", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-hoist-variables", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-hoist-variables"}, "type": "commonjs", "version": "7.22.5"}