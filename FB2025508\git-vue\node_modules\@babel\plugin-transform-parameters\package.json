{"_from": "@babel/plugin-transform-parameters@^7.23.3", "_id": "@babel/plugin-transform-parameters@7.23.3", "_inBundle": false, "_integrity": "sha512-09lMt6UsUb3/34BbECKVbVwrT9bO6lILWln237z7sLaWnMsTi7Yc9fhX5DLpkJzAGfaReXI22wP41SZmnAA3Vw==", "_location": "/@babel/plugin-transform-parameters", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-parameters@^7.23.3", "name": "@babel/plugin-transform-parameters", "escapedName": "@babel%2fplugin-transform-parameters", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/plugin-transform-object-rest-spread", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.23.3.tgz", "_shasum": "83ef5d1baf4b1072fa6e54b2b0999a7b2527e2af", "_spec": "@babel/plugin-transform-parameters@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Compile ES2015 default and rest parameters to ES5", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-parameters", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-parameters", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-parameters"}, "type": "commonjs", "version": "7.23.3"}