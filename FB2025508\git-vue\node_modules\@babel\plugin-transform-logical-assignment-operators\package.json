{"_from": "@babel/plugin-transform-logical-assignment-operators@^7.23.4", "_id": "@babel/plugin-transform-logical-assignment-operators@7.23.4", "_inBundle": false, "_integrity": "sha512-Mc/ALf1rmZTP4JKKEhUwiORU+vcfarFVLfcFiolKUo6sewoxSEgl36ak5t+4WamRsNr6nzjZXQjM35WsU+9vbg==", "_location": "/@babel/plugin-transform-logical-assignment-operators", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-logical-assignment-operators@^7.23.4", "name": "@babel/plugin-transform-logical-assignment-operators", "escapedName": "@babel%2fplugin-transform-logical-assignment-operators", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.23.4.tgz", "_shasum": "e599f82c51d55fac725f62ce55d3a0886279ecb5", "_spec": "@babel/plugin-transform-logical-assignment-operators@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "deprecated": false, "description": "Transforms logical assignment operators into short-circuited assignments", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-logical-assignment-operators", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-logical-assignment-operators", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-logical-assignment-operators"}, "type": "commonjs", "version": "7.23.4"}