<template>
	<view class="container">
		<!-- 顶部标题 -->
		<view class="header">
			<view class="title">服务项目统计</view>
		</view>
		
		<!-- 当期订单统计 -->
		<view class="stats-section">
			<!-- 今日订单 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">今日订单量（{{ formatDate('today') }}）</text>
				</view>
				<view class="card-body">
					<view class="stat-row total-row">
						<text class="stat-label">订单总数：</text>
						<text class="stat-value">{{ statistics.today && statistics.today.orderCount || 0 }}</text>
					</view>
					<!-- 服务项目明细 -->
					<view class="service-items">
						<view class="service-item" v-for="(item, index) in statistics.today && statistics.today.serviceItems || []" :key="index">
							<text class="service-name">{{ item.service_name }}</text>
							<text class="service-count">{{ item.count }}</text>
						</view>
					</view>
					<view class="stat-row income-row">
						<text class="stat-label">总收入：</text>
						<text class="stat-value highlight">¥{{ formatNumber(statistics.today && statistics.today.income || 0) }}</text>
					</view>
				</view>
			</view>
			
			<!-- 本周订单 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">本周订单量（{{ formatDate('week') }}）</text>
				</view>
				<view class="card-body">
					<view class="stat-row total-row">
						<text class="stat-label">订单总数：</text>
						<text class="stat-value">{{ statistics.week && statistics.week.orderCount || 0 }}</text>
					</view>
					<!-- 服务项目明细 -->
					<view class="service-items">
						<view class="service-item" v-for="(item, index) in statistics.week && statistics.week.serviceItems || []" :key="index">
							<text class="service-name">{{ item.service_name }}</text>
							<text class="service-count">{{ item.count }}</text>
						</view>
					</view>
					<view class="stat-row income-row">
						<text class="stat-label">总收入：</text>
						<text class="stat-value highlight">¥{{ formatNumber(statistics.week && statistics.week.income || 0) }}</text>
					</view>
				</view>
			</view>
			
			<!-- 本月订单 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">本月订单量（{{ formatDate('month') }}）</text>
				</view>
				<view class="card-body">
					<view class="stat-row total-row">
						<text class="stat-label">订单总数：</text>
						<text class="stat-value">{{ statistics.month && statistics.month.orderCount || 0 }}</text>
					</view>
					<!-- 服务项目明细 -->
					<view class="service-items">
						<view class="service-item" v-for="(item, index) in statistics.month && statistics.month.serviceItems || []" :key="index">
							<text class="service-name">{{ item.service_name }}</text>
							<text class="service-count">{{ item.count }}</text>
						</view>
					</view>
					<view class="stat-row income-row">
						<text class="stat-label">总收入：</text>
						<text class="stat-value highlight">¥{{ formatNumber(statistics.month && statistics.month.income || 0) }}</text>
					</view>
				</view>
			</view>
			
			<!-- 本年订单 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">本年订单量（{{ formatDate('year') }}）</text>
				</view>
				<view class="card-body">
					<view class="stat-row total-row">
						<text class="stat-label">订单总数：</text>
						<text class="stat-value">{{ statistics.year && statistics.year.orderCount || 0 }}</text>
					</view>
					<!-- 服务项目明细 -->
					<view class="service-items">
						<view class="service-item" v-for="(item, index) in statistics.year && statistics.year.serviceItems || []" :key="index">
							<text class="service-name">{{ item.service_name }}</text>
							<text class="service-count">{{ item.count }}</text>
						</view>
					</view>
					<view class="stat-row income-row">
						<text class="stat-label">总收入：</text>
						<text class="stat-value highlight">¥{{ formatNumber(statistics.year && statistics.year.income || 0) }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 历史年度数据 -->
		<view class="history-section">
			<view class="section-title">历史年度统计</view>
			
			<view class="history-list">
				<view v-for="(item, index) in displayedYearlyStats" :key="index" class="history-item">
					<view class="history-header">
						<text class="history-year">{{ item.year }}年订单量</text>
					</view>
					<view class="history-body">
						<view class="stat-row total-row">
							<text class="stat-label">订单总数：</text>
							<text class="stat-value">{{ item.orderCount || 0 }}</text>
						</view>
						<!-- 服务项目明细 -->
						<view class="service-items">
							<view class="service-item" v-for="(service, sIndex) in item.serviceItems || []" :key="sIndex">
								<text class="service-name">{{ service.service_name }}</text>
								<text class="service-count">{{ service.count }}</text>
							</view>
						</view>
						<view class="stat-row income-row">
							<text class="stat-label">总收入：</text>
							<text class="stat-value highlight">¥{{ formatNumber(item.income || 0) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 显示更多按钮 -->
			<view v-if="hasMoreYears" class="show-more-btn" @click="showMoreYears">
				显示更多
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
	import { getStaffOrderStats } from '@/api/work/usertotal.js'
	
	export default {
		data() {
			return {
				loading: false,
				statistics: {
					today: {},
					week: {},
					month: {},
					year: {}
				},
				yearlyStats: [],
				currentPage: 1,
				pageSize: 3
			}
		},
		computed: {
			displayedYearlyStats() {
				const end = this.currentPage * this.pageSize;
				return this.yearlyStats.slice(0, end);
			},
			hasMoreYears() {
				return this.yearlyStats.length > this.currentPage * this.pageSize;
			}
		},
		onLoad() {
			this.loadStatistics();
		},
		methods: {
			loadStatistics() {
				this.loading = true;
				
				getStaffOrderStats()
					.then(res => {
						if (res.code === 200) {
							this.statistics = {
								today: res.data.today || {},
								week: res.data.week || {},
								month: res.data.month || {},
								year: res.data.year || {}
							};
							this.yearlyStats = res.data.yearlyStats || [];
						} else {
							uni.showToast({
								title: '获取统计数据失败',
								icon: 'none'
							});
						}
						this.loading = false;
					})
					.catch(err => {
						console.error('获取统计数据出错', err);
						uni.showToast({
							title: '获取统计数据出错',
							icon: 'none'
						});
						this.loading = false;
					});
			},
			
			formatNumber(num) {
				num = parseFloat(num);
				return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
			},
			
			formatDate(period) {
				const now = new Date();
				const year = now.getFullYear();
				const month = now.getMonth() + 1;
				const day = now.getDate();
				
				switch (period) {
					case 'today':
						return `${year}-${month}-${day}`;
					case 'week':
						return `${year}-Week${this.getWeekNumber(now)}`;
					case 'month':
						return `${year}-${month}`;
					case 'year':
						return `${year}`;
					default:
						return '';
				}
			},
			
			getWeekNumber(date) {
				// 获取当前是一年中的第几周
				const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
				const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
				return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
			},
			
			showMoreYears() {
				this.currentPage += 1;
			}
		}
	}
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.stats-section {
		margin-bottom: 30rpx;
	}
	
	.stats-card {
		background-color: #007aff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		color: #fff;
	}
	
	.card-header {
		padding-bottom: 15rpx;
		border-bottom: 1px solid rgba(255, 255, 255, 0.2);
		margin-bottom: 15rpx;
	}
	
	.card-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #fff;
	}
	
	.card-body {
		padding: 10rpx 0;
	}
	
	.stat-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
		font-size: 28rpx;
	}
	
	.total-row {
		margin-bottom: 15rpx;
	}
	
	.income-row {
		margin-top: 15rpx;
		border-top: 1px solid rgba(255, 255, 255, 0.2);
		padding-top: 15rpx;
	}
	
	.stat-label {
		color: rgba(255, 255, 255, 0.9);
	}
	
	.stat-value {
		color: #fff;
		font-weight: bold;
	}
	
	.highlight {
		color: #ffff00;
	}
	
	.service-items {
		margin-left: 20rpx;
	}
	
	.service-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8rpx;
		font-size: 26rpx;
	}
	
	.service-name {
		color: rgba(255, 255, 255, 0.9);
	}
	
	.service-count {
		color: #fff;
		font-weight: bold;
	}
	
	.history-section {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		padding-bottom: 15rpx;
		border-bottom: 1px solid #eee;
	}
	
	.history-list {
		margin-bottom: 20rpx;
	}
	
	.history-item {
		padding: 20rpx;
		border-bottom: 1px dashed #eee;
		margin-bottom: 15rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
	}
	
	.history-header {
		margin-bottom: 15rpx;
	}
	
	.history-year {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	
	.history-body .stat-row {
		color: #333;
	}
	
	.history-body .stat-label {
		color: #666;
	}
	
	.history-body .stat-value {
		color: #333;
	}
	
	.history-body .highlight {
		color: #ff7f32;
	}
	
	.history-body .service-name {
		color: #666;
	}
	
	.history-body .service-count {
		color: #333;
	}
	
	.show-more-btn {
		text-align: center;
		padding: 20rpx 0;
		font-size: 28rpx;
		color: #ff7f32;
	}
	
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;
	}
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid rgba(255, 127, 50, 0.2);
		border-top-color: #ff7f32;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	.loading-text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #999;
	}
	
	@keyframes spin {
		to {
			transform: rotate(360deg);
		}
	}
</style>
