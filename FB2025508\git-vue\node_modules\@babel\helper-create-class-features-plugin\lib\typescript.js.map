{"version": 3, "names": ["assertFieldTransformed", "path", "node", "declare", "buildCodeFrameError"], "sources": ["../src/typescript.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nexport function assertFieldTransformed(\n  path: NodePath<t.ClassProperty | t.ClassDeclaration>,\n) {\n  if (\n    path.node.declare ||\n    (process.env.BABEL_8_BREAKING\n      ? path.isClassProperty({ definite: true })\n      : false)\n  ) {\n    throw path.buildCodeFrameError(\n      `TypeScript 'declare' fields must first be transformed by ` +\n        `@babel/plugin-transform-typescript.\\n` +\n        `If you have already enabled that plugin (or '@babel/preset-typescript'), make sure ` +\n        `that it runs before any plugin related to additional class features:\\n` +\n        ` - @babel/plugin-transform-class-properties\\n` +\n        ` - @babel/plugin-transform-private-methods\\n` +\n        ` - @babel/plugin-proposal-decorators`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAGO,SAASA,sBAAsBA,CACpCC,IAAoD,EACpD;EACA,IACEA,IAAI,CAACC,IAAI,CAACC,OAAO,IAGb,KAAM,EACV;IACA,MAAMF,IAAI,CAACG,mBAAmB,CAC3B,2DAA0D,GACxD,uCAAsC,GACtC,qFAAoF,GACpF,wEAAuE,GACvE,+CAA8C,GAC9C,8CAA6C,GAC7C,sCACL,CAAC;EACH;AACF"}