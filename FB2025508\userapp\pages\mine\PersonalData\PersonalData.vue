<template>
	<view class="personal-data-container">
		<view class="page-header">
			<view class="header-title">个人资料</view>
		</view>
		
		<view class="form-content">
			<!-- 头像 -->
<!-- 			<view class="form-item">
				<view class="form-label">头像</view>
				<view class="avatar-wrapper">
					<image class="avatar" src="/static/images/default-avatar.png" mode="aspectFill"></image>
				</view>
			</view> -->
			
			<!-- 昵称 -->
			<view class="form-item">
				<view class="form-label">昵称</view>
				<input class="form-input" type="text" v-model="formData.nickname" placeholder="请输入昵称" />
			</view>
			
			<!-- 手机号 -->
			<view class="form-item">
				<view class="form-label">手机号</view>
				<input class="form-input" type="text" v-model="formData.phone" placeholder="请输入手机号" />
				<view class="form-tip">仅下方可见</view>
			</view>
			
			<!-- 姓名 -->
			<view class="form-item">
				<view class="form-label">姓名<text class="required">*</text></view>
				<input class="form-input" type="text" v-model="formData.staffName" placeholder="请输入姓名" />
			</view>
			
			<!-- 性别 -->
			<view class="form-item">
				<view class="form-label">性别</view>
				<picker @change="bindGenderChange" :value="genderIndex" :range="genders" range-key="label">
					<view class="picker-view">
						<text>{{genders[genderIndex].label}}</text>
						<text class="picker-arrow">></text>
					</view>
				</picker>
			</view>
			
			<!-- 年龄 -->
			<view class="form-item">
				<view class="form-label">年龄<text class="required">*</text></view>
				<view class="age-display">{{formData.age || 0}}</view>
			</view>
			
			<!-- 出生年月 -->
			<view class="form-item">
				<view class="form-label">出生年月<text class="required">*</text></view>
				<picker mode="date" :start="startDate" :end="endDate" @change="bindBirthDateChange" :value="formData.birthTime" fields="month">
					<view class="picker-view">
						<text>{{formData.birthTime || '请选择出生年月'}}</text>
						<text class="picker-arrow">></text>
					</view>
				</picker>
			</view>
			
			<!-- 身份证号 -->
			<view class="form-item">
				<view class="form-label">身份证号<text class="required">*</text></view>
				<input class="form-input" type="idcard" v-model="formData.idCard" placeholder="请输入身份证号" />
			</view>
			
			<!-- 所在省市 -->
			<view class="form-item">
				<view class="form-label">所在省市</view>
				<view class="region-picker">
					<!-- 省份选择 -->
					<view class="region-box">
						<picker @change="bindProvinceChange" :value="regionIndex.province" :range="regionData.provinces" range-key="regioninfo_name">
							<view class="region-item">
								<text>{{selectedRegion.province || '省'}}</text>
								<view class="arrow-down"></view>
							</view>
						</picker>
					</view>
					
					<!-- 城市选择 -->
					<view class="region-box">
						<picker @change="bindCityChange" :value="regionIndex.city" :range="regionData.cities" range-key="regioninfo_name" :disabled="!selectedRegion.province">
							<view class="region-item" :class="{'region-disabled': !selectedRegion.province}">
								<text>{{selectedRegion.city || '市'}}</text>
								<view class="arrow-down"></view>
							</view>
						</picker>
					</view>
					
					<!-- 区县选择 -->
					<view class="region-box">
						<picker @change="bindDistrictChange" :value="regionIndex.district" :range="regionData.districts" range-key="regioninfo_name" :disabled="!selectedRegion.city">
							<view class="region-item" :class="{'region-disabled': !selectedRegion.city}">
								<text>{{selectedRegion.district || '区/县'}}</text>
								<view class="arrow-down"></view>
							</view>
						</picker>
					</view>
				</view>
			</view>
			
			<!-- 保存按钮 -->
			<view class="btn-container">
				<button class="save-btn" @click="savePersonalData">保存</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { getStaffPersonalData, updateStaffPersonalData, getProvinces, getCities, getDistricts, getRegionPath } from '@/api/work/usertotal'
	
	export default {
		data() {
			const currentYear = new Date().getFullYear()
			return {
				formData: {
					staffId: null,
					staffName: '',
					nickname: '',
					gender: '0',
					phone: '',
					idCard: '',
					birthTime: '',
					age: 0,
					regionId: null
				},
				genders: [
					{ value: '0', label: '男' },
					{ value: '1', label: '女' },
					{ value: '2', label: '未知' }
				],
				genderIndex: 0,
				startDate: '1980-01',
				endDate: `${currentYear}-12`,
				regionData: {
					provinces: [],
					cities: [],
					districts: []
				},
				regionIndex: {
					province: 0,
					city: 0,
					district: 0
				},
				selectedRegion: {
					province: '',
					city: '',
					district: ''
				},
				selectedRegionId: {
					province: null,
					city: null,
					district: null
				}
			}
		},
		onLoad() {
			// 先加载省份数据
			this.loadProvinces()
			// 再加载个人数据（包含区域ID）
			this.loadStaffData()
		},
		methods: {
			// 加载店员个人资料
			async loadStaffData() {
				try {
					uni.showLoading({
						title: '加载中...'
					})
					
					const res = await getStaffPersonalData()
					
					uni.hideLoading()
					
					if (res.code === 200 && res.data) {
						this.formData = {
							staffId: res.data.staff_id,
							staffName: res.data.staff_name || '',
							nickname: res.data.nickname || '',
							gender: res.data.gender || '0',
							phone: res.data.phone || '',
							idCard: res.data.id_card || '',
							birthTime: this.formatDate(res.data.birth_time),
							age: res.data.age || 0,
							regionId: res.data.region_id
						}
						
						// 设置性别索引
						this.genderIndex = this.genders.findIndex(item => item.value === this.formData.gender)
						if (this.genderIndex === -1) this.genderIndex = 0
						
						// 如果有区域ID，加载完整的省市区信息
						if (this.formData.regionId) {
							// 重置省市区数据，避免数据混淆
							this.selectedRegion = {
								province: '',
								city: '',
								district: ''
							}
							this.selectedRegionId = {
								province: null,
								city: null,
								district: null
							}
							
							this.loadRegionPath(this.formData.regionId)
						}
					} else {
						uni.showToast({
							title: '获取个人资料失败',
							icon: 'none'
						})
					}
				} catch (error) {
					uni.hideLoading()
					console.error('获取店员个人资料失败:', error)
					uni.showToast({
						title: '获取资料失败，请稍后重试',
						icon: 'none'
					})
				}
			},
			
			// 加载省份列表
			async loadProvinces() {
				try {
					const res = await getProvinces()
					
					if (res.code === 200 && res.data) {
						this.regionData.provinces = res.data
					}
				} catch (error) {
					console.error('获取省份列表失败:', error)
				}
			},
			
			// 加载城市列表
			async loadCities(provinceId) {
				try {
					const res = await getCities(provinceId)
					
					if (res.code === 200 && res.data) {
						this.regionData.cities = res.data
						
						// 只有在没有选择城市的情况下才默认选择第一个
						if (!this.selectedRegion.city) {
							this.regionIndex.city = 0
							this.selectedRegion.city = this.regionData.cities[0]?.regioninfo_name || ''
							this.selectedRegionId.city = this.regionData.cities[0]?.regioninfo_id || null
							
							// 清空区县数据
							this.regionData.districts = []
							this.regionIndex.district = 0
							this.selectedRegion.district = ''
							this.selectedRegionId.district = null
							
							// 加载区县列表
							if (this.selectedRegionId.city) {
								this.loadDistricts(this.selectedRegionId.city)
							}
						}
					}
				} catch (error) {
					console.error('获取城市列表失败:', error)
				}
			},
			
			// 加载区县列表
			async loadDistricts(cityId) {
				try {
					const res = await getDistricts(cityId)
					
					if (res.code === 200 && res.data) {
						this.regionData.districts = res.data
						
						// 只有在没有选择区县的情况下才默认选择第一个
						if (!this.selectedRegion.district) {
							this.regionIndex.district = 0
							this.selectedRegion.district = this.regionData.districts[0]?.regioninfo_name || ''
							this.selectedRegionId.district = this.regionData.districts[0]?.regioninfo_id || null
							
							// 更新区域ID
							this.formData.regionId = this.selectedRegionId.district
						}
					}
				} catch (error) {
					console.error('获取区县列表失败:', error)
				}
			},
			
			// 根据区域ID加载完整的省市区路径
			async loadRegionPath(regionId) {
				try {
					const res = await getRegionPath(regionId)
					
					if (res.code === 200 && res.data) {
						// 先加载省份
						if (res.data.province) {
							this.selectedRegion.province = res.data.province.regioninfo_name
							this.selectedRegionId.province = res.data.province.regioninfo_id
							
							// 查找省份索引
							this.regionIndex.province = this.regionData.provinces.findIndex(
								item => item.regioninfo_id === this.selectedRegionId.province
							)
							if (this.regionIndex.province === -1) {
								this.regionIndex.province = 0
							}
							
							// 先加载城市列表
							await this.loadCities(this.selectedRegionId.province)
						}
						
						// 再设置城市
						if (res.data.city) {
							this.selectedRegion.city = res.data.city.regioninfo_name
							this.selectedRegionId.city = res.data.city.regioninfo_id
							
							// 查找城市索引
							this.regionIndex.city = this.regionData.cities.findIndex(
								item => item.regioninfo_id === this.selectedRegionId.city
							)
							if (this.regionIndex.city === -1) {
								this.regionIndex.city = 0
							}
							
							// 加载区县列表
							await this.loadDistricts(this.selectedRegionId.city)
						}
						
						// 最后设置区县
						if (res.data.district) {
							this.selectedRegion.district = res.data.district.regioninfo_name
							this.selectedRegionId.district = res.data.district.regioninfo_id
							
							// 查找区县索引
							this.regionIndex.district = this.regionData.districts.findIndex(
								item => item.regioninfo_id === this.selectedRegionId.district
							)
							if (this.regionIndex.district === -1) {
								this.regionIndex.district = 0
							}
							
							// 更新区域ID
							this.formData.regionId = this.selectedRegionId.district
						}
					}
				} catch (error) {
					console.error('获取区域路径失败:', error)
				}
			},
			
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return ''
				
				const date = new Date(dateStr)
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				
				return `${year}-${month}`
			},
			
			// 计算年龄
			calculateAge(birthDateStr) {
				if (!birthDateStr) return 0
				
				const birthDate = new Date(birthDateStr)
				const currentDate = new Date()
				
				let age = currentDate.getFullYear() - birthDate.getFullYear()
				
				// 如果生日还没过，年龄减1
				if (
					currentDate.getMonth() < birthDate.getMonth() || 
					(currentDate.getMonth() === birthDate.getMonth() && 
					currentDate.getDate() < birthDate.getDate())
				) {
					age--
				}
				
				return age
			},
			
			// 性别选择器变更
			bindGenderChange(e) {
				this.genderIndex = e.detail.value
				this.formData.gender = this.genders[this.genderIndex].value
			},
			
			// 出生年月选择器变更
			bindBirthDateChange(e) {
				this.formData.birthTime = e.detail.value
				// 计算年龄
				this.formData.age = this.calculateAge(`${this.formData.birthTime}-01`)
			},
			
			// 省份选择器变更
			bindProvinceChange(e) {
				const index = e.detail.value
				const selectedProvince = this.regionData.provinces[index]
				
				this.regionIndex.province = index
				this.selectedRegion.province = selectedProvince.regioninfo_name
				this.selectedRegionId.province = selectedProvince.regioninfo_id
				
				// 重置市和区
				this.selectedRegion.city = ''
				this.selectedRegionId.city = null
				this.regionData.cities = []
				this.selectedRegion.district = ''
				this.selectedRegionId.district = null
				this.regionData.districts = []
				
				// 加载城市列表
				this.loadCities(selectedProvince.regioninfo_id)
			},
			
			// 城市选择器变更
			bindCityChange(e) {
				const index = e.detail.value
				const selectedCity = this.regionData.cities[index]
				
				this.regionIndex.city = index
				this.selectedRegion.city = selectedCity.regioninfo_name
				this.selectedRegionId.city = selectedCity.regioninfo_id
				
				// 重置区
				this.selectedRegion.district = ''
				this.selectedRegionId.district = null
				this.regionData.districts = []
				
				// 加载区县列表
				this.loadDistricts(selectedCity.regioninfo_id)
			},
			
			// 区县选择器变更
			bindDistrictChange(e) {
				const index = e.detail.value
				const selectedDistrict = this.regionData.districts[index]
				
				this.regionIndex.district = index
				this.selectedRegion.district = selectedDistrict.regioninfo_name
				this.selectedRegionId.district = selectedDistrict.regioninfo_id
				
				// 更新区域ID
				this.formData.regionId = selectedDistrict.regioninfo_id
			},
			
			// 表单验证
			validateForm() {
				if (!this.formData.staffName) {
					uni.showToast({
						title: '请输入姓名',
						icon: 'none'
					})
					return false
				}
				
				if (!this.formData.birthTime) {
					uni.showToast({
						title: '请选择出生年月',
						icon: 'none'
					})
					return false
				}
				
				if (!this.formData.idCard) {
					uni.showToast({
						title: '请输入身份证号',
						icon: 'none'
					})
					return false
				}
				
				// 身份证号格式验证（简单验证18位或15位）
				const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
				if (!idCardReg.test(this.formData.idCard)) {
					uni.showToast({
						title: '请输入正确的身份证号',
						icon: 'none'
					})
					return false
				}
				
				return true
			},
			
			// 保存个人资料
			async savePersonalData() {
				if (!this.validateForm()) return
				
				try {
					uni.showLoading({
						title: '保存中...'
					})
					
					const res = await updateStaffPersonalData({
						staffName: this.formData.staffName,
						nickname: this.formData.nickname,
						gender: this.formData.gender,
						phone: this.formData.phone,
						idCard: this.formData.idCard,
						birthTime: `${this.formData.birthTime}-01`, // 添加日期
						regionId: this.formData.regionId
					})
					
					uni.hideLoading()
					
					if (res.code === 200) {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						})
						
						// 重新加载数据
						this.loadStaffData()
					} else {
						uni.showToast({
							title: res.msg || '保存失败',
							icon: 'none'
						})
					}
				} catch (error) {
					uni.hideLoading()
					console.error('保存个人资料失败:', error)
					uni.showToast({
						title: '保存失败，请稍后重试',
						icon: 'none'
					})
				}
			}
		}
	}
</script>

<style>
	.personal-data-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.page-header {
		padding: 20rpx 0;
	}
	
	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.form-content {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
	}
	
	.form-item {
		padding: 20rpx 0;
		border-bottom: 1px solid #eee;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.required {
		color: #ff4d4f;
		margin-left: 4rpx;
	}
	
	.form-input {
		width: 100%;
		height: 80rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.form-tip {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
	}
	
	.avatar-wrapper {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
	}
	
	.avatar {
		width: 150rpx;
		height: 150rpx;
		border-radius: 75rpx;
	}
	
	.picker-view {
		height: 80rpx;
		line-height: 80rpx;
		display: flex;
		justify-content: space-between;
		font-size: 28rpx;
		color: #333;
	}
	
	.picker-arrow {
		color: #999;
		transform: rotate(90deg);
	}
	
	.age-display {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.region-picker {
		display: flex;
		justify-content: space-between;
	}
	
	.region-box {
		flex: 1;
		margin: 0 5rpx;
	}
	
	.region-item {
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
		background-color: #fff;
	}
	
	.region-disabled {
		background-color: #f5f5f5;
		color: #999;
	}
	
	.arrow-down {
		width: 0;
		height: 0;
		border-left: 10rpx solid transparent;
		border-right: 10rpx solid transparent;
		border-top: 10rpx solid #999;
	}
	
	.region-disabled .arrow-down {
		border-top-color: #ccc;
	}
	
	.btn-container {
		margin-top: 60rpx;
		padding: 20rpx 0;
	}
	
	.save-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		background-color: #007aff;
		color: #fff;
		font-size: 32rpx;
		border-radius: 45rpx;
	}
</style>
