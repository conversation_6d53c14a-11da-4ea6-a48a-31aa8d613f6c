{"_from": "@babel/plugin-transform-dynamic-import@^7.23.4", "_id": "@babel/plugin-transform-dynamic-import@7.23.4", "_inBundle": false, "_integrity": "sha512-V6jIbLhdJK86MaLh4Jpghi8ho5fGzt3imHOBu/x0jlBaPYqDoWz4RDXjmMOfnh+JWNaQleEAByZLV0QzBT4YQQ==", "_location": "/@babel/plugin-transform-dynamic-import", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-dynamic-import@^7.23.4", "name": "@babel/plugin-transform-dynamic-import", "escapedName": "@babel%2fplugin-transform-dynamic-import", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.23.4.tgz", "_shasum": "c7629e7254011ac3630d47d7f34ddd40ca535143", "_spec": "@babel/plugin-transform-dynamic-import@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "deprecated": false, "description": "Transform import() expressions", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-dynamic-import", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-dynamic-import"}, "type": "commonjs", "version": "7.23.4"}