{"_from": "lru-cache@^5.1.1", "_id": "lru-cache@5.1.1", "_inBundle": false, "_integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "_location": "/@babel/helper-compilation-targets/lru-cache", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lru-cache@^5.1.1", "name": "lru-cache", "escapedName": "lru-cache", "rawSpec": "^5.1.1", "saveSpec": null, "fetchSpec": "^5.1.1"}, "_requiredBy": ["/@babel/helper-compilation-targets"], "_resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "_shasum": "1da27e6710271947695daf6848e847f01d84b920", "_spec": "lru-cache@^5.1.1", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-compilation-targets", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "bundleDependencies": false, "dependencies": {"yallist": "^3.0.2"}, "deprecated": false, "description": "A cache object that deletes the least-recently-used items.", "devDependencies": {"benchmark": "^2.1.4", "tap": "^12.1.0"}, "files": ["index.js"], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "keywords": ["mru", "lru", "cache"], "license": "ISC", "main": "index.js", "name": "lru-cache", "repository": {"type": "git", "url": "git://github.com/isaacs/node-lru-cache.git"}, "scripts": {"coveragerport": "tap --coverage-report=html", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J"}, "version": "5.1.1"}