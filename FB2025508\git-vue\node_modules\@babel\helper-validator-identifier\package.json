{"_from": "@babel/helper-validator-identifier@^7.22.20", "_id": "@babel/helper-validator-identifier@7.22.20", "_inBundle": false, "_integrity": "sha512-Y4O<PERSON>+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==", "_location": "/@babel/helper-validator-identifier", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-validator-identifier@^7.22.20", "name": "@babel/helper-validator-identifier", "escapedName": "@babel%2fhelper-validator-identifier", "scope": "@babel", "rawSpec": "^7.22.20", "saveSpec": null, "fetchSpec": "^7.22.20"}, "_requiredBy": ["/@babel/helper-module-transforms", "/@babel/highlight", "/@babel/plugin-transform-modules-systemjs", "/@babel/types"], "_resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz", "_shasum": "c4ae002c61d2879e724581d96665583dbc1dc0e0", "_spec": "@babel/helper-validator-identifier@^7.22.20", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\highlight", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Validate identifier/keywords name", "devDependencies": {"@unicode/unicode-15.1.0": "^1.5.2", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-validator-identifier", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-identifier"}, "type": "commonjs", "version": "7.22.20"}