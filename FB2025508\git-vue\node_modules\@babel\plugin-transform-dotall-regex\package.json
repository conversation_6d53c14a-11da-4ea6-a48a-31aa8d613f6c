{"_from": "@babel/plugin-transform-dotall-regex@^7.23.3", "_id": "@babel/plugin-transform-dotall-regex@7.23.3", "_inBundle": false, "_integrity": "sha512-vgnFYDHAKzFaTVp+mneDsIEbnJ2Np/9ng9iviHw3P/KVcgONxpNULEW/51Z/BaFojG2GI2GwwXck5uV1+1NOYQ==", "_location": "/@babel/plugin-transform-dotall-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-dotall-regex@^7.23.3", "name": "@babel/plugin-transform-dotall-regex", "escapedName": "@babel%2fplugin-transform-dotall-regex", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.23.3.tgz", "_shasum": "3f7af6054882ede89c378d0cf889b854a993da50", "_spec": "@babel/plugin-transform-dotall-regex@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-dotall-regex", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "type": "commonjs", "version": "7.23.3"}