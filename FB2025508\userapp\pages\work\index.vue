<template>
  <view class="work-container">
    <!-- 如果是店长，显示提示信息 -->
    <view v-if="isManager" class="manager-tip">
      <uni-icons type="info-filled" size="30" color="#007AFF"></uni-icons>
      <text class="tip-text">您是店长身份，无法使用店员端应用。请使用店长端进行登录。</text>
    </view>

    <!-- 非店长用户才显示功能页面 -->
    <view v-else>
      <!-- 轮播图 -->
      <uni-swiper-dot class="uni-swiper-dot-box" :info="data" :current="current" field="content">
        <swiper class="swiper-box" :current="swiperDotIndex" @change="changeSwiper">
          <swiper-item v-for="(item, index) in data" :key="index">
            <view class="swiper-item" @click="clickBannerItem(item)">
              <image :src="item.image" mode="aspectFill" :draggable="false" />
            </view>
          </swiper-item>
        </swiper>
      </uni-swiper-dot>

      <!-- 宫格组件 -->
      <uni-section title="系统管理" type="line"></uni-section>
      <view class="grid-body">
        <uni-grid :column="4" :showBorder="false" @change="changeGrid" >
           <uni-grid-item :index="8" v-show="true">
            <view class="grid-item-box" >
              <uni-icons type="person-filled" size="30"></uni-icons>
              <text class="text">新建订单</text>
            </view>
          </uni-grid-item>
          <uni-grid-item :index="2" v-show="true">
            <view class="grid-item-box" >
              <uni-icons type="staff-filled" size="30"></uni-icons>
              <text class="text">订单中心</text>
            </view>
          </uni-grid-item>
          <uni-grid-item :index="3" v-show="true">
            <view class="grid-item-box" >
              <uni-icons type="staff-filled" size="30"></uni-icons>
              <text class="text">历史订单</text>
            </view>
          </uni-grid-item>
        <uni-grid-item :index="4" v-show="true">
            <view class="grid-item-box" >
              <uni-icons type="staff-filled" size="30"></uni-icons>
              <text class="text">用户列表</text>
            </view>
          </uni-grid-item>
          <!-- <uni-grid-item :index="5" v-show="this.Pendant">
            <view class="grid-item-box">
              <uni-icons type="person-filled" size="30"></uni-icons>
              <text class="text">管理</text>
            </view>
          </uni-grid-item> -->
           <!--  <uni-grid-item :index="4" v-show="this.Shipping">
            <view class="grid-item-box">
              <uni-icons type="person-filled" size="30"></uni-icons>
              <text class="text">挂件核验</text>
            </view>
          </uni-grid-item>
           <uni-grid-item :index="5" v-show="this.Spareparts">
            <view class="grid-item-box">
              <uni-icons type="person-filled" size="30"></uni-icons>
              <text class="text">备品涂装</text>
            </view>
          </uni-grid-item>
            <uni-grid-item :index="6" v-show="this.Assembly">
            <view class="grid-item-box">
              <uni-icons type="person-filled" size="30"></uni-icons>
              <text class="text">备品装配</text>
            </view>
          </uni-grid-item>
          <uni-grid-item :index="7" v-show="this.Defect">
            <view class="grid-item-box">
              <uni-icons type="person-filled" size="30"></uni-icons>
              <text class="text">备品涂装缺陷</text>
            </view>
          </uni-grid-item> -->
        </uni-grid>
      </view>
    </view>
  </view>
</template>

<script>
  import { checkIsManager } from '@/api/work/usertotal.js'
  
  export default {
    data() {
      return {
        current: 0,
        Dismantle: false,
        Spareparts: false,
        Pendant: false,
        Shipping: false,
        Returnitem: false,
        qualityReturnitem: false,
        Assembly: false,
        Defect: false,
        swiperDotIndex: 0,
        isManager: false, // 是否是店长标志
        data: [{
            image: '/static/images/banner/banner.png'
          },
          // {
          //   image: '/static/images/banner/banner02.jpg'
          // },
          // {
          //   image: '/static/images/banner/banner03.jpg'
          // }
        ]
      }
    },
    mounted()
    {
      // 首先检查当前用户是否是店长
      //this.checkUserIsManager();
      
      // 获取当前用户名
      var username = this.$store.state.user.name;
      let PermissionList = [];
      uni.getStorage({
        key: 'userData',
        success: function (res) {
          PermissionList = res.data;
          // console.log('获取数据成功：', username);
        },
        fail: function (error) {
          //console.log('获取数据失败：', error);
        }
      });

      this.Dismantle = true;
      this.Spareparts = true;
      this.Pendant = true;
      this.Shipping = true;
      this.qualityReturnitem = true;
      this.Returnitem = true;
      this.Assembly = true;
      this.Defect = true;
      if(username=='admin')
      {
        this.Dismantle = true;
        this.Spareparts = true;
        this.Pendant = true;
        this.Shipping = true;
        this.qualityReturnitem = true;
        this.Returnitem = true;
        this.Assembly = true;
        this.Defect = true;
      }else
      {
        for (let i = 0; i < PermissionList.length; i++) {
          if(PermissionList[i]=='Dismantle')
          {
            this.Dismantle = true;
            
          }
          if(PermissionList[i]=='Spareparts')
          {
            this.Spareparts = true;
          }
          if(PermissionList[i]=='Pendant')
          {
            this.Pendant = true;
          }
          if(PermissionList[i]=='Shipping')
          {
            this.Shipping = true;
          }
          if(PermissionList[i]=='Returnitem')
          {
            this.Returnitem = true;
          }
          if(PermissionList[i]=='qualityReturnitem')
          {
            this.qualityReturnitem = true;
          }
          if(PermissionList[i]=='Assembly')
          {
            this.Assembly = true;
          
          }
          if(PermissionList[i]=='Defect')
          {
            this.Defect = true;
          
          }
        }
      }
    },
    methods: {
      // 检查当前用户是否是店长
      checkUserIsManager() {
        checkIsManager().then(res => {
          if (res.code === 200) {
            this.isManager = res.data.isManager;
            
            // 如果是店长，跳转到错误页面
            if (this.isManager) {
              // 跳转到错误页面并传递错误信息
              uni.navigateTo({
                url: '/pages/work/error/error?message=您是店长身份或者不被允许登录，无法使用店员端应用。请使用店长端进行登录。'
              });
            }
          } else {
            this.$modal.showToast(res.msg || '获取用户信息失败');
          }
        }).catch(err => {
          //console.error('检查用户身份失败:', err);
        //  this.$modal.showToast(res.msg);
          uni.navigateTo({
                url: '/pages/work/error/error?message=您是店长身份或者不被允许登录，无法使用店员端应用。请使用店长端进行登录。'
              });
        });
      },
      clickBannerItem(item) {
        // 如果是店长，不执行任何操作
        if (this.isManager) return;
        console.info(item);
      },
      changeSwiper(e) {
        this.current = e.detail.current;
      },
      changeGrid(e) {
        // 如果是店长，不执行任何操作
        if (this.isManager) return;
        
        console.log(e);
        switch(e.detail.index){
          case 8:
            this.$tab.navigateTo("/pages/work/NewStart/NewStart");
            break;
          case 2:
            this.$tab.navigateTo("/pages/work/OrderExcute/OrderExcute");
            break;
          case 3:
            this.$tab.navigateTo("/pages/work/OrderDetail/OrderDetail");
            break;
          case 4:
            this.$tab.navigateTo("/pages/work/UserList/UserList");
            break;
          case 5:
            this.$tab.navigateTo("/pages/work/assistantMag/assistantMag");
            break;
          case 6:
            this.$tab.navigateTo("/pages/work/spares_Assemble/index");
            break;
          case 7:
            this.$tab.navigateTo("/pages/work/spares_Painting_Defect/index");
            break;
          case 1:
            this.$tab.navigateTo("/pages/work/UserList/UserList");
            break;
          default:
            this.$modal.showToast('模块建设中~');
            break;
        }
      }
    }
  }
</script>

<style lang="scss">
  /* #ifndef APP-NVUE */
  page {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #fff;
    min-height: 100%;
    height: auto;
  }

  view {
    font-size: 14px;
    line-height: inherit;
  }

  /* #endif */
  
  /* 店长提示样式 */
  .manager-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    background-color: #f8f8f8;
    border-radius: 8px;
    margin: 50px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .tip-text {
    font-size: 16px;
    color: #333;
    margin-top: 20px;
    text-align: center;
    line-height: 1.5;
  }

  .text {
    text-align: center;
    font-size: 26rpx;
    margin-top: 10rpx;
  }

  .grid-item-box {
    flex: 1;
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
  }

  .uni-margin-wrap {
    width: 690rpx;
    width: 100%;
    ;
  }

  .swiper {
    height: 300rpx;
  }

  .swiper-box {
    height: 150px;
  }

  .swiper-item {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    height: 300rpx;
    line-height: 300rpx;
  }

  @media screen and (min-width: 500px) {
    .uni-swiper-dot-box {
      width: 400px;
      /* #ifndef APP-NVUE */
      margin: 0 auto;
      /* #endif */
      margin-top: 8px;
    }

    .image {
      width: 100%;
    }
  }
</style>
