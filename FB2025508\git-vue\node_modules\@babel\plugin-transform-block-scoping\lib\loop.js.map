{"version": 3, "names": ["_core", "require", "collectLoopBodyBindingsVisitor", "Expression|Declaration|Loop", "path", "skip", "<PERSON><PERSON>", "state", "isFunctionParent", "bindings", "scope", "name", "Object", "keys", "binding", "kind", "blockScoped", "push", "getLoopBodyBindings", "loopPath", "traverse", "getUsageInBody", "seen", "WeakSet", "capturedInClosure", "constantViolations", "filterMap", "inBody", "inClosure", "relativeLoopLocation", "id", "isUpdateExpression", "get", "isAssignmentExpression", "add", "node", "references", "referencePaths", "has", "hasConstantViolations", "length", "usages", "concat", "bodyPath", "currPath", "parentPath", "isFunction", "isClass", "isMethod", "Error", "collectCompletionsAndVarsVisitor", "Function", "LabeledStatement", "enter", "labelsStack", "label", "exit", "popped", "pop", "Loop", "_", "labellessContinueTargets", "labellessBreakTargets", "SwitchStatement", "BreakStatement|ContinueStatement", "includes", "isBreakStatement", "breaks<PERSON><PERSON><PERSON><PERSON>", "ReturnStatement", "returns", "VariableDeclaration", "parent", "loopNode", "isVarInLoopHead", "vars", "wrapLoopBody", "captured", "updatedBindingsUsages", "callArgs", "closureParams", "updater", "updatedUsage", "t", "identifier", "innerName", "generateUid", "assignmentExpression", "replaceWith", "fn", "functionExpression", "toBlock", "body", "call", "callExpression", "fnParent", "findParent", "p", "async", "generator", "yieldExpression", "awaitExpression", "updaterNode", "expressionStatement", "sequenceExpression", "<PERSON><PERSON><PERSON>", "insertBefore", "variableDeclaration", "variableDeclarator", "bodyStmts", "varNames", "assign", "decl", "declarations", "getBindingIdentifiers", "init", "replacement", "isForStatement", "isForXStatement", "left", "remove", "pushContainer", "map", "labelNum", "returnNum", "type", "addComment", "returnStatement", "numericLiteral", "cloneNode", "template", "statement", "ast", "completionId", "isVariableDeclaration", "injected", "i", "indexOf", "hasInjected", "arg", "argument", "buildUndefinedNode", "blockStatement", "key", "list", "result", "item", "mapped"], "sources": ["../src/loop.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport type { NodePath, Visitor, Binding } from \"@babel/traverse\";\n\ninterface LoopBodyBindingsState {\n  blockScoped: Binding[];\n}\n\nconst collectLoopBodyBindingsVisitor: Visitor<LoopBodyBindingsState> = {\n  \"Expression|Declaration|Loop\"(path) {\n    path.skip();\n  },\n  Scope(path, state) {\n    if (path.isFunctionParent()) path.skip();\n\n    const { bindings } = path.scope;\n    for (const name of Object.keys(bindings)) {\n      const binding = bindings[name];\n      if (\n        binding.kind === \"let\" ||\n        binding.kind === \"const\" ||\n        binding.kind === \"hoisted\"\n      ) {\n        state.blockScoped.push(binding);\n      }\n    }\n  },\n};\n\nexport function getLoopBodyBindings(loopPath: NodePath<t.Loop>) {\n  const state: LoopBodyBindingsState = { blockScoped: [] };\n  loopPath.traverse(collectLoopBodyBindingsVisitor, state);\n  return state.blockScoped;\n}\n\nexport function getUsageInBody(binding: Binding, loopPath: NodePath<t.Loop>) {\n  // UpdateExpressions are counted both as a reference and a mutation,\n  // so we need to de-duplicate them.\n  const seen = new WeakSet<t.Node>();\n\n  let capturedInClosure = false;\n\n  const constantViolations = filterMap(binding.constantViolations, path => {\n    const { inBody, inClosure } = relativeLoopLocation(path, loopPath);\n    if (!inBody) return null;\n    capturedInClosure ||= inClosure;\n\n    const id = path.isUpdateExpression()\n      ? path.get(\"argument\")\n      : path.isAssignmentExpression()\n        ? path.get(\"left\")\n        : null;\n    if (id) seen.add(id.node);\n    return id as NodePath<t.Identifier> | null;\n  });\n\n  const references = filterMap(binding.referencePaths, path => {\n    if (seen.has(path.node)) return null;\n\n    const { inBody, inClosure } = relativeLoopLocation(path, loopPath);\n    if (!inBody) return null;\n    capturedInClosure ||= inClosure;\n\n    return path as NodePath<t.Identifier>;\n  });\n\n  return {\n    capturedInClosure,\n    hasConstantViolations: constantViolations.length > 0,\n    usages: references.concat(constantViolations),\n  };\n}\n\nfunction relativeLoopLocation(path: NodePath, loopPath: NodePath<t.Loop>) {\n  const bodyPath = loopPath.get(\"body\");\n  let inClosure = false;\n\n  for (let currPath = path; currPath; currPath = currPath.parentPath) {\n    if (currPath.isFunction() || currPath.isClass() || currPath.isMethod()) {\n      inClosure = true;\n    }\n    if (currPath === bodyPath) {\n      return { inBody: true, inClosure };\n    } else if (currPath === loopPath) {\n      return { inBody: false, inClosure };\n    }\n  }\n\n  throw new Error(\n    \"Internal Babel error: path is not in loop. Please report this as a bug.\",\n  );\n}\n\ninterface CompletionsAndVarsState {\n  breaksContinues: NodePath<t.BreakStatement | t.ContinueStatement>[];\n  returns: NodePath<t.ReturnStatement>[];\n  labelsStack: string[];\n  labellessContinueTargets: number;\n  labellessBreakTargets: number;\n\n  vars: NodePath<t.VariableDeclaration>[];\n  loopNode: t.Loop;\n}\n\nconst collectCompletionsAndVarsVisitor: Visitor<CompletionsAndVarsState> = {\n  Function(path) {\n    path.skip();\n  },\n  LabeledStatement: {\n    enter({ node }, state) {\n      state.labelsStack.push(node.label.name);\n    },\n    exit({ node }, state) {\n      const popped = state.labelsStack.pop();\n      if (popped !== node.label.name) {\n        throw new Error(\"Assertion failure. Please report this bug to Babel.\");\n      }\n    },\n  },\n  Loop: {\n    enter(_, state) {\n      state.labellessContinueTargets++;\n      state.labellessBreakTargets++;\n    },\n    exit(_, state) {\n      state.labellessContinueTargets--;\n      state.labellessBreakTargets--;\n    },\n  },\n  SwitchStatement: {\n    enter(_, state) {\n      state.labellessBreakTargets++;\n    },\n    exit(_, state) {\n      state.labellessBreakTargets--;\n    },\n  },\n  \"BreakStatement|ContinueStatement\"(\n    path: NodePath<t.BreakStatement | t.ContinueStatement>,\n    state,\n  ) {\n    const { label } = path.node;\n    if (label) {\n      if (state.labelsStack.includes(label.name)) return;\n    } else if (\n      path.isBreakStatement()\n        ? state.labellessBreakTargets > 0\n        : state.labellessContinueTargets > 0\n    ) {\n      return;\n    }\n    state.breaksContinues.push(path);\n  },\n  ReturnStatement(path, state) {\n    state.returns.push(path);\n  },\n  VariableDeclaration(path, state) {\n    if (path.parent === state.loopNode && isVarInLoopHead(path)) return;\n    if (path.node.kind === \"var\") state.vars.push(path);\n  },\n};\n\nexport function wrapLoopBody(\n  loopPath: NodePath<t.Loop>,\n  captured: string[],\n  updatedBindingsUsages: Map<string, NodePath<t.Identifier>[]>,\n) {\n  const loopNode = loopPath.node;\n  const state: CompletionsAndVarsState = {\n    breaksContinues: [],\n    returns: [],\n    labelsStack: [],\n    labellessBreakTargets: 0,\n    labellessContinueTargets: 0,\n    vars: [],\n    loopNode,\n  };\n  loopPath.traverse(collectCompletionsAndVarsVisitor, state);\n\n  const callArgs = [];\n  const closureParams = [];\n  const updater = [];\n  for (const [name, updatedUsage] of updatedBindingsUsages) {\n    callArgs.push(t.identifier(name));\n\n    const innerName = loopPath.scope.generateUid(name);\n    closureParams.push(t.identifier(innerName));\n    updater.push(\n      t.assignmentExpression(\"=\", t.identifier(name), t.identifier(innerName)),\n    );\n    for (const path of updatedUsage) path.replaceWith(t.identifier(innerName));\n  }\n  for (const name of captured) {\n    if (updatedBindingsUsages.has(name)) continue; // already injected\n    callArgs.push(t.identifier(name));\n    closureParams.push(t.identifier(name));\n  }\n\n  const id = loopPath.scope.generateUid(\"loop\");\n  const fn = t.functionExpression(\n    null,\n    closureParams,\n    t.toBlock(loopNode.body),\n  );\n  let call: t.Expression = t.callExpression(t.identifier(id), callArgs);\n\n  const fnParent = loopPath.findParent(p => p.isFunction());\n  if (fnParent) {\n    const { async, generator } = fnParent.node as t.Function;\n    fn.async = async;\n    fn.generator = generator;\n    if (generator) call = t.yieldExpression(call, true);\n    else if (async) call = t.awaitExpression(call);\n  }\n\n  const updaterNode =\n    updater.length > 0\n      ? t.expressionStatement(t.sequenceExpression(updater))\n      : null;\n  if (updaterNode) fn.body.body.push(updaterNode);\n\n  // NOTE: Calling .insertBefore on the loop path might cause the\n  // loop to be moved in the AST. For example, in\n  //   if (true) for (let x of y) ...\n  // .insertBefore will replace the loop with a block:\n  //   if (true) { var _loop = ...; for (let x of y) ... }\n  // All subsequent operations in this function on the loop node\n  // must not assume that loopPath still represents the loop.\n  // TODO: Consider using a function declaration\n  const [varPath] = loopPath.insertBefore(\n    t.variableDeclaration(\"var\", [t.variableDeclarator(t.identifier(id), fn)]),\n  ) as [NodePath<t.VariableDeclaration>];\n\n  const bodyStmts: t.Statement[] = [];\n\n  const varNames: string[] = [];\n  for (const varPath of state.vars) {\n    const assign = [];\n    for (const decl of varPath.node.declarations) {\n      varNames.push(...Object.keys(t.getBindingIdentifiers(decl.id)));\n      if (decl.init) {\n        assign.push(t.assignmentExpression(\"=\", decl.id, decl.init));\n      }\n    }\n    if (assign.length > 0) {\n      let replacement: t.Node =\n        assign.length === 1 ? assign[0] : t.sequenceExpression(assign);\n      if (\n        !t.isForStatement(varPath.parent, { init: varPath.node }) &&\n        !t.isForXStatement(varPath.parent, { left: varPath.node })\n      ) {\n        replacement = t.expressionStatement(replacement);\n      }\n      varPath.replaceWith(replacement);\n    } else {\n      varPath.remove();\n    }\n  }\n  if (varNames.length) {\n    varPath.pushContainer(\n      \"declarations\",\n      varNames.map(name => t.variableDeclarator(t.identifier(name))),\n    );\n  }\n\n  const labelNum = state.breaksContinues.length;\n  const returnNum = state.returns.length;\n  if (labelNum + returnNum === 0) {\n    bodyStmts.push(t.expressionStatement(call));\n  } else if (labelNum === 1 && returnNum === 0) {\n    for (const path of state.breaksContinues) {\n      const { node } = path;\n      const { type, label } = node;\n      let name = type === \"BreakStatement\" ? \"break\" : \"continue\";\n      if (label) name += \" \" + label.name;\n      path.replaceWith(\n        t.addComment(\n          t.returnStatement(t.numericLiteral(1)),\n          \"trailing\",\n          \" \" + name,\n          true,\n        ),\n      );\n      if (updaterNode) path.insertBefore(t.cloneNode(updaterNode));\n\n      bodyStmts.push(\n        template.statement.ast`\n        if (${call}) ${node}\n      `,\n      );\n    }\n  } else {\n    const completionId = loopPath.scope.generateUid(\"ret\");\n\n    if (varPath.isVariableDeclaration()) {\n      varPath.pushContainer(\"declarations\", [\n        t.variableDeclarator(t.identifier(completionId)),\n      ]);\n      bodyStmts.push(\n        t.expressionStatement(\n          t.assignmentExpression(\"=\", t.identifier(completionId), call),\n        ),\n      );\n    } else {\n      bodyStmts.push(\n        t.variableDeclaration(\"var\", [\n          t.variableDeclarator(t.identifier(completionId), call),\n        ]),\n      );\n    }\n\n    const injected: string[] = [];\n    for (const path of state.breaksContinues) {\n      const { node } = path;\n      const { type, label } = node;\n      let name = type === \"BreakStatement\" ? \"break\" : \"continue\";\n      if (label) name += \" \" + label.name;\n\n      let i = injected.indexOf(name);\n      const hasInjected = i !== -1;\n      if (!hasInjected) {\n        injected.push(name);\n        i = injected.length - 1;\n      }\n\n      path.replaceWith(\n        t.addComment(\n          t.returnStatement(t.numericLiteral(i)),\n          \"trailing\",\n          \" \" + name,\n          true,\n        ),\n      );\n      if (updaterNode) path.insertBefore(t.cloneNode(updaterNode));\n\n      if (hasInjected) continue;\n\n      bodyStmts.push(\n        template.statement.ast`\n        if (${t.identifier(completionId)} === ${t.numericLiteral(i)}) ${node}\n      `,\n      );\n    }\n\n    if (returnNum) {\n      for (const path of state.returns) {\n        const arg = path.node.argument || path.scope.buildUndefinedNode();\n        path.replaceWith(\n          template.statement.ast`\n          return { v: ${arg} };\n        `,\n        );\n      }\n\n      bodyStmts.push(\n        template.statement.ast`\n          if (${t.identifier(completionId)}) return ${t.identifier(\n            completionId,\n          )}.v;\n        `,\n      );\n    }\n  }\n\n  loopNode.body = t.blockStatement(bodyStmts);\n\n  return varPath;\n}\n\nexport function isVarInLoopHead(path: NodePath<t.VariableDeclaration>) {\n  if (t.isForStatement(path.parent)) return path.key === \"init\";\n  if (t.isForXStatement(path.parent)) return path.key === \"left\";\n  return false;\n}\n\nfunction filterMap<T, U extends object>(list: T[], fn: (item: T) => U | null) {\n  const result: U[] = [];\n  for (const item of list) {\n    const mapped = fn(item);\n    if (mapped) result.push(mapped);\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAOA,MAAMC,8BAA8D,GAAG;EACrE,6BAA6BC,CAACC,IAAI,EAAE;IAClCA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,KAAKA,CAACF,IAAI,EAAEG,KAAK,EAAE;IACjB,IAAIH,IAAI,CAACI,gBAAgB,CAAC,CAAC,EAAEJ,IAAI,CAACC,IAAI,CAAC,CAAC;IAExC,MAAM;MAAEI;IAAS,CAAC,GAAGL,IAAI,CAACM,KAAK;IAC/B,KAAK,MAAMC,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACxC,MAAMK,OAAO,GAAGL,QAAQ,CAACE,IAAI,CAAC;MAC9B,IACEG,OAAO,CAACC,IAAI,KAAK,KAAK,IACtBD,OAAO,CAACC,IAAI,KAAK,OAAO,IACxBD,OAAO,CAACC,IAAI,KAAK,SAAS,EAC1B;QACAR,KAAK,CAACS,WAAW,CAACC,IAAI,CAACH,OAAO,CAAC;MACjC;IACF;EACF;AACF,CAAC;AAEM,SAASI,mBAAmBA,CAACC,QAA0B,EAAE;EAC9D,MAAMZ,KAA4B,GAAG;IAAES,WAAW,EAAE;EAAG,CAAC;EACxDG,QAAQ,CAACC,QAAQ,CAAClB,8BAA8B,EAAEK,KAAK,CAAC;EACxD,OAAOA,KAAK,CAACS,WAAW;AAC1B;AAEO,SAASK,cAAcA,CAACP,OAAgB,EAAEK,QAA0B,EAAE;EAG3E,MAAMG,IAAI,GAAG,IAAIC,OAAO,CAAS,CAAC;EAElC,IAAIC,iBAAiB,GAAG,KAAK;EAE7B,MAAMC,kBAAkB,GAAGC,SAAS,CAACZ,OAAO,CAACW,kBAAkB,EAAErB,IAAI,IAAI;IACvE,MAAM;MAAEuB,MAAM;MAAEC;IAAU,CAAC,GAAGC,oBAAoB,CAACzB,IAAI,EAAEe,QAAQ,CAAC;IAClE,IAAI,CAACQ,MAAM,EAAE,OAAO,IAAI;IACxBH,iBAAiB,KAAjBA,iBAAiB,GAAKI,SAAS;IAE/B,MAAME,EAAE,GAAG1B,IAAI,CAAC2B,kBAAkB,CAAC,CAAC,GAChC3B,IAAI,CAAC4B,GAAG,CAAC,UAAU,CAAC,GACpB5B,IAAI,CAAC6B,sBAAsB,CAAC,CAAC,GAC3B7B,IAAI,CAAC4B,GAAG,CAAC,MAAM,CAAC,GAChB,IAAI;IACV,IAAIF,EAAE,EAAER,IAAI,CAACY,GAAG,CAACJ,EAAE,CAACK,IAAI,CAAC;IACzB,OAAOL,EAAE;EACX,CAAC,CAAC;EAEF,MAAMM,UAAU,GAAGV,SAAS,CAACZ,OAAO,CAACuB,cAAc,EAAEjC,IAAI,IAAI;IAC3D,IAAIkB,IAAI,CAACgB,GAAG,CAAClC,IAAI,CAAC+B,IAAI,CAAC,EAAE,OAAO,IAAI;IAEpC,MAAM;MAAER,MAAM;MAAEC;IAAU,CAAC,GAAGC,oBAAoB,CAACzB,IAAI,EAAEe,QAAQ,CAAC;IAClE,IAAI,CAACQ,MAAM,EAAE,OAAO,IAAI;IACxBH,iBAAiB,KAAjBA,iBAAiB,GAAKI,SAAS;IAE/B,OAAOxB,IAAI;EACb,CAAC,CAAC;EAEF,OAAO;IACLoB,iBAAiB;IACjBe,qBAAqB,EAAEd,kBAAkB,CAACe,MAAM,GAAG,CAAC;IACpDC,MAAM,EAAEL,UAAU,CAACM,MAAM,CAACjB,kBAAkB;EAC9C,CAAC;AACH;AAEA,SAASI,oBAAoBA,CAACzB,IAAc,EAAEe,QAA0B,EAAE;EACxE,MAAMwB,QAAQ,GAAGxB,QAAQ,CAACa,GAAG,CAAC,MAAM,CAAC;EACrC,IAAIJ,SAAS,GAAG,KAAK;EAErB,KAAK,IAAIgB,QAAQ,GAAGxC,IAAI,EAAEwC,QAAQ,EAAEA,QAAQ,GAAGA,QAAQ,CAACC,UAAU,EAAE;IAClE,IAAID,QAAQ,CAACE,UAAU,CAAC,CAAC,IAAIF,QAAQ,CAACG,OAAO,CAAC,CAAC,IAAIH,QAAQ,CAACI,QAAQ,CAAC,CAAC,EAAE;MACtEpB,SAAS,GAAG,IAAI;IAClB;IACA,IAAIgB,QAAQ,KAAKD,QAAQ,EAAE;MACzB,OAAO;QAAEhB,MAAM,EAAE,IAAI;QAAEC;MAAU,CAAC;IACpC,CAAC,MAAM,IAAIgB,QAAQ,KAAKzB,QAAQ,EAAE;MAChC,OAAO;QAAEQ,MAAM,EAAE,KAAK;QAAEC;MAAU,CAAC;IACrC;EACF;EAEA,MAAM,IAAIqB,KAAK,CACb,yEACF,CAAC;AACH;AAaA,MAAMC,gCAAkE,GAAG;EACzEC,QAAQA,CAAC/C,IAAI,EAAE;IACbA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb,CAAC;EACD+C,gBAAgB,EAAE;IAChBC,KAAKA,CAAC;MAAElB;IAAK,CAAC,EAAE5B,KAAK,EAAE;MACrBA,KAAK,CAAC+C,WAAW,CAACrC,IAAI,CAACkB,IAAI,CAACoB,KAAK,CAAC5C,IAAI,CAAC;IACzC,CAAC;IACD6C,IAAIA,CAAC;MAAErB;IAAK,CAAC,EAAE5B,KAAK,EAAE;MACpB,MAAMkD,MAAM,GAAGlD,KAAK,CAAC+C,WAAW,CAACI,GAAG,CAAC,CAAC;MACtC,IAAID,MAAM,KAAKtB,IAAI,CAACoB,KAAK,CAAC5C,IAAI,EAAE;QAC9B,MAAM,IAAIsC,KAAK,CAAC,qDAAqD,CAAC;MACxE;IACF;EACF,CAAC;EACDU,IAAI,EAAE;IACJN,KAAKA,CAACO,CAAC,EAAErD,KAAK,EAAE;MACdA,KAAK,CAACsD,wBAAwB,EAAE;MAChCtD,KAAK,CAACuD,qBAAqB,EAAE;IAC/B,CAAC;IACDN,IAAIA,CAACI,CAAC,EAAErD,KAAK,EAAE;MACbA,KAAK,CAACsD,wBAAwB,EAAE;MAChCtD,KAAK,CAACuD,qBAAqB,EAAE;IAC/B;EACF,CAAC;EACDC,eAAe,EAAE;IACfV,KAAKA,CAACO,CAAC,EAAErD,KAAK,EAAE;MACdA,KAAK,CAACuD,qBAAqB,EAAE;IAC/B,CAAC;IACDN,IAAIA,CAACI,CAAC,EAAErD,KAAK,EAAE;MACbA,KAAK,CAACuD,qBAAqB,EAAE;IAC/B;EACF,CAAC;EACD,kCAAkCE,CAChC5D,IAAsD,EACtDG,KAAK,EACL;IACA,MAAM;MAAEgD;IAAM,CAAC,GAAGnD,IAAI,CAAC+B,IAAI;IAC3B,IAAIoB,KAAK,EAAE;MACT,IAAIhD,KAAK,CAAC+C,WAAW,CAACW,QAAQ,CAACV,KAAK,CAAC5C,IAAI,CAAC,EAAE;IAC9C,CAAC,MAAM,IACLP,IAAI,CAAC8D,gBAAgB,CAAC,CAAC,GACnB3D,KAAK,CAACuD,qBAAqB,GAAG,CAAC,GAC/BvD,KAAK,CAACsD,wBAAwB,GAAG,CAAC,EACtC;MACA;IACF;IACAtD,KAAK,CAAC4D,eAAe,CAAClD,IAAI,CAACb,IAAI,CAAC;EAClC,CAAC;EACDgE,eAAeA,CAAChE,IAAI,EAAEG,KAAK,EAAE;IAC3BA,KAAK,CAAC8D,OAAO,CAACpD,IAAI,CAACb,IAAI,CAAC;EAC1B,CAAC;EACDkE,mBAAmBA,CAAClE,IAAI,EAAEG,KAAK,EAAE;IAC/B,IAAIH,IAAI,CAACmE,MAAM,KAAKhE,KAAK,CAACiE,QAAQ,IAAIC,eAAe,CAACrE,IAAI,CAAC,EAAE;IAC7D,IAAIA,IAAI,CAAC+B,IAAI,CAACpB,IAAI,KAAK,KAAK,EAAER,KAAK,CAACmE,IAAI,CAACzD,IAAI,CAACb,IAAI,CAAC;EACrD;AACF,CAAC;AAEM,SAASuE,YAAYA,CAC1BxD,QAA0B,EAC1ByD,QAAkB,EAClBC,qBAA4D,EAC5D;EACA,MAAML,QAAQ,GAAGrD,QAAQ,CAACgB,IAAI;EAC9B,MAAM5B,KAA8B,GAAG;IACrC4D,eAAe,EAAE,EAAE;IACnBE,OAAO,EAAE,EAAE;IACXf,WAAW,EAAE,EAAE;IACfQ,qBAAqB,EAAE,CAAC;IACxBD,wBAAwB,EAAE,CAAC;IAC3Ba,IAAI,EAAE,EAAE;IACRF;EACF,CAAC;EACDrD,QAAQ,CAACC,QAAQ,CAAC8B,gCAAgC,EAAE3C,KAAK,CAAC;EAE1D,MAAMuE,QAAQ,GAAG,EAAE;EACnB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,OAAO,GAAG,EAAE;EAClB,KAAK,MAAM,CAACrE,IAAI,EAAEsE,YAAY,CAAC,IAAIJ,qBAAqB,EAAE;IACxDC,QAAQ,CAAC7D,IAAI,CAACiE,WAAC,CAACC,UAAU,CAACxE,IAAI,CAAC,CAAC;IAEjC,MAAMyE,SAAS,GAAGjE,QAAQ,CAACT,KAAK,CAAC2E,WAAW,CAAC1E,IAAI,CAAC;IAClDoE,aAAa,CAAC9D,IAAI,CAACiE,WAAC,CAACC,UAAU,CAACC,SAAS,CAAC,CAAC;IAC3CJ,OAAO,CAAC/D,IAAI,CACViE,WAAC,CAACI,oBAAoB,CAAC,GAAG,EAAEJ,WAAC,CAACC,UAAU,CAACxE,IAAI,CAAC,EAAEuE,WAAC,CAACC,UAAU,CAACC,SAAS,CAAC,CACzE,CAAC;IACD,KAAK,MAAMhF,IAAI,IAAI6E,YAAY,EAAE7E,IAAI,CAACmF,WAAW,CAACL,WAAC,CAACC,UAAU,CAACC,SAAS,CAAC,CAAC;EAC5E;EACA,KAAK,MAAMzE,IAAI,IAAIiE,QAAQ,EAAE;IAC3B,IAAIC,qBAAqB,CAACvC,GAAG,CAAC3B,IAAI,CAAC,EAAE;IACrCmE,QAAQ,CAAC7D,IAAI,CAACiE,WAAC,CAACC,UAAU,CAACxE,IAAI,CAAC,CAAC;IACjCoE,aAAa,CAAC9D,IAAI,CAACiE,WAAC,CAACC,UAAU,CAACxE,IAAI,CAAC,CAAC;EACxC;EAEA,MAAMmB,EAAE,GAAGX,QAAQ,CAACT,KAAK,CAAC2E,WAAW,CAAC,MAAM,CAAC;EAC7C,MAAMG,EAAE,GAAGN,WAAC,CAACO,kBAAkB,CAC7B,IAAI,EACJV,aAAa,EACbG,WAAC,CAACQ,OAAO,CAAClB,QAAQ,CAACmB,IAAI,CACzB,CAAC;EACD,IAAIC,IAAkB,GAAGV,WAAC,CAACW,cAAc,CAACX,WAAC,CAACC,UAAU,CAACrD,EAAE,CAAC,EAAEgD,QAAQ,CAAC;EAErE,MAAMgB,QAAQ,GAAG3E,QAAQ,CAAC4E,UAAU,CAACC,CAAC,IAAIA,CAAC,CAAClD,UAAU,CAAC,CAAC,CAAC;EACzD,IAAIgD,QAAQ,EAAE;IACZ,MAAM;MAAEG,KAAK;MAAEC;IAAU,CAAC,GAAGJ,QAAQ,CAAC3D,IAAkB;IACxDqD,EAAE,CAACS,KAAK,GAAGA,KAAK;IAChBT,EAAE,CAACU,SAAS,GAAGA,SAAS;IACxB,IAAIA,SAAS,EAAEN,IAAI,GAAGV,WAAC,CAACiB,eAAe,CAACP,IAAI,EAAE,IAAI,CAAC,CAAC,KAC/C,IAAIK,KAAK,EAAEL,IAAI,GAAGV,WAAC,CAACkB,eAAe,CAACR,IAAI,CAAC;EAChD;EAEA,MAAMS,WAAW,GACfrB,OAAO,CAACxC,MAAM,GAAG,CAAC,GACd0C,WAAC,CAACoB,mBAAmB,CAACpB,WAAC,CAACqB,kBAAkB,CAACvB,OAAO,CAAC,CAAC,GACpD,IAAI;EACV,IAAIqB,WAAW,EAAEb,EAAE,CAACG,IAAI,CAACA,IAAI,CAAC1E,IAAI,CAACoF,WAAW,CAAC;EAU/C,MAAM,CAACG,OAAO,CAAC,GAAGrF,QAAQ,CAACsF,YAAY,CACrCvB,WAAC,CAACwB,mBAAmB,CAAC,KAAK,EAAE,CAACxB,WAAC,CAACyB,kBAAkB,CAACzB,WAAC,CAACC,UAAU,CAACrD,EAAE,CAAC,EAAE0D,EAAE,CAAC,CAAC,CAC3E,CAAsC;EAEtC,MAAMoB,SAAwB,GAAG,EAAE;EAEnC,MAAMC,QAAkB,GAAG,EAAE;EAC7B,KAAK,MAAML,OAAO,IAAIjG,KAAK,CAACmE,IAAI,EAAE;IAChC,MAAMoC,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMC,IAAI,IAAIP,OAAO,CAACrE,IAAI,CAAC6E,YAAY,EAAE;MAC5CH,QAAQ,CAAC5F,IAAI,CAAC,GAAGL,MAAM,CAACC,IAAI,CAACqE,WAAC,CAAC+B,qBAAqB,CAACF,IAAI,CAACjF,EAAE,CAAC,CAAC,CAAC;MAC/D,IAAIiF,IAAI,CAACG,IAAI,EAAE;QACbJ,MAAM,CAAC7F,IAAI,CAACiE,WAAC,CAACI,oBAAoB,CAAC,GAAG,EAAEyB,IAAI,CAACjF,EAAE,EAAEiF,IAAI,CAACG,IAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAIJ,MAAM,CAACtE,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI2E,WAAmB,GACrBL,MAAM,CAACtE,MAAM,KAAK,CAAC,GAAGsE,MAAM,CAAC,CAAC,CAAC,GAAG5B,WAAC,CAACqB,kBAAkB,CAACO,MAAM,CAAC;MAChE,IACE,CAAC5B,WAAC,CAACkC,cAAc,CAACZ,OAAO,CAACjC,MAAM,EAAE;QAAE2C,IAAI,EAAEV,OAAO,CAACrE;MAAK,CAAC,CAAC,IACzD,CAAC+C,WAAC,CAACmC,eAAe,CAACb,OAAO,CAACjC,MAAM,EAAE;QAAE+C,IAAI,EAAEd,OAAO,CAACrE;MAAK,CAAC,CAAC,EAC1D;QACAgF,WAAW,GAAGjC,WAAC,CAACoB,mBAAmB,CAACa,WAAW,CAAC;MAClD;MACAX,OAAO,CAACjB,WAAW,CAAC4B,WAAW,CAAC;IAClC,CAAC,MAAM;MACLX,OAAO,CAACe,MAAM,CAAC,CAAC;IAClB;EACF;EACA,IAAIV,QAAQ,CAACrE,MAAM,EAAE;IACnBgE,OAAO,CAACgB,aAAa,CACnB,cAAc,EACdX,QAAQ,CAACY,GAAG,CAAC9G,IAAI,IAAIuE,WAAC,CAACyB,kBAAkB,CAACzB,WAAC,CAACC,UAAU,CAACxE,IAAI,CAAC,CAAC,CAC/D,CAAC;EACH;EAEA,MAAM+G,QAAQ,GAAGnH,KAAK,CAAC4D,eAAe,CAAC3B,MAAM;EAC7C,MAAMmF,SAAS,GAAGpH,KAAK,CAAC8D,OAAO,CAAC7B,MAAM;EACtC,IAAIkF,QAAQ,GAAGC,SAAS,KAAK,CAAC,EAAE;IAC9Bf,SAAS,CAAC3F,IAAI,CAACiE,WAAC,CAACoB,mBAAmB,CAACV,IAAI,CAAC,CAAC;EAC7C,CAAC,MAAM,IAAI8B,QAAQ,KAAK,CAAC,IAAIC,SAAS,KAAK,CAAC,EAAE;IAC5C,KAAK,MAAMvH,IAAI,IAAIG,KAAK,CAAC4D,eAAe,EAAE;MACxC,MAAM;QAAEhC;MAAK,CAAC,GAAG/B,IAAI;MACrB,MAAM;QAAEwH,IAAI;QAAErE;MAAM,CAAC,GAAGpB,IAAI;MAC5B,IAAIxB,IAAI,GAAGiH,IAAI,KAAK,gBAAgB,GAAG,OAAO,GAAG,UAAU;MAC3D,IAAIrE,KAAK,EAAE5C,IAAI,IAAI,GAAG,GAAG4C,KAAK,CAAC5C,IAAI;MACnCP,IAAI,CAACmF,WAAW,CACdL,WAAC,CAAC2C,UAAU,CACV3C,WAAC,CAAC4C,eAAe,CAAC5C,WAAC,CAAC6C,cAAc,CAAC,CAAC,CAAC,CAAC,EACtC,UAAU,EACV,GAAG,GAAGpH,IAAI,EACV,IACF,CACF,CAAC;MACD,IAAI0F,WAAW,EAAEjG,IAAI,CAACqG,YAAY,CAACvB,WAAC,CAAC8C,SAAS,CAAC3B,WAAW,CAAC,CAAC;MAE5DO,SAAS,CAAC3F,IAAI,CACZgH,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B,cAAcvC,IAAK,KAAIzD,IAAK;AAC5B,OACM,CAAC;IACH;EACF,CAAC,MAAM;IACL,MAAMiG,YAAY,GAAGjH,QAAQ,CAACT,KAAK,CAAC2E,WAAW,CAAC,KAAK,CAAC;IAEtD,IAAImB,OAAO,CAAC6B,qBAAqB,CAAC,CAAC,EAAE;MACnC7B,OAAO,CAACgB,aAAa,CAAC,cAAc,EAAE,CACpCtC,WAAC,CAACyB,kBAAkB,CAACzB,WAAC,CAACC,UAAU,CAACiD,YAAY,CAAC,CAAC,CACjD,CAAC;MACFxB,SAAS,CAAC3F,IAAI,CACZiE,WAAC,CAACoB,mBAAmB,CACnBpB,WAAC,CAACI,oBAAoB,CAAC,GAAG,EAAEJ,WAAC,CAACC,UAAU,CAACiD,YAAY,CAAC,EAAExC,IAAI,CAC9D,CACF,CAAC;IACH,CAAC,MAAM;MACLgB,SAAS,CAAC3F,IAAI,CACZiE,WAAC,CAACwB,mBAAmB,CAAC,KAAK,EAAE,CAC3BxB,WAAC,CAACyB,kBAAkB,CAACzB,WAAC,CAACC,UAAU,CAACiD,YAAY,CAAC,EAAExC,IAAI,CAAC,CACvD,CACH,CAAC;IACH;IAEA,MAAM0C,QAAkB,GAAG,EAAE;IAC7B,KAAK,MAAMlI,IAAI,IAAIG,KAAK,CAAC4D,eAAe,EAAE;MACxC,MAAM;QAAEhC;MAAK,CAAC,GAAG/B,IAAI;MACrB,MAAM;QAAEwH,IAAI;QAAErE;MAAM,CAAC,GAAGpB,IAAI;MAC5B,IAAIxB,IAAI,GAAGiH,IAAI,KAAK,gBAAgB,GAAG,OAAO,GAAG,UAAU;MAC3D,IAAIrE,KAAK,EAAE5C,IAAI,IAAI,GAAG,GAAG4C,KAAK,CAAC5C,IAAI;MAEnC,IAAI4H,CAAC,GAAGD,QAAQ,CAACE,OAAO,CAAC7H,IAAI,CAAC;MAC9B,MAAM8H,WAAW,GAAGF,CAAC,KAAK,CAAC,CAAC;MAC5B,IAAI,CAACE,WAAW,EAAE;QAChBH,QAAQ,CAACrH,IAAI,CAACN,IAAI,CAAC;QACnB4H,CAAC,GAAGD,QAAQ,CAAC9F,MAAM,GAAG,CAAC;MACzB;MAEApC,IAAI,CAACmF,WAAW,CACdL,WAAC,CAAC2C,UAAU,CACV3C,WAAC,CAAC4C,eAAe,CAAC5C,WAAC,CAAC6C,cAAc,CAACQ,CAAC,CAAC,CAAC,EACtC,UAAU,EACV,GAAG,GAAG5H,IAAI,EACV,IACF,CACF,CAAC;MACD,IAAI0F,WAAW,EAAEjG,IAAI,CAACqG,YAAY,CAACvB,WAAC,CAAC8C,SAAS,CAAC3B,WAAW,CAAC,CAAC;MAE5D,IAAIoC,WAAW,EAAE;MAEjB7B,SAAS,CAAC3F,IAAI,CACZgH,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B,cAAcjD,WAAC,CAACC,UAAU,CAACiD,YAAY,CAAE,QAAOlD,WAAC,CAAC6C,cAAc,CAACQ,CAAC,CAAE,KAAIpG,IAAK;AAC7E,OACM,CAAC;IACH;IAEA,IAAIwF,SAAS,EAAE;MACb,KAAK,MAAMvH,IAAI,IAAIG,KAAK,CAAC8D,OAAO,EAAE;QAChC,MAAMqE,GAAG,GAAGtI,IAAI,CAAC+B,IAAI,CAACwG,QAAQ,IAAIvI,IAAI,CAACM,KAAK,CAACkI,kBAAkB,CAAC,CAAC;QACjExI,IAAI,CAACmF,WAAW,CACd0C,cAAQ,CAACC,SAAS,CAACC,GAAI;AACjC,wBAAwBO,GAAI;AAC5B,SACQ,CAAC;MACH;MAEA9B,SAAS,CAAC3F,IAAI,CACZgH,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B,gBAAgBjD,WAAC,CAACC,UAAU,CAACiD,YAAY,CAAE,YAAWlD,WAAC,CAACC,UAAU,CACtDiD,YACF,CAAE;AACZ,SACM,CAAC;IACH;EACF;EAEA5D,QAAQ,CAACmB,IAAI,GAAGT,WAAC,CAAC2D,cAAc,CAACjC,SAAS,CAAC;EAE3C,OAAOJ,OAAO;AAChB;AAEO,SAAS/B,eAAeA,CAACrE,IAAqC,EAAE;EACrE,IAAI8E,WAAC,CAACkC,cAAc,CAAChH,IAAI,CAACmE,MAAM,CAAC,EAAE,OAAOnE,IAAI,CAAC0I,GAAG,KAAK,MAAM;EAC7D,IAAI5D,WAAC,CAACmC,eAAe,CAACjH,IAAI,CAACmE,MAAM,CAAC,EAAE,OAAOnE,IAAI,CAAC0I,GAAG,KAAK,MAAM;EAC9D,OAAO,KAAK;AACd;AAEA,SAASpH,SAASA,CAAsBqH,IAAS,EAAEvD,EAAyB,EAAE;EAC5E,MAAMwD,MAAW,GAAG,EAAE;EACtB,KAAK,MAAMC,IAAI,IAAIF,IAAI,EAAE;IACvB,MAAMG,MAAM,GAAG1D,EAAE,CAACyD,IAAI,CAAC;IACvB,IAAIC,MAAM,EAAEF,MAAM,CAAC/H,IAAI,CAACiI,MAAM,CAAC;EACjC;EACA,OAAOF,MAAM;AACf"}