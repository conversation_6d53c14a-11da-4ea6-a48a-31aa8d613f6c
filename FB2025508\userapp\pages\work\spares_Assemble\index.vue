<!-- 看板发货 -->
<template>
	<view class="part-container">
		<!-- 列表 -->
		<uni-card :is-shadow="false" is-full v-for="item in mainList" v-show="listShow" :key='item.orderNum'>
			<view style="border: 1px solid #ccc;border-radius: 6px;padding: 10px;">
				
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="计划号"><template v-slot:right>{{item.plan_num}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="ERP号"><template v-slot:right>{{item.erp_num}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="描述"><template v-slot:right>{{item.erp_remark}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="数量"><template v-slot:right>{{item.plan_quantity}}</template></uni-section>
					</uni-col>
				</uni-row>
				<button type="primary" @click="()=>operationClick(item.id)" >操作</button>
			</view>
		</uni-card>
		

		<!-- 操作信息 -->
		<view v-show="!listShow">
			<uni-forms ref="form" :modelValue="formData">
				<uni-forms-item>
					<uni-col :span="20">
						<uni-section class="mb-10" title="计划:"><template v-slot:right>{{formData.plannum}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
                <uni-forms-item>
					<uni-col :span="20">
						<uni-section class="mb-10" title="Erp号:"><template v-slot:right>{{formData.erpnum}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
				<uni-forms-item>
					<uni-col :span="20">
						<uni-section class="mb-10" title="描述:"><template v-slot:right>{{formData.erpremark}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
				<uni-forms-item>
					<uni-col :span="20">
						<uni-section class="mb-10" title="计划数量:"><template v-slot:right>{{formData.planquantity}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
				<uni-forms-item>
					<uni-col :span="20">
						<uni-section class="mb-10" title="生产数量:"><template v-slot:right>{{formData.realquanity}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
				<uni-forms-item name="barCode">
                   <uni-section title="条码" subTitle="" type="line" padding>
					<!-- <uni-easyinput ref="barCodeInput"  trim="all" v-model="formData.barCode" focus placeholder="请输入内容" @change="barCodeChange"></uni-easyinput> -->
					<uni-view class="bar-code-css-view1 uni-input-wrapper">
						<uni-easyinput 
							id="barCodeInputClick" 
							class="uni-input bar-code-css1" 
							focus 
							placeholder="请进行扫码" 
							trim="all"
							v-model="formData.barCode" 
							@focus="barCodeFocus" 
							@blur="barCodeBlur" 
							@confirm="barCodeChange" />
					</uni-view>
				</uni-section>
				</uni-forms-item>
			</uni-forms>
			<button type="warn" @click="reset">返回</button>
			<uni-table ref="table" :loading="loading" border stripe emptyText="暂无更多数据" @selection-change="selectionChange">
			<uni-tr :style="{ background: '#000000' }">
				<uni-th align="center">ERP号</uni-th>
				<uni-th align="center">条码</uni-th>
			</uni-tr>
			<uni-tr v-for="(item, index) in sonList" :key="index" :data-id="item.ID" 
				:style="{ background: item.toivCount == 0 ? '#ffffff' : '#33f533' }">
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.erp_num }}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.barcode }}</uni-td>
			</uni-tr>
		</uni-table>
		</view>
		
		<!-- 遮罩 -->
		<uni-popup ref="loading" type="center" :animation="false" :mask-click="false">遮罩</uni-popup>
		<!-- 消息 -->
		<uni-popup ref="popup"><view class="popup-content"><text class="text" :duration="100">上报成功</text></view></uni-popup>
		<uni-popup ref="popup1"><view class="popup1-content"><text class="text" :duration="500">{{errorMsg}}</text></view></uni-popup>
		
		<!-- <view>
			<input v-model="text" placeholder="输入一段文字" />
			<button @click="speak">播放文本</button>
		</view> -->
	</view>
</template>

<script>
  import { findSpare,findSparebyid,scanBarcode,findSpareListByPlanNum} from '@/api/work/spares_Assemble.js'
  
import { reactive } from "vue"
	export default {
	  data() {
	    return {
			//列表的显示
			listShow: true,
			// text: '',
			errorMsg: '',
			formData: {
				id: '',
				plannum: '',
				erpnum: '',
				erpremark: '',
				planquantity: '',
				realquanity: '',
				barCode: '',
			},
			formTem1: { barCode: '', erpNum: '', remark: '', num: '', },
			formTem2: {deviceCode: '', assemblyList: [{ barCode: '', erpNum: '', remark: '', num: '', }] },
			mainList: [],
			sonList: [],
			rules: {
				deviceCode: {
					rules:[
						{
							required: true,
							errorMessage: '请扫描器具码',
						}
					],
					validateTrigger:'submit'
				},
				erpNum: {
					rules:[
						{
							required: true,
							errorMessage: '请填写ERP号或者进行扫描条码',
						}
					],
					validateTrigger:'submit'
				},
				actualLoadingQuantity: {
					rules:[
						{
							required: true,
							errorMessage: '请填写数量',
						}
					],
					validateTrigger:'submit'
				},
			},
			lastScanTime: 0, // 添加这行，记录上次扫码时间
	    }
	  },
	  created(){
	  	this.tableList()
	  },
	  methods: {
		  //列表获取
		  tableList(){
			  findSpare().then(r=>{
			  	this.mainList= r.data
			  })
		  },
		  //操作的点击事件
		  operationClick(id){
			this.listShow= false;
			findSparebyid({'id':id}).then(r=>{
			 this.formData.id =  r.data.id;
			 this.formData.plannum =  r.data.plan_num;
			 this.formData.erpnum =  r.data.erp_num;
			 this.formData.erpremark =  r.data.erp_remark;
			 this.formData.planquantity =  r.data.plan_quantity;
			 this.formData.erpnum =  r.data.erp_num;
			 this.formData.realquanity = r.data.real_quanity;
			 this.barCodeClick();

			 findSpareListByPlanNum({'planNum':this.formData.plannum}).then(r=>{
                    this.sonList = r.data; 
				 })
				//this.formData= { ...r.data,  deviceList: [{deviceCode: '', assemblyList: [{ barCode: '', erpNum: '', remark: '', num: '', }] }] }
				
			})
		  },
		  // 条码事件
		  barCodeChange(){
			
			  const now = Date.now();
			  // 计算与上次扫码的时间间隔（毫秒）
			  const timeGap = now - this.lastScanTime;
			  
			  // 如果间隔小于1秒（1000毫秒）
			  if (timeGap < 1500) {
				  this.errorMsg = '扫码频率过快，请稍后再试';
				  this.speak(this.errorMsg);
				  this.msg('popup1');
				  this.formData.barCode = "";
				  this.barCodeClick();
				  return;
			  }
			  
			  // 更新最后扫码时间
			  this.lastScanTime = now;
			  
			  // 原有的扫码逻辑
			  this.$refs.loading.open()
			  if(this.formData.barCode.length==0) {
				  this.errorMsg= '请扫描条码';
				  this.speak(this.errorMsg);
				  this.msg('popup1');
				  return;
			  }
			  scanBarcode({'barCode':this.formData.barCode,'PlanErp':this.formData.erpnum,'id':this.formData.id}).then(r=>{
				  if(r.data.code == '200'){
					this.formData.realquanity = r.data.realnum;
					var param = {erp_num: this.formData.erpnum,barcode: this.formData.barCode};
					this.sonList.push(param);
					this.speak("绑定成功")
					this.msg('popup')
					this.formData.barCode = "";
					this.barCodeClick();

				  } else {
					this.errorMsg= r.data.msg;
					this.formData.realquanity = r.data.realnum;
					this.speak('绑定失败')
					this.msg('popup1')
				    this.formData.barCode = "";
					this.barCodeClick();
				  }
			  }).catch(e=>{
				  this.errorMsg= e
				  this.speak(this.errorMsg)
				  this.msg('popup1')
			      this.formData.barCode = "";
				  this.barCodeClick();
			  })
		  },
		  //条码触发焦点回调事件
		  barCodeFocus(){
			  //border-color: rgb(41, 121, 255);
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = 'rgb(41, 121, 255)';
			  }
		  },
		  //条码离开焦点回调事件
		  barCodeBlur(){
			  //去除边框样式
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = '';
			  }
		  },
		  //重置
		  reset(){
			  this.formData.deviceList = []
			  this.formData= JSON.parse(JSON.stringify(this.formData))
			  this.tableList()
			  this.listShow= true
		  },
		  //消息提示
		  msg(type){
			this.$refs[type].open('center')
			setTimeout(() => {
				this.$refs[type].close()
			}, 3000);
			this.$refs.loading.close()
		  },
		  //语音提示
		  speak(text) {
			  var music = null;
			  music = uni.createInnerAudioContext(); //创建播放器对象
			  music.src = `../../../static/video/msg/${text.includes("成功") ? 'czcg.wav' : 'czsb.mp3'}`;
			  music.volume = 1;
			  music.play(); //执行播放
			  music.onEnded(() => {
				  //播放结束
				  music = null;
			  });
		  },
		   barCodeClick(){
			document.getElementById("barCodeInputClick").getElementsByTagName('input')[0].focus();
		  },
		  //数组对比
		  arraysAreDifferent(arr11, arr22) {
			const arr1 = [ ...new Set(arr11) ]
			const arr2 = [ ...new Set(arr22) ]
		    return !arr2.every(item => arr1.includes(item))
		  },
	  },
	}
</script>

<style scoped lang="scss">
.part-container{
	background-color: #ffffff;
}
.bar-code-css{
	height: 35px;
	text-indent: 10px;
	display: flex;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	color: #000;
	font-size: 14px;
}
.bar-code-css .uni-input-placeholder.input-placeholder{
	color: #999;
}
.bar-code-css-view{
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	/* border-color: rgb(41, 121, 255); */
	background-color: rgb(255, 255, 255);
}
.popup-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #09bb07;
	background-color: #e1f3d8;
}
.popup1-content{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #f56c6c;
	background-color: #fde2e2;
}

.view-border{
	margin: 10px;
	padding: 10px;
	border: 1px solid #ccc;
	border-radius: 6px;
}
	
::v-deep .view-border .section-css uni-view.uni-section-header__slot-right{
	width: 70%!important;
}
</style>