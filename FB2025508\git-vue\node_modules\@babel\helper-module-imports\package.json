{"_from": "@babel/helper-module-imports@^7.22.15", "_id": "@babel/helper-module-imports@7.22.15", "_inBundle": false, "_integrity": "sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==", "_location": "/@babel/helper-module-imports", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-module-imports@^7.22.15", "name": "@babel/helper-module-imports", "escapedName": "@babel%2fhelper-module-imports", "scope": "@babel", "rawSpec": "^7.22.15", "saveSpec": null, "fetchSpec": "^7.22.15"}, "_requiredBy": ["/@babel/helper-module-transforms", "/@babel/plugin-transform-async-to-generator", "/@babel/plugin-transform-runtime", "/@vue/babel-plugin-jsx", "/@vue/babel-plugin-resolve-type", "/@vue/babel-plugin-transform-vue-jsx", "/@vue/babel-preset-app"], "_resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz", "_shasum": "16146307acdc40cc00c3b2c647713076464bdbf0", "_spec": "@babel/helper-module-imports@^7.22.15", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-module-transforms", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.15"}, "deprecated": false, "description": "Babel helper functions for inserting module loads", "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-module-imports", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-imports"}, "type": "commonjs", "version": "7.22.15"}