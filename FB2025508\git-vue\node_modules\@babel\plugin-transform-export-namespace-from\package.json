{"_from": "@babel/plugin-transform-export-namespace-from@^7.23.4", "_id": "@babel/plugin-transform-export-namespace-from@7.23.4", "_inBundle": false, "_integrity": "sha512-GzuSBcKkx62dGzZI1WVgTWvkkz84FZO5TC5T8dl/Tht/rAla6Dg/Mz9Yhypg+ezVACf/rgDuQt3kbWEv7LdUDQ==", "_location": "/@babel/plugin-transform-export-namespace-from", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-export-namespace-from@^7.23.4", "name": "@babel/plugin-transform-export-namespace-from", "escapedName": "@babel%2fplugin-transform-export-namespace-from", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.23.4.tgz", "_shasum": "084c7b25e9a5c8271e987a08cf85807b80283191", "_spec": "@babel/plugin-transform-export-namespace-from@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "deprecated": false, "description": "Compile export namespace to ES2015", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-export-namespace-from", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-export-namespace-from", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-export-namespace-from"}, "type": "commonjs", "version": "7.23.4"}