{"_from": "@babel/plugin-syntax-unicode-sets-regex@^7.18.6", "_id": "@babel/plugin-syntax-unicode-sets-regex@7.18.6", "_inBundle": false, "_integrity": "sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==", "_location": "/@babel/plugin-syntax-unicode-sets-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-unicode-sets-regex@^7.18.6", "name": "@babel/plugin-syntax-unicode-sets-regex", "escapedName": "@babel%2fplugin-syntax-unicode-sets-regex", "scope": "@babel", "rawSpec": "^7.18.6", "saveSpec": null, "fetchSpec": "^7.18.6"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "_shasum": "d49a3b3e6b52e5be6740022317580234a6a47357", "_spec": "@babel/plugin-syntax-unicode-sets-regex@^7.18.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "deprecated": false, "description": "Parse regular expressions' unicodeSets (v) flag.", "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-unicode-sets-regex", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-unicode-sets-regex", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-unicode-sets-regex"}, "type": "commonjs", "version": "7.18.6"}