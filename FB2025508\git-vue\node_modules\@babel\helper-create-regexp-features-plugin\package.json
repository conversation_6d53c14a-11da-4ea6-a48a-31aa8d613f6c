{"_from": "@babel/helper-create-regexp-features-plugin@^7.18.6", "_id": "@babel/helper-create-regexp-features-plugin@7.22.15", "_inBundle": false, "_integrity": "sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==", "_location": "/@babel/helper-create-regexp-features-plugin", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-create-regexp-features-plugin@^7.18.6", "name": "@babel/helper-create-regexp-features-plugin", "escapedName": "@babel%2fhelper-create-regexp-features-plugin", "scope": "@babel", "rawSpec": "^7.18.6", "saveSpec": null, "fetchSpec": "^7.18.6"}, "_requiredBy": ["/@babel/plugin-syntax-unicode-sets-regex", "/@babel/plugin-transform-dotall-regex", "/@babel/plugin-transform-named-capturing-groups-regex", "/@babel/plugin-transform-unicode-property-regex", "/@babel/plugin-transform-unicode-regex", "/@babel/plugin-transform-unicode-sets-regex"], "_resolved": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.15.tgz", "_shasum": "5ee90093914ea09639b01c711db0d6775e558be1", "_spec": "@babel/helper-create-regexp-features-plugin@^7.18.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\plugin-syntax-unicode-sets-regex", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "regexpu-core": "^5.3.1", "semver": "^6.3.1"}, "deprecated": false, "description": "Compile ESNext Regular Expressions to ES5", "devDependencies": {"@babel/core": "^7.22.15", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-create-regexp-features-plugin", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "type": "commonjs", "version": "7.22.15"}