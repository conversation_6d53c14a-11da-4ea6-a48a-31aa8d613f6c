package com.ruoyi.faty.task;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.faty.domain.SysAutoTask;
import com.ruoyi.faty.mapper.SparesPaintingMapper;
import com.ruoyi.faty.mapper.SysAutoTaskMapper;
import com.ruoyi.faty.service.IBizAppUserService;
import com.ruoyi.faty.service.ISysAutoTaskService;
import com.ruoyi.faty.utils.UserUtils;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 自动分配用户定时任务
 *
 * <AUTHOR>
 */
@Component
public class AutoDistributeTask {
    private static final Logger log = LoggerFactory.getLogger(AutoDistributeTask.class);

    @Autowired
    private IBizAppUserService bizAppUserService;

    @Autowired
    private UserUtils userUtils;

    @Autowired
    private ISysAutoTaskService sysAutoTaskService;

    @Autowired
    private org.springframework.jdbc.core.JdbcTemplate jdbcTemplate;
    @Autowired
    private SparesPaintingMapper sparesPaintingMapper;
    /**
     * 执行自动分配用户任务
     * 使用数据库中配置的cron表达式来控制执行时间
     */
    @Scheduled(cron = "0 0/1 * * * ?") // 每分钟检查一次，实际执行取决于数据库配置
    public void executeAutoDistribute() {
        log.debug("执行自动分配用户定时检查");

        try {
            // 获取所有区域的ID
            List<Map<String, Object>> regions = getAllRegions();
            if (regions == null || regions.isEmpty()) {
                log.warn("未找到任何区域信息，跳过执行");
                return;
            }

            // 逐个区域处理
            for (Map<String, Object> region : regions) {
                Long regionId = Long.valueOf(region.get("region_id").toString());
                String regionName = region.get("region_name").toString();

                try {
                    // 获取该区域的配置
                    Map<String, String> config = sysAutoTaskService.getAutoDistributeConfigByRegion(regionId);

                    // 检查是否启用自动分配功能
                    boolean enabled = "true".equals(config.get("enabled"));
                    if (!enabled) {
                        log.debug("区域[{}]的自动分配用户功能未启用，跳过执行", regionName);
                        continue;
                    }

                    // 获取配置的最大用户数
                    Integer maxUserCount = 10; // 默认值
                    if (config.containsKey("maxUserCount")) {
                        try {
                            maxUserCount = Integer.parseInt(config.get("maxUserCount"));
                        } catch (NumberFormatException e) {
                            log.warn("区域[{}]的最大用户数配置格式错误，使用默认值: 10", regionName);
                        }
                    }

                    // 检查当前时间是否符合配置的cron表达式
                    String configuredCron = config.get("cron");
                    if (!shouldRunAtCurrentTime(configuredCron)) {
                        log.debug("区域[{}]的当前时间不在配置的执行时间范围内，跳过执行", regionName);
                        continue;
                    }

                    log.info("开始执行区域[{}]的自动分配用户定时任务，每个店员最大分配用户数: {}", regionName, maxUserCount);
//                    LoginUser uss= SecurityUtils.getLoginUser();
//                    SysUser sysu = SecurityUtils.getLoginUser().getSysUser();
//                    if(sysu.getUserName().contains("admin")){
//                        log.info("超级管理员无法分配区域用户", sysu.getUserName());
//                        return;
//                    }
                    // 获取该区域的管理员账号
                    List<Map<String, Object>>  ltmap =sparesPaintingMapper.getUserListByDeptName(regionName);
                    if (ltmap == null) {
                        log.error("无法获取区域[{}]的管理员账号，跳过执行", regionName);
                        continue;
                    }
                    // 获取该区域的管理员账号
//                    SysUser adminUser = getRegionAdmin(regionId);
//                    if (adminUser == null) {
//                        log.error("无法获取区域[{}]的管理员账号，跳过执行", regionName);
//                        continue;
//                    }

                  //  log.info("使用管理员账号[{}]执行区域[{}]的自动分配用户", adminUser.getUserName(), regionName);

                    // 调用服务层执行自动分配逻辑
                    bizAppUserService.autoDistributeUsers(maxUserCount, ltmap);

                    log.info("区域[{}]的自动分配用户定时任务执行完成", regionName);
                } catch (Exception e) {
                    log.error("执行区域[{}]的自动分配用户定时任务异常", regionName, e);
                }
            }
        } catch (Exception e) {
            log.error("执行自动分配用户定时任务异常", e);
        }
    }

    /**
     * 检查当前时间是否符合配置的cron表达式
     * 简化版：只检查小时和分钟是否匹配
     * @param cron cron表达式，例如 "0 30 14 * * ?"表示每天14:30执行
     * @return 是否应该在当前时间执行
     */
    private boolean shouldRunAtCurrentTime(String cron) {
        if (StringUtils.isEmpty(cron)) {
            return false;
        }

        try {
            // 解析cron表达式
            String[] parts = cron.split(" ");
            if (parts.length < 6) {
                log.warn("无效的cron表达式: {}", cron);
                return false;
            }

            // 获取配置的分钟和小时
            int minute = Integer.parseInt(parts[1]);
            int hour = Integer.parseInt(parts[2]);

            // 获取当前时间
            java.util.Calendar cal = java.util.Calendar.getInstance();
            int currentMinute = cal.get(java.util.Calendar.MINUTE);
            int currentHour = cal.get(java.util.Calendar.HOUR_OF_DAY);

            // 只有当前小时和分钟都匹配时才执行
            return currentHour == hour && currentMinute == minute;
        } catch (Exception e) {
            log.warn("解析cron表达式出错: {}", cron, e);
            return false;
        }
    }

    /**
     * 获取所有区域信息
     * @return 区域信息列表，包含 region_id 和 region_name
     */
    private List<Map<String, Object>> getAllRegions() {
        String sql = "SELECT region_id, region_name FROM biz_region ORDER BY region_id";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 获取指定区域的管理员账号
     * @param regionId 区域编号
     * @return 管理员用户对象，如果找不到则返回null
     */
    private SysUser getRegionAdmin(Long regionId) {
        try {
            // 首先查询该区域的管理员用户
            String sql = "SELECT u.user_id, u.user_name, u.nick_name " +
                    "FROM sys_user u " +
                    "INNER JOIN sys_user_role ur ON u.user_id = ur.user_id " +
                    "INNER JOIN sys_dept_region dr ON u.dept_id = dr.dept_id " +
                    "WHERE ur.role_id IN (SELECT role_id FROM sys_role WHERE role_key = 'admin') " +
                    "AND dr.region_name = ? " +
                    "LIMIT 1";

            List<SysUser> users = jdbcTemplate.query(sql, new Object[]{regionId}, (rs, rowNum) -> {
                SysUser user = new SysUser();
                user.setUserId(rs.getLong("user_id"));
                user.setUserName(rs.getString("user_name"));
                user.setNickName(rs.getString("nick_name"));
                return user;
            });

            if (users != null && !users.isEmpty()) {
                return users.get(0);
            }

            // 如果找不到区域管理员，则获取任一管理员
            log.warn("未找到区域[{}]的专属管理员，尝试获取系统管理员", regionId);
            List<SysUser> adminUsers = userUtils.getAdminUsers();
            if (adminUsers != null && !adminUsers.isEmpty()) {
                return adminUsers.get(0);
            }

            return null;
        } catch (Exception e) {
            log.error("获取区域[{}]管理员异常", regionId, e);
            return null;
        }
    }
}
