{"_from": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.23.3", "_id": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.23.3", "_inBundle": false, "_integrity": "sha512-iRkKcCqb7iGnq9+3G6rZ+Ciz5VywC4XNRHe57lKM+jOeYAoR0lVqdeeDRfh0tQcTfw/+vBhHn926FmQhLtlFLQ==", "_location": "/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.23.3", "name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "escapedName": "@babel%2fplugin-bugfix-safari-id-destructuring-collision-in-function-expression", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.23.3.tgz", "_shasum": "5cd1c87ba9380d0afb78469292c954fee5d2411a", "_spec": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-function-name": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "keywords": ["babel-plugin", "bugfix"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"}, "type": "commonjs", "version": "7.23.3"}