
# mutation-observer

  Exposes the native `MutationObserver` API provided by the browser, or a polyfill based on mutation events. (For compatibility with IE9-10.)

  MutationObserver polyfill by the [Polymer Project](https://www.polymer-project.org/).

## Installation

```bash
$ npm install mutation-observer
```

## API

```javascript
var MutationObserver = require('mutation-observer');
```

## License

  BSD (See LICENSE file)