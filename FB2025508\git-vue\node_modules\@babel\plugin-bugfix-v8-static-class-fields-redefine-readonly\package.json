{"_from": "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.23.7", "_id": "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.23.7", "_inBundle": false, "_integrity": "sha512-LlRT7HgaifEpQA1ZgLVOIJZZFVPWN5iReq/7/JixwBtwcoeVGDBD53ZV28rrsLYOZs1Y/EHhA8N/Z6aazHR8cw==", "_location": "/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.23.7", "name": "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly", "escapedName": "@babel%2fplugin-bugfix-v8-static-class-fields-redefine-readonly", "scope": "@babel", "rawSpec": "^7.23.7", "saveSpec": null, "fetchSpec": "^7.23.7"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.23.7.tgz", "_shasum": "516462a95d10a9618f197d39ad291a9b47ae1d7b", "_spec": "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.23.7", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Transform static class fields assignments that are affected by https://crbug.com/v8/12421", "devDependencies": {"@babel/core": "^7.23.7", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.7"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-bugfix-v8-static-class-fields-redefine-readonly", "keywords": ["babel-plugin", "bugfix"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-bugfix-v8-static-class-fields-redefine-readonly"}, "type": "commonjs", "version": "7.23.7"}