<template>
  <view class="mine-container" :style="{height: `${windowHeight}px`}">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <view v-if="!avatar" class="cu-avatar xl round bg-white">
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix">
          </image>
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            点击登录
          </view>
          <view v-if="name" @click="handleToInfo" class="user-info">
            <view class="u_title">
              用户名：{{ name }}
            </view>
          </view>
        </view>
        <view @click="handleToInfo" class="flex align-center">
          <text>个人信息</text>
          <view class="iconfont icon-right"></view>
        </view>
      </view>
    </view>

    <view class="content-section">
<!--      <view class="mine-actions grid col-4 text-center">
        <view class="action-item" @click="handleJiaoLiuQun">
          <view class="iconfont icon-friendfill text-pink icon"></view>
          <text class="text">交流群</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-service text-blue icon"></view>
          <text class="text">在线客服</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-community text-mauve icon"></view>
          <text class="text">反馈社区</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-dianzan text-green icon"></view>
          <text class="text">点赞我们</text>
        </view>
      </view> -->

      <view class="menu-list">
        <view class="list-cell list-cell-arrow" @click="handleToEditInfo">
          <view class="menu-item-box">
            <view class="iconfont icon-user menu-icon"></view>
            <view>门店信息</view>
          </view>
        </view>
       <view class="list-cell list-cell-arrow" @click="handleHelp">
          <view class="menu-item-box">
            <view class="iconfont icon-help menu-icon"></view>
            <view>服务项目统计</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleAbout">
          <view class="menu-item-box">
            <view class="iconfont icon-aixin menu-icon"></view>
            <view>订单数据统计</view>
          </view>
        </view>
		<view class="list-cell list-cell-arrow" @click="handleuserApplication">
		  <view class="menu-item-box">
		    <view class="iconfont icon-aixin menu-icon"></view>
		    <view>用户手动分配</view>
		  </view>
		</view>
		<view class="list-cell list-cell-arrow" @click="handleuserAssign">
		  <view class="menu-item-box">
		    <view class="iconfont icon-aixin menu-icon"></view>
		    <view>我要加盟</view>
		  </view>
		</view>
		<view class="list-cell list-cell-arrow" @click="ShopOpinion">
		  <view class="menu-item-box">
		    <view class="iconfont icon-aixin menu-icon"></view>
		    <view>意见反馈</view>
		  </view>
		</view>
		<view class="list-cell">
		  <view class="menu-item-box-between">
		    <view class="menu-item-left">
              <view class="iconfont icon-aixin menu-icon"></view>
              <view>是否允许店员登录</view>
            </view>
            <view class="menu-item-right">
              <view class="switch-label" :class="{'switch-label-enabled': allowStaffLogin, 'switch-label-disabled': !allowStaffLogin}">
                {{ allowStaffLogin ? '允许' : '禁止' }}
              </view>
              <view class="custom-switch" :class="{'custom-switch-on': allowStaffLogin}" @click="toggleCustomSwitch">
                <view class="custom-switch-handle"></view>
              </view>
            </view>
		  </view>
		</view>
        <view class="list-cell list-cell-arrow" @click="handleToSetting">
          <view class="menu-item-box">
            <view class="iconfont icon-setting menu-icon"></view>
            <view>应用设置</view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
  import storage from '@/utils/storage'
  import { updateStaffLoginStatus } from '@/api/work/retrun_part.js'
  
  export default {
    data() {
      return {
        name: this.$store.state.user.name,
        version: getApp().globalData.config.appInfo.version,
        allowStaffLogin: true,
        loading: false
      }
    },
    computed: {
      avatar() {
        return this.$store.state.user.avatar
      },
      windowHeight() {
        return uni.getSystemInfoSync().windowHeight - 50
      }
    },
    onShow() {
      this.loadStaffLoginSetting();
    },
    methods: {
      loadStaffLoginSetting() {
        // 显示加载中
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        
        // 调用后端接口获取当前状态
        this.getShopInfo().then(res => {
          if (res && res.code === 200 && res.data) {
            // 获取店铺下任意一个店员的登录状态，判断当前设置
            const loginStatus = res.data.staffLoginStatus;
            this.allowStaffLogin = loginStatus === '1';
            
            // 同步到本地存储
            uni.setStorageSync('allowStaffLogin', this.allowStaffLogin);
          }
          uni.hideLoading();
        }).catch(() => {
          // 从本地存储获取
          const setting = uni.getStorageSync('allowStaffLogin');
          this.allowStaffLogin = setting !== '' ? setting : true;
          uni.hideLoading();
        });
      },
      
      // 获取店铺信息和店员登录状态
      getShopInfo() {
        return new Promise((resolve, reject) => {
          updateStaffLoginStatus({ action: 'query' })
            .then(res => {
              resolve(res);
            })
            .catch(err => {
              console.error('获取店员登录状态失败', err);
              reject(err);
            });
        });
      },
      
      // 自定义滑块点击事件
      toggleCustomSwitch() {
        if (this.loading) return;

        // 模拟原生switch的事件格式，调用现有逻辑
        const newValue = !this.allowStaffLogin;
        this.toggleStaffLogin({
          detail: {
            value: newValue
          }
        });
      },

      toggleStaffLogin(e) {
        if (this.loading) return;
        this.loading = true;

        const value = e.detail.value;
        const statusValue = value ? '1' : '0';
        
        // 显示加载中
        uni.showLoading({
          title: '更新中...',
          mask: true
        });
        
        // 调用后端接口保存设置
        updateStaffLoginStatus({
          action: 'update',
          loginStatus: statusValue
        })
        .then(res => {
          uni.hideLoading();
          
          if (res.code === 200) {
            // 更新成功，保存到本地存储
            this.allowStaffLogin = value;
            uni.setStorageSync('allowStaffLogin', value);
            
            // 提示成功信息
            this.$modal.showToast(value ? '已允许店员登录' : '已禁止店员登录');
          } else {
            // 更新失败，恢复原来的设置
            this.allowStaffLogin = !value;
            this.$modal.showToast(res.msg || '更新失败，请稍后重试');
          }
          this.loading = false;
        })
        .catch(err => {
          uni.hideLoading();
          console.error('更新店员登录状态失败', err);
          this.$modal.showToast('更新失败，请稍后重试');
          // 恢复原来的设置
          this.allowStaffLogin = !value;
          this.loading = false;
        });
      },
      handleToInfo() {
        this.$tab.navigateTo('/pages/mine/info/index')
      },
      handleToEditInfo() {
        this.$tab.navigateTo('/pages/mine/info/edit')
      },
      handleToSetting() {
        this.$tab.navigateTo('/pages/mine/setting/index')
      },
      handleToLogin() {
        this.$tab.reLaunch('/pages/login')
      },
      handleToAvatar() {
        this.$tab.navigateTo('/pages/mine/avatar/index')
      },
      handleLogout() {
        this.$modal.confirm('确定注销并退出系统吗？').then(() => {
          this.$store.dispatch('LogOut').then(() => {
            this.$tab.reLaunch('/pages/index')
          })
        })
      },
      handleHelp() {
        this.$tab.navigateTo('/pages/mine/help/index')
      },
      handleAbout() {
        this.$tab.navigateTo('/pages/mine/about/index')
      },
	  handleuserAssign() {
	    this.$tab.navigateTo('/pages/mine/ShopApplication/ShopApplication')
	  },
	  ShopOpinion() {
	    this.$tab.navigateTo('/pages/mine/ShopOpinion/ShopOpinion')
	  },
	  handleuserApplication() {
	    this.$tab.navigateTo('/pages/mine/userAssign/index')
	  },
      handleJiaoLiuQun() {
        this.$modal.showToast('QQ群：①133713780、②146013835')
      },
      handleBuilding() {
        this.$modal.showToast('模块建设中~')
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #f5f6f7;
  }

  .mine-container {
    width: 100%;
    height: 100%;


    .header-section {
      padding: 15px 15px 45px 15px;
      background-color: #3c96f3;
      color: white;

      .login-tip {
        font-size: 18px;
        margin-left: 10px;
      }

      .cu-avatar {
        border: 2px solid #eaeaea;

        .icon {
          font-size: 40px;
        }
      }

      .user-info {
        margin-left: 15px;

        .u_title {
          font-size: 18px;
          line-height: 30px;
        }
      }
    }

    .content-section {
      position: relative;
      top: -50px;

      .mine-actions {
        margin: 15px 15px;
        padding: 20px 0px;
        border-radius: 8px;
        background-color: white;

        .action-item {
          .icon {
            font-size: 28px;
          }

          .text {
            display: block;
            font-size: 13px;
            margin: 8px 0px;
          }
        }
      }
    }
  }
  
  .menu-item-box-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
  }
  
  .menu-item-left {
    display: flex;
    align-items: center;
  }
  
  .menu-item-right {
    display: flex;
    align-items: center;
  }
  
  .switch-label {
    font-size: 14px;
    margin-right: 10px;
    padding: 4px 12px;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 40px;
    text-align: center;
  }

  .switch-label-enabled {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  .switch-label-disabled {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }

  /* 自定义滑块样式 */
  .custom-switch {
    position: relative;
    width: 50px;
    height: 28px;
    background-color: #ddd;
    border-radius: 14px;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .custom-switch-on {
    background-color: #52c41a;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 8px rgba(82, 196, 26, 0.3);
  }

  .custom-switch-handle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .custom-switch-on .custom-switch-handle {
    transform: translateX(22px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  /* 添加点击效果 */
  .custom-switch:active {
    transform: scale(0.95);
  }

  .custom-switch:active .custom-switch-handle {
    width: 26px;
  }
</style>
