{"_from": "@babel/plugin-transform-object-super@^7.23.3", "_id": "@babel/plugin-transform-object-super@7.23.3", "_inBundle": false, "_integrity": "sha512-BwQ8q0x2JG+3lxCVFohg+KbQM7plfpBwThdW9A6TMtWwLsbDA01Ek2Zb/AgDN39BiZsExm4qrXxjk+P1/fzGrA==", "_location": "/@babel/plugin-transform-object-super", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-object-super@^7.23.3", "name": "@babel/plugin-transform-object-super", "escapedName": "@babel%2fplugin-transform-object-super", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.23.3.tgz", "_shasum": "81fdb636dcb306dd2e4e8fd80db5b2362ed2ebcd", "_spec": "@babel/plugin-transform-object-super@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20"}, "deprecated": false, "description": "Compile ES2015 object super to ES5", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-super", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-object-super", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-object-super"}, "type": "commonjs", "version": "7.23.3"}