package com.ruoyi.faty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface UserAppMapper extends BaseMapper<HashMap<String,Object>> {

    /**
     * 通过SQL查询单条记录
     * @param sql SQL语句
     * @return 查询结果
     */
    @Select("${sql}")
    Map<String, Object> selectOneBySql(@Param("sql") String sql);


    @Select("SELECT s.*, sh.*, r.* " +
            "FROM biz_staff s " +
            "JOIN biz_shop sh ON s.shop_id = sh.shop_id " +
            "JOIN biz_region r ON sh.region_id = r.region_id " +
            "WHERE s.staff_name = #{staffName} AND sh.region_id = #{regionId}")
    List<Map<String, Object>> selectStaffWithShopAndRegion(
            @Param("staffName") String staffName,
            @Param("regionId") Long regionId
    );
    /**
     * 通过SQL查询多条记录
     * @param sql SQL语句
     * @return 查询结果列表
     */
    @Select("${sql}")
    List<Map<String, Object>> selectListBySql(@Param("sql") String sql);

    /**
     * 通过SQL更新数据
     * @param sql SQL语句
     * @return 影响行数
     */
    @Update("${sql}")
    int updateBySql(@Param("sql") String sql);

    /**
     * 通过SQL插入数据
     * @param sql SQL语句
     * @return 影响行数
     */
    @Insert("${sql}")
    int insertBySql(@Param("sql") String sql);

    @Delete("${sql}")
    int deleteBySql(@Param("sql") String sql);

    /**
     * 通过带参数的SQL插入数据
     * @param sql SQL语句
     * @param params 参数映射，key为参数序号(从1开始)，value为参数值
     * @return 影响行数
     */
    @Insert("${sql}")
    int insertBySqlWithParams(@Param("sql") String sql, @Param("params") Map<String, Object> params);

    /**
     * 通过SQL插入数据并返回自增ID
     * @param sql SQL语句
     * @param params 参数映射，key为参数序号(从1开始)，value为参数值
     * @return 自增ID
     */
    @Insert("${sql}")
    @Options(useGeneratedKeys = true, keyProperty = "userId", keyColumn = "user_id")
    Long insertBySqlReturnId(@Param("sql") String sql, @Param("params") Map<String, Object> params);
}
