<template>
	<view class="add-user-container">
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">用户信息</text>
				<text class="form-subtitle">请完善以下信息</text>
			</view>
			
			<view class="form-item">
				<text class="form-label">姓名/昵称</text>
				<input class="form-input" type="text" v-model="formData.userName" placeholder="请输入姓名/昵称" :disabled="formDisabled" />
			</view>
			
			<view class="form-item">
				<text class="form-label">性别</text>
				<picker class="form-picker" :disabled="formDisabled" @change="bindGenderChange" :value="genderIndex" :range="genderOptions" range-key="label">
					<view class="picker-content">
						<text>{{ genderOptions[genderIndex].label }}</text>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">出生年月</text>
				<picker class="form-picker" :disabled="formDisabled" mode="date" @change="bindDateChange" :value="formData.birthDate" fields="month">
					<view class="picker-content">
						<text>{{ formData.birthDate || '请选择出生年月' }}</text>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">手机号</text>
				<input class="form-input" type="text" v-model="formData.phone" placeholder="请输入手机号" :disabled="formDisabled" />
			</view>
			
			<!-- 修改所在省市选择器 -->
			<view class="form-item">
				<text class="form-label">所在省市</text>
				<picker class="form-picker" :disabled="formDisabled" @change="bindRegionChange" :value="regionIndex" :range="regions" range-key="region_name">
					<view class="picker-content">
						<text class="picker-text">{{ regionIndex >= 0 && regions.length > 0 ? regions[regionIndex].region_name : '请选择地区' }}</text>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</picker>
			</view>

			<view class="form-item">
				<text class="form-label">用户编号</text>
				<picker class="form-picker" :disabled="formDisabled" @change="bindUserCodeChange" :value="userCodeIndex" :range="userCodes" range-key="user_code">
					<view class="picker-content">
						<text class="picker-text">{{ userCodeIndex >= 0 && userCodes.length > 0 ? userCodes[userCodeIndex].user_code : '请选择用户编号' }}</text>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</picker>
			</view>
		</view>
		
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">店铺信息</text>
				<text class="form-subtitle">关联店铺与店员</text>
			</view>
			
			<view class="form-item">
				<text class="form-label">筛选字母</text>
				<picker class="form-picker" :disabled="formDisabled" @change="bindLetterChange" :value="letterIndex" :range="letters">
					<view class="picker-content">
						<text>{{ letters[letterIndex] || '请选择字母' }}</text>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">店铺编码</text>
				<picker class="form-picker" :disabled="formDisabled" @change="bindShopChange" :value="shopIndex" :range="shops" range-key="shop_code">
					<view class="picker-content">
						<text class="picker-text">{{ shopIndex >= 0 && shops.length > 0 ? shops[shopIndex].shop_code : '请选择店铺编码' }}</text>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">店员编号</text>
				<picker class="form-picker" :disabled="formDisabled || !selectedShopId" @change="bindStaffChange" :value="staffIndex" :range="staffs" range-key="staff_code">
					<view class="picker-content">
						<text class="picker-text">{{ staffIndex >= 0 && staffs.length > 0 ? staffs[staffIndex].staff_code : '请选择店员编号' }}</text>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</picker>
			</view>
		</view>
		
		<!-- 移除三级联动省市区选择器弹窗 -->

		<view class="button-container">
			<button class="btn btn-reset" @click="resetForm" :disabled="formDisabled">重置</button>
			<button class="btn btn-edit" @click="editForm" v-if="formDisabled">修改</button>
			<button class="btn btn-submit" @click="submitForm" :disabled="formDisabled">提交</button>
		</view>
	</view>
</template>

<script>
import { getShopList, getStaffListByShopId, getProvinces, getCities, getDistricts, saveUserData, getAvailableUserCodes, getRegions } from '@/api/work/usertotal.js';

export default {
	data() {
		return {
			formData: {
				userName: '',
				gender: '0',  // 默认为男性
				birthDate: '',
				phone: '',
				regionId: null,
				filteredLetter: '',
				shopId: null,
				staffId: null,
				userCode: '',
				codeId: null
			},
			formDisabled: false,
			
			// 性别选项
			genderOptions: [
				{ value: '0', label: '男' },
				{ value: '1', label: '女' },
				{ value: '2', label: '未知' }
			],
			genderIndex: 0,
			
			// 筛选字母
			letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
			letterIndex: 0,
			
			// 店铺和店员数据
			shops: [],
			shopIndex: -1,
			selectedShopId: null,
			staffs: [],
			staffIndex: -1,
			
			// 用户编号数据
			userCodes: [],
			userCodeIndex: -1,
			
			// 地区数据（单选）
			regions: [],
			regionIndex: -1,
			
			// 省市区数据 - 不再使用，但保留避免影响现有代码
			regionTabIndex: 0,
			provinces: [],
			cities: [],
			districts: [],
			currentRegionList: [],
			regionSelection: {
				province: {},
				city: {},
				district: {}
			},
			regionText: '',
			finalRegionId: null
		}
	},
	created() {
		// 尝试从localStorage加载上次填写的数据
		this.loadFromLocalStorage();
		
		// 获取地区数据
		this.fetchRegions();
		
		// 获取店铺列表
		this.fetchShopList();
		
		// 获取用户编号列表
		this.fetchUserCodes();
	},
	methods: {
		// 加载本地存储的数据
		loadFromLocalStorage() {
			try {
				const savedData = uni.getStorageSync('addUserFormData');
				if (savedData) {
					const parsedData = JSON.parse(savedData);
					this.formData = parsedData.formData || this.formData;
					this.formDisabled = parsedData.formDisabled || false;
					this.shopIndex = parsedData.shopIndex || -1;
					this.staffIndex = parsedData.staffIndex || -1;
					this.letterIndex = parsedData.letterIndex || 0;
					this.genderIndex = parsedData.genderIndex || 0;
					this.userCodeIndex = parsedData.userCodeIndex || -1;
					this.regionIndex = parsedData.regionIndex || -1;
					
					if (parsedData.selectedShopId) {
						this.selectedShopId = parsedData.selectedShopId;
						this.fetchStaffList(this.selectedShopId);
					}
				}
			} catch (e) {
				console.error('加载本地存储数据失败:', e);
			}
		},
		
		// 保存数据到本地存储
		saveToLocalStorage() {
			try {
				const dataToSave = {
					formData: this.formData,
					formDisabled: this.formDisabled,
					shopIndex: this.shopIndex,
					staffIndex: this.staffIndex,
					letterIndex: this.letterIndex,
					genderIndex: this.genderIndex,
					userCodeIndex: this.userCodeIndex,
					selectedShopId: this.selectedShopId,
					regionIndex: this.regionIndex
				};
				uni.setStorageSync('addUserFormData', JSON.stringify(dataToSave));
			} catch (e) {
				console.error('保存数据到本地存储失败:', e);
			}
		},
		
		// 获取店铺列表
		async fetchShopList() {
			try {
				const response = await getShopList();
				if (response && response.code === 200) {
					this.shops = response.data || [];
					// 如果已有选择的店铺ID，则找到对应的索引
					if (this.selectedShopId) {
						this.shopIndex = this.shops.findIndex(shop => shop.shop_id === this.selectedShopId);
					}
				}
			} catch (error) {
				console.error('获取店铺列表失败:', error);
				uni.showToast({
					title: '获取店铺列表失败',
					icon: 'none'
				});
			}
		},
		
		// 获取店员列表
		async fetchStaffList(shopId) {
			if (!shopId) return;
			
			try {
				const response = await getStaffListByShopId(shopId);
				if (response && response.code === 200) {
					this.staffs = response.data || [];
					// 如果已有选择的店员ID，则找到对应的索引
					if (this.formData.staffId) {
						this.staffIndex = this.staffs.findIndex(staff => staff.staff_id === this.formData.staffId);
					} else {
						this.staffIndex = -1;
					}
				}
			} catch (error) {
				console.error('获取店员列表失败:', error);
				uni.showToast({
					title: '获取店员列表失败',
					icon: 'none'
				});
			}
		},
		
		// 获取用户编号列表
		async fetchUserCodes() {
			try {
				const response = await getAvailableUserCodes();
				if (response && response.code === 200) {
					this.userCodes = response.data || [];
					// 如果已有选择的用户编号，则找到对应的索引
					if (this.formData.userCode) {
						this.userCodeIndex = this.userCodes.findIndex(code => code.user_code === this.formData.userCode);
					}
				}
			} catch (error) {
				console.error('获取用户编号列表失败:', error);
				uni.showToast({
					title: '获取用户编号列表失败',
					icon: 'none'
				});
			}
		},
		
		// 获取地区列表
		async fetchRegions() {
			try {
				const response = await getRegions();
				if (response && response.code === 200) {
					this.regions = response.data || [];
					// 如果已有选择的地区ID，则找到对应的索引
					if (this.formData.regionId) {
						this.regionIndex = this.regions.findIndex(region => region.region_id === this.formData.regionId);
					}
				}
			} catch (error) {
				console.error('获取地区列表失败:', error);
				uni.showToast({
					title: '获取地区列表失败',
					icon: 'none'
				});
			}
		},
		
		// 地区选择变更
		bindRegionChange(e) {
			this.regionIndex = e.detail.value;
			this.formData.regionId = this.regions[this.regionIndex].region_id;
		},
		
		// 性别选择变更
		bindGenderChange(e) {
			this.genderIndex = e.detail.value;
			this.formData.gender = this.genderOptions[this.genderIndex].value;
		},
		
		// 日期选择变更
		bindDateChange(e) {
			this.formData.birthDate = e.detail.value;
		},
		
		// 字母筛选变更
		bindLetterChange(e) {
			this.letterIndex = e.detail.value;
			this.formData.filteredLetter = this.letters[this.letterIndex];
		},
		
		// 用户编号选择变更
		bindUserCodeChange(e) {
			this.userCodeIndex = e.detail.value;
			const selectedUserCode = this.userCodes[this.userCodeIndex];
			this.formData.userCode = selectedUserCode.user_code;
			this.formData.codeId = selectedUserCode.code_id;
		},
		
		// 店铺选择变更
		bindShopChange(e) {
			this.shopIndex = e.detail.value;
			const selectedShop = this.shops[this.shopIndex];
			this.selectedShopId = selectedShop.shop_id;
			this.formData.shopId = selectedShop.shop_id;
			
			// 清空店员选择
			this.staffs = [];
			this.staffIndex = -1;
			this.formData.staffId = null;
			
			// 获取该店铺的店员列表
			this.fetchStaffList(selectedShop.shop_id);
		},
		
		// 店员选择变更
		bindStaffChange(e) {
			this.staffIndex = e.detail.value;
			this.formData.staffId = this.staffs[this.staffIndex].staff_id;
		},
		
		// 以下原三级联动相关方法保留，但不再使用
		
		// 显示地区选择弹窗 - 已不再使用，但保留以避免引用错误
		showRegionPicker() {
			// 此方法已不再使用，使用bindRegionChange替代
			console.log('原三级联动方法已弃用');
		},
		
		// 切换地区选择标签 - 不再使用
		switchRegionTab(index) {
			console.log('原三级联动方法已弃用');
		},
		
		// 选择地区项 - 不再使用
		selectRegionItem(item) {
			console.log('原三级联动方法已弃用');
		},
		
		// 取消地区选择 - 不再使用
		cancelRegionSelection() {
			console.log('原三级联动方法已弃用');
		},
		
		// 确认地区选择 - 不再使用
		confirmRegionSelection() {
			console.log('原三级联动方法已弃用');
		},
		
		// 重置表单
		resetForm() {
			this.formData = {
				userName: '',
				gender: '0',
				birthDate: '',
				phone: '',
				regionId: null,
				filteredLetter: '',
				shopId: null,
				staffId: null,
				userCode: '',
				codeId: null
			};
			this.genderIndex = 0;
			this.letterIndex = 0;
			this.shopIndex = -1;
			this.selectedShopId = null;
			this.staffs = [];
			this.staffIndex = -1;
			this.userCodeIndex = -1;
			this.regionIndex = -1;
			this.regionText = '';
			this.regionSelection = {
				province: {},
				city: {},
				district: {}
			};
			this.finalRegionId = null;
			
			// 清除本地存储
			uni.removeStorageSync('addUserFormData');
		},
		
		// 激活编辑模式
		editForm() {
			this.formDisabled = false;
			this.saveToLocalStorage();
		},
		
		// 提交表单
		async submitForm() {
			// 表单验证
			if (!this.formData.userName) {
				uni.showToast({
					title: '请输入姓名',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.birthDate) {
				uni.showToast({
					title: '请选择出生年月',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.phone) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.regionId) {
				uni.showToast({
					title: '请选择所在地区',
					icon: 'none'
				});
				return;
			}
			
			// 加强对用户编号选择的检查，确保已选择有效的用户编号
			if (this.userCodeIndex < 0 || !this.formData.userCode || this.userCodes.length === 0) {
				uni.showToast({
					title: '请选择用户编号',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.shopId) {
				uni.showToast({
					title: '请选择店铺',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.staffId) {
				uni.showToast({
					title: '请选择店员',
					icon: 'none'
				});
				return;
			}
			
			try {
				const submitData = {
					userName: this.formData.userName,
					gender: this.formData.gender,
					birthDate: this.formData.birthDate,
					phone: this.formData.phone,
					regionId: this.formData.regionId,
					shopId: this.formData.shopId,
					staffId: this.formData.staffId,
					filteredLetter: this.formData.filteredLetter,
					userCode: this.formData.userCode,
					codeId: this.formData.codeId
				};
				
				const response = await saveUserData(submitData);
				
				if (response && response.code === 200) {
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});
					
					// 禁用表单
					this.formDisabled = true;
					
					// 重置用户编号相关状态
					this.userCodeIndex = -1;
					this.formData.userCode = '';
					this.formData.codeId = null;
					
					// 重新获取用户编号列表（因为刚才选择的已被使用）
					await this.fetchUserCodes();
					
					this.saveToLocalStorage();
				} else {
					uni.showToast({
						title: response.msg || '提交失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('提交数据失败:', error);
				uni.showToast({
					title: response.msg,
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style>
@font-face {
  font-family: 'iconfont';  /* 可以自定义字体名称 */
  src: url('//at.alicdn.com/t/font_8d5l8fzk5b87iudi.woff') format('woff');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-right:before {
  content: "\e697";
}

.add-user-container {
	min-height: 100vh;
	background-color: #f7f8fa;
	padding: 20px 15px;
}

.form-card {
	background-color: #ffffff;
	border-radius: 12px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	padding: 20px;
	margin-bottom: 20px;
}

.form-header {
	margin-bottom: 20px;
}

.form-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5px;
}

.form-subtitle {
	font-size: 14px;
	color: #999;
}

.form-item {
	margin-bottom: 20px;
}

.form-label {
	display: block;
	font-size: 14px;
	color: #666;
	margin-bottom: 8px;
}

.form-input {
	width: 100%;
	height: 44px;
	background-color: #f5f7fa;
	border-radius: 8px;
	padding: 0 15px;
	font-size: 14px;
	color: #333;
	border: none;
}

.form-picker {
	width: 100%;
	height: 44px;
	background-color: #f5f7fa;
	border-radius: 8px;
	padding: 0 15px;
	font-size: 14px;
}

.picker-content {
	height: 44px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.picker-text {
	color: #333;
}

.button-container {
	display: flex;
	justify-content: space-between;
	margin-top: 30px;
}

.btn {
	flex: 1;
	height: 48px;
	border-radius: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	font-weight: 500;
	margin: 0 6px;
}

.btn-reset {
	background-color: #f5f7fa;
	color: #666;
	border: 1px solid #e0e0e0;
}

.btn-edit {
	background-color: #ff9500;
	color: #fff;
}

.btn-submit {
	background: linear-gradient(to right, #4a90e2, #1976d2);
	color: #fff;
}

.btn-submit[disabled] {
	background: linear-gradient(to right, #b0bec5, #90a4ae);
	color: rgba(255, 255, 255, 0.7);
}

/* 地区选择器样式 */
.region-popup {
	background-color: #fff;
	border-top-left-radius: 16px;
	border-top-right-radius: 16px;
	height: 60vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.region-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #f0f0f0;
}

.region-cancel {
	color: #999;
	font-size: 14px;
}

.region-title {
	color: #333;
	font-size: 16px;
	font-weight: 500;
}

.region-confirm {
	color: #4a90e2;
	font-size: 14px;
	font-weight: 500;
}

.region-tabs {
	display: flex;
	background-color: #f7f8fa;
	padding: 10px 0;
}

.region-tab-item {
	flex: 1;
	text-align: center;
	padding: 10px 0;
	font-size: 14px;
	color: #666;
	position: relative;
	transition: all 0.3s;
}

.region-tab-item.active {
	color: #4a90e2;
	font-weight: 500;
}

.region-tab-item.active:after {
	content: "";
	position: absolute;
	bottom: -10px;
	left: 50%;
	transform: translateX(-50%);
	width: 20px;
	height: 3px;
	background-color: #4a90e2;
	border-radius: 3px;
}

.region-list {
	flex: 1;
	overflow-y: auto;
}

.region-list-item {
	padding: 15px 20px;
	font-size: 14px;
	color: #333;
	border-bottom: 1px solid #f5f5f5;
}

.region-list-item:active {
	background-color: #f5f7fa;
}
</style>
