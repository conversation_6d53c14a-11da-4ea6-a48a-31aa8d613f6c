{"_from": "@babel/plugin-transform-classes@^7.23.8", "_id": "@babel/plugin-transform-classes@7.23.8", "_inBundle": false, "_integrity": "sha512-yAYslGsY1bX6Knmg46RjiCiNSwJKv2IUC8qOdYKqMMr0491SXFhcHqOdRDeCRohOOIzwN/90C6mQ9qAKgrP7dg==", "_location": "/@babel/plugin-transform-classes", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-classes@^7.23.8", "name": "@babel/plugin-transform-classes", "escapedName": "@babel%2fplugin-transform-classes", "scope": "@babel", "rawSpec": "^7.23.8", "saveSpec": null, "fetchSpec": "^7.23.8"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.23.8.tgz", "_shasum": "d08ae096c240347badd68cdf1b6d1624a6435d92", "_spec": "@babel/plugin-transform-classes@^7.23.8", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.23.6", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-function-name": "^7.23.0", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6", "globals": "^11.1.0"}, "deprecated": false, "description": "Compile ES2015 classes to ES5", "devDependencies": {"@babel/core": "^7.23.7", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.7"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-classes", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-classes"}, "type": "commonjs", "version": "7.23.8"}