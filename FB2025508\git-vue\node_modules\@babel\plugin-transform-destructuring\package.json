{"_from": "@babel/plugin-transform-destructuring@^7.23.3", "_id": "@babel/plugin-transform-destructuring@7.23.3", "_inBundle": false, "_integrity": "sha512-n225npDqjDIr967cMScVKHXJs7rout1q+tt50inyBCPkyZ8KxeI6d+GIbSBTT/w/9WdlWDOej3V9HE5Lgk57gw==", "_location": "/@babel/plugin-transform-destructuring", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-destructuring@^7.23.3", "name": "@babel/plugin-transform-destructuring", "escapedName": "@babel%2fplugin-transform-destructuring", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.23.3.tgz", "_shasum": "8c9ee68228b12ae3dff986e56ed1ba4f3c446311", "_spec": "@babel/plugin-transform-destructuring@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Compile ES2015 destructuring to ES5", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-destructuring", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-destructuring", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-destructuring"}, "type": "commonjs", "version": "7.23.3"}