{"_from": "@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.10.4", "_inBundle": false, "_integrity": "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==", "_location": "/@babel/plugin-syntax-logical-assignment-operators", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "name": "@babel/plugin-syntax-logical-assignment-operators", "escapedName": "@babel%2fplugin-syntax-logical-assignment-operators", "scope": "@babel", "rawSpec": "^7.10.4", "saveSpec": null, "fetchSpec": "^7.10.4"}, "_requiredBy": ["/@babel/plugin-transform-logical-assignment-operators", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "_shasum": "ca91ef46303530448b906652bac2e9fe9941f699", "_spec": "@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "deprecated": false, "description": "Allow parsing of the logical assignment operators", "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-logical-assignment-operators", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-logical-assignment-operators"}, "version": "7.10.4"}