{"_from": "@babel/helper-split-export-declaration@^7.22.6", "_id": "@babel/helper-split-export-declaration@7.22.6", "_inBundle": false, "_integrity": "sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==", "_location": "/@babel/helper-split-export-declaration", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-split-export-declaration@^7.22.6", "name": "@babel/helper-split-export-declaration", "escapedName": "@babel%2fhelper-split-export-declaration", "scope": "@babel", "rawSpec": "^7.22.6", "saveSpec": null, "fetchSpec": "^7.22.6"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-module-transforms", "/@babel/plugin-transform-classes", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "_shasum": "322c61b7310c0997fe4c323955667f18fcefb91c", "_spec": "@babel/helper-split-export-declaration@^7.22.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-module-transforms", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.5"}, "deprecated": false, "description": ">", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-split-export-declaration", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-split-export-declaration", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-split-export-declaration"}, "type": "commonjs", "version": "7.22.6"}