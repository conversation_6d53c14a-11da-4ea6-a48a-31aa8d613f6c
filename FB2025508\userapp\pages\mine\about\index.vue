<template>
	<view class="container">
		<view class="header">
			<view class="title">门店订单统计</view>
		</view>
		
		<!-- 顶部加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-spinner"></view>
			<text>数据加载中...</text>
		</view>
		
		<!-- 统计卡片 -->
		<view v-else class="stats-container">
			<!-- 今日订单统计 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">今日订单统计 ({{ currentDate }})</text>
				</view>
				<view class="card-content">
					<view class="stat-row">
						<view class="stat-item">
							<text class="stat-label">订单量</text>
							<text class="stat-value">{{ formatNumber(todayStat.order_count) }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">总收入</text>
							<text class="stat-value">¥{{ formatCurrency(todayStat.total_amount) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 本周订单统计 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">本周订单统计</text>
					<text class="card-subtitle">{{ weekRange }}</text>
				</view>
				<view class="card-content">
					<view class="stat-row">
						<view class="stat-item">
							<text class="stat-label">订单量</text>
							<text class="stat-value">{{ formatNumber(weekStat.order_count) }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">总收入</text>
							<text class="stat-value">¥{{ formatCurrency(weekStat.total_amount) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 本月订单统计 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">本月订单统计 ({{ currentMonth }})</text>
				</view>
				<view class="card-content">
					<view class="stat-row">
						<view class="stat-item">
							<text class="stat-label">订单量</text>
							<text class="stat-value">{{ formatNumber(monthStat.order_count) }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">总收入</text>
							<text class="stat-value">¥{{ formatCurrency(monthStat.total_amount) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 本年订单统计 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">本年订单统计 ({{ currentYear }})</text>
				</view>
				<view class="card-content">
					<view class="stat-row">
						<view class="stat-item">
							<text class="stat-label">订单量</text>
							<text class="stat-value">{{ formatNumber(yearStat.order_count) }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">总收入</text>
							<text class="stat-value">¥{{ formatCurrency(yearStat.total_amount) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 近6个月门店订单量 -->
			<view class="stats-card">
				<view class="card-header">
					<text class="card-title">近6个月门店订单量</text>
				</view>
				<view class="card-content">
					<view class="chart-container">
						<view class="chart-labels">
							<text v-for="(item, index) in last6MonthsStats" :key="index" class="chart-label">{{ formatMonthYear(item.month_year) }}</text>
						</view>
						<view class="chart-bars">
							<view 
								v-for="(item, index) in last6MonthsStats" 
								:key="index" 
								class="chart-bar" 
								:style="{ height: calculateBarHeight(item.order_count) }"
							>
								<text class="bar-value">{{ item.order_count }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 历史年度数据 -->
			<block v-for="(yearData, yearIndex) in visibleYearlyStats" :key="yearIndex">
				<view class="stats-card historical">
					<view class="card-header">
						<text class="card-title">{{ yearData.year }}年订单统计</text>
					</view>
					<view class="card-content">
						<view class="stat-row">
							<view class="stat-item">
								<text class="stat-label">订单量</text>
								<text class="stat-value">{{ formatNumber(yearData.order_count) }}</text>
							</view>
							<view class="stat-item">
								<text class="stat-label">总收入</text>
								<text class="stat-value">¥{{ formatCurrency(yearData.total_amount) }}</text>
							</view>
						</view>
					</view>
				</view>
			</block>
			
			<!-- 显示更多按钮 -->
			<view v-if="hasMoreYearlyStats" class="show-more" @click="showMore">
				<text>显示更多历史数据</text>
				<view class="arrow-down"></view>
			</view>
			
			<!-- 底部提示 -->
			<view class="footer-tip" v-if="!loading && !hasMoreYearlyStats && yearlyStats.length > 0">
				<text>已显示全部历史数据</text>
			</view>
			
			<!-- 无数据提示 -->
			<view v-if="!loading && noDataAtAll" class="no-data-container">
				<text>暂无任何订单统计数据</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getShopOrderStats } from '@/api/work/retrun_part.js';

export default {
	data() {
		return {
			loading: true,
			currentDate: '',
			weekRange: '',
			currentMonth: '',
			currentYear: '',
			todayStat: { order_count: 0, total_amount: 0 },
			weekStat: { order_count: 0, total_amount: 0 },
			monthStat: { order_count: 0, total_amount: 0 },
			yearStat: { order_count: 0, total_amount: 0 },
			last6MonthsStats: [],
			yearlyStats: [],
			visibleYearlyStats: [],
			displayedYearCount: 1, // 初始显示1年的历史数据
			maxYearsPerPage: 1 // 每次"显示更多"增加的年份数量
		};
	},
	computed: {
		hasMoreYearlyStats() {
			return this.visibleYearlyStats.length < this.yearlyStats.length;
		},
		noDataAtAll() {
			return (
				parseInt(this.todayStat.order_count || 0) === 0 && 
				parseInt(this.weekStat.order_count || 0) === 0 && 
				parseInt(this.monthStat.order_count || 0) === 0 && 
				parseInt(this.yearStat.order_count || 0) === 0 && 
				this.yearlyStats.length === 0
			);
		}
	},
	onLoad() {
		this.fetchOrderStats();
	},
	methods: {
		async fetchOrderStats() {
			this.loading = true;
			
			try {
				const res = await getShopOrderStats();
				
				if (res.code === 200 && res.data) {
					// 更新日期信息
					this.currentDate = res.data.currentDate;
					this.weekRange = res.data.weekRange;
					this.currentMonth = res.data.currentMonth;
					this.currentYear = res.data.currentYear;
					
					// 更新统计数据
					this.todayStat = res.data.today || { order_count: 0, total_amount: 0 };
					this.weekStat = res.data.week || { order_count: 0, total_amount: 0 };
					this.monthStat = res.data.month || { order_count: 0, total_amount: 0 };
					this.yearStat = res.data.year || { order_count: 0, total_amount: 0 };
					this.last6MonthsStats = res.data.last6Months || [];
					
					// 按年份降序排序（新年份在前）
					this.yearlyStats = (res.data.yearlyStats || []).sort((a, b) => {
						return parseInt(b.year) - parseInt(a.year);
					});
					
					// 初始化显示的历史数据
					this.updateVisibleYearlyStats();
				} else {
					uni.showToast({
						title: res.msg || '获取统计数据失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取订单统计数据失败', error);
				uni.showToast({
					title: '获取统计数据失败，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 更新显示的历史年份数据
		updateVisibleYearlyStats() {
			this.visibleYearlyStats = this.yearlyStats.slice(0, this.displayedYearCount);
		},
		
		// 显示更多历史数据
		showMore() {
			this.displayedYearCount += this.maxYearsPerPage;
			// 确保不超出数组范围
			if (this.displayedYearCount > this.yearlyStats.length) {
				this.displayedYearCount = this.yearlyStats.length;
			}
			this.updateVisibleYearlyStats();
		},
		
		// 格式化数字（添加千位分隔符）
		formatNumber(num) {
			if (num === undefined || num === null) return '0';
			return parseInt(num).toLocaleString();
		},
		
		// 格式化货币（保留2位小数）
		formatCurrency(num) {
			if (num === undefined || num === null) return '0.00';
			return parseFloat(num).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
		},
		
		// 格式化月份年份显示（如：2023-06 -> 6月）
		formatMonthYear(monthYear) {
			if (!monthYear) return '';
			const parts = monthYear.split('-');
			if (parts.length !== 2) return monthYear;
			
			// 把月份转为数字并显示（去掉前导零）
			const month = parseInt(parts[1]);
			return `${month}月`;
		},
		
		// 计算柱状图的高度
		calculateBarHeight(count) {
			// 找到最大订单量，用于计算比例
			const maxCount = Math.max(...this.last6MonthsStats.map(item => parseInt(item.order_count || 0)));
			if (maxCount === 0) return '10rpx'; // 如果都是0，给一个最小高度
			
			// 计算高度比例，最高150rpx
			const height = Math.max(10, (parseInt(count || 0) / maxCount) * 150);
			return `${height}rpx`;
		}
	}
};
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	box-sizing: border-box;
}

.header {
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	color: #333;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300rpx;
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #3c96f3;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
}

.stats-container {
	margin-bottom: 40rpx;
}

.stats-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	
	&.historical {
		background-color: #f9f9f9;
	}
}

.card-header {
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 16rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.card-subtitle {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-top: 6rpx;
}

.card-content {
	padding: 10rpx 0;
}

.stat-row {
	display: flex;
	justify-content: space-between;
	padding: 10rpx 0;
}

.stat-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10rpx;
}

.stat-label {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.stat-value {
	font-size: 36rpx;
	color: #3c96f3;
	font-weight: bold;
}

.chart-container {
	margin-top: 20rpx;
	padding: 10rpx 0;
}

.chart-labels {
	display: flex;
	justify-content: space-around;
	margin-bottom: 10rpx;
}

.chart-label {
	font-size: 22rpx;
	color: #666;
	text-align: center;
	width: 16%;
}

.chart-bars {
	display: flex;
	justify-content: space-around;
	align-items: flex-end;
	height: 180rpx;
}

.chart-bar {
	width: 12%;
	background-color: #3c96f3;
	border-radius: 6rpx 6rpx 0 0;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	padding-top: 6rpx;
	transition: height 0.3s;
	min-height: 10rpx;
}

.bar-value {
	font-size: 20rpx;
	color: #fff;
	transform: translateY(-24rpx);
}

.show-more {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	margin: 10rpx 0;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	color: #3c96f3;
	font-size: 28rpx;
	
	.arrow-down {
		width: 0;
		height: 0;
		border-left: 12rpx solid transparent;
		border-right: 12rpx solid transparent;
		border-top: 12rpx solid #3c96f3;
		margin-top: 10rpx;
	}
}

.footer-tip {
	text-align: center;
	color: #999;
	font-size: 26rpx;
	margin: 20rpx 0;
}

.no-data-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 300rpx;
	color: #999;
	font-size: 28rpx;
}
</style>
