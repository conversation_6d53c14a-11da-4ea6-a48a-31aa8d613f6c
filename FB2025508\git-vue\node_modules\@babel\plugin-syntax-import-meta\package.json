{"_from": "@babel/plugin-syntax-import-meta@^7.10.4", "_id": "@babel/plugin-syntax-import-meta@7.10.4", "_inBundle": false, "_integrity": "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==", "_location": "/@babel/plugin-syntax-import-meta", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-import-meta@^7.10.4", "name": "@babel/plugin-syntax-import-meta", "escapedName": "@babel%2fplugin-syntax-import-meta", "scope": "@babel", "rawSpec": "^7.10.4", "saveSpec": null, "fetchSpec": "^7.10.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "_shasum": "ee601348c370fa334d2207be158777496521fd51", "_spec": "@babel/plugin-syntax-import-meta@^7.10.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "deprecated": false, "description": "Allow parsing of import.meta", "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-import-meta", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-meta"}, "version": "7.10.4"}