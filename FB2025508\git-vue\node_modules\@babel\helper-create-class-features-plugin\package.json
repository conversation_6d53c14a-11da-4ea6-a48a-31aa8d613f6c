{"_from": "@babel/helper-create-class-features-plugin@^7.18.6", "_id": "@babel/helper-create-class-features-plugin@7.24.0", "_inBundle": false, "_integrity": "sha512-QAH+vfvts51BCsNZ2PhY6HAggnlS6omLLFTsIpeqZk/MmJ6cW7tgz5yRv0fMJThcr6FmbMrENh1RgrWPTYA76g==", "_location": "/@babel/helper-create-class-features-plugin", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-create-class-features-plugin@^7.18.6", "name": "@babel/helper-create-class-features-plugin", "escapedName": "@babel%2fhelper-create-class-features-plugin", "scope": "@babel", "rawSpec": "^7.18.6", "saveSpec": null, "fetchSpec": "^7.18.6"}, "_requiredBy": ["/@babel/plugin-proposal-class-properties", "/@babel/plugin-proposal-decorators", "/@babel/plugin-transform-class-properties", "/@babel/plugin-transform-class-static-block", "/@babel/plugin-transform-private-methods", "/@babel/plugin-transform-private-property-in-object"], "_resolved": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.0.tgz", "_shasum": "fc7554141bdbfa2d17f7b4b80153b9b090e5d158", "_spec": "@babel/helper-create-class-features-plugin@^7.18.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\plugin-proposal-class-properties", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-function-name": "^7.23.0", "@babel/helper-member-expression-to-functions": "^7.23.0", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-replace-supers": "^7.22.20", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "semver": "^6.3.1"}, "deprecated": false, "description": "Compile class public and private fields, private methods and decorators to ES6", "devDependencies": {"@babel/core": "^7.24.0", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/preset-env": "^7.24.0", "@types/charcodes": "^0.2.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-create-class-features-plugin", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-class-features-plugin"}, "type": "commonjs", "version": "7.24.0"}