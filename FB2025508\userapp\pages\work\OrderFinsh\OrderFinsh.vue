<template>
	<view>
		<view class="new-order-container">
			<!-- 页面头部 -->
			<view class="header">
				<view class="city-select">
					<text>{{cityName}}</text>
					<text class="iconfont icon-arrow-down"></text>
				</view>
				<!-- <view class="header-buttons">
					<button class="btn btn-primary btn-with-icon" @click="createOrder"><text class="iconfont icon-plus"></text>新建订单</button>
					<button class="btn btn-outline" @click="clearAll">清空</button>
				</view> -->
			</view>
			
			<!-- 选项卡行 -->
			<view class="option-rows">
				<!-- 第一行选项 - 男生店铺/店员/客户 -->
				<view class="option-row">
					<view class="option-indicator">
						<view class="option-dot red-dot"></view>
					</view>
					<view class="dropdown-group">
						<!-- 字母筛选下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedMaleLetter || 'A'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
						
						<!-- 店铺下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedMaleShop ? selectedMaleShop.shop_code : '请选择店铺'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
						
						<!-- 店员下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedMaleStaff ? selectedMaleStaff.staff_code : '请选择店员'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
						
						<!-- 用户下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedMaleUser ? selectedMaleUser.user_code : '请选择用户'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 第二行选项 - 女生店铺/店员/客户 -->
				<view class="option-row">
					<view class="option-indicator">
						<view class="option-dot green-dot"></view>
					</view>
					<view class="dropdown-group">
						<!-- 字母筛选下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedFemaleLetter || 'A'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
						
						<!-- 店铺下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedFemaleShop ? selectedFemaleShop.shop_code : '请选择店铺'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
						
						<!-- 店员下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedFemaleStaff ? selectedFemaleStaff.staff_code : '请选择店员'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
						
						<!-- 用户下拉框 -->
						<view class="dropdown-wrapper">
							<view class="dropdown-label">
								{{selectedFemaleUser ? selectedFemaleUser.user_code : '请选择用户'}}
								<text class="iconfont icon-arrow-down"></text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 数据展示区域 -->
			<view class="data-display-area">
				<view class="empty-state" v-if="!selectedMaleUser && !selectedFemaleUser">
					<text class="iconfont icon-data"></text>
					<text class="placeholder-text">数据展示区</text>
					<text class="sub-text">请选择至少一位用户后将显示用户信息</text>
				</view>
				<view class="user-info-display" v-else>
					<view class="user-card male-card">
						<view class="user-header">
							<text class="user-gender">男</text>
							<text class="user-code">{{selectedMaleUser ? selectedMaleUser.user_code : '未选择'}}</text>
						</view>
						<view class="user-detail" v-if="selectedMaleUser">
							<view class="detail-item staff-info">
								<text class="label">男生代理：</text>
								<text class="value">{{selectedMaleStaff ? selectedMaleStaff.staff_name : '--'}}</text>
								<text class="value phone">{{selectedMaleStaff ? selectedMaleStaff.phone : '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">姓名：</text>
								<text class="value">{{maleUserDetail.user_name || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">性别：</text>
								<text class="value">{{maleUserDetail.gender === '0' ? '男' : '女'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">出生年月：</text>
								<text class="value">{{maleUserDetail.birth_date || '--'}} ({{maleUserDetail.age || '--'}}岁)</text>
							</view>
							<view class="detail-item">
								<text class="label">身高：</text>
								<text class="value">{{maleUserDetail.height || '--'}}cm</text>
							</view>
							<view class="detail-item">
								<text class="label">体重：</text>
								<text class="value">{{maleUserDetail.weight || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">毕业院校：</text>
								<text class="value">{{maleUserDetail.education_school || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">工作单位：</text>
								<text class="value">{{maleUserDetail.work_unit || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">职业：</text>
								<text class="value">{{maleUserDetail.occupation_name || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">爱好：</text>
								<text class="value">{{maleUserDetail.hobbies || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">对女生的希望：</text>
								<text class="value">{{maleUserDetail.expectations || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">微信号：</text>
								<text class="value">{{maleUserDetail.wechat_id || '--'}}</text>
							</view>
							<view class="user-avatar" v-if="maleUserDetail.avatar">
								<image :src="maleUserDetail.avatar" mode="aspectFill"></image>
							</view>
							<view class="user-avatar empty-avatar" v-else>
								<text>暂无头像</text>
							</view>
						</view>
						<view class="user-detail" v-else>
							<text class="user-name">请选择男用户</text>
						</view>
					</view>
					<view class="user-card female-card">
						<view class="user-header">
							<text class="user-gender">女</text>
							<text class="user-code">{{selectedFemaleUser ? selectedFemaleUser.user_code : '未选择'}}</text>
						</view>
						<view class="user-detail" v-if="selectedFemaleUser">
							<view class="detail-item staff-info">
								<text class="label">女生代理：</text>
								<text class="value">{{selectedFemaleStaff ? selectedFemaleStaff.staff_name : '--'}}</text>
								<text class="value phone">{{selectedFemaleStaff ? selectedFemaleStaff.phone : '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">姓名：</text>
								<text class="value">{{femaleUserDetail.user_name || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">性别：</text>
								<text class="value">{{femaleUserDetail.gender === '0' ? '男' : '女'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">出生年月：</text>
								<text class="value">{{femaleUserDetail.birth_date || '--'}} ({{femaleUserDetail.age || '--'}}岁)</text>
							</view>
							<view class="detail-item">
								<text class="label">身高：</text>
								<text class="value">{{femaleUserDetail.height || '--'}}cm</text>
							</view>
							<view class="detail-item">
								<text class="label">体重：</text>
								<text class="value">{{femaleUserDetail.weight || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">毕业院校：</text>
								<text class="value">{{femaleUserDetail.education_school || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">工作单位：</text>
								<text class="value">{{femaleUserDetail.work_unit || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">职业：</text>
								<text class="value">{{femaleUserDetail.occupation_name || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">爱好：</text>
								<text class="value">{{femaleUserDetail.hobbies || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">对男生的希望：</text>
								<text class="value">{{femaleUserDetail.expectations || '--'}}</text>
							</view>
							<view class="detail-item">
								<text class="label">微信号：</text>
								<text class="value">{{femaleUserDetail.wechat_id || '--'}}</text>
							</view>
							<view class="user-avatar" v-if="femaleUserDetail.avatar">
								<image :src="femaleUserDetail.avatar" mode="aspectFill"></image>
							</view>
							<view class="user-avatar empty-avatar" v-else>
								<text>暂无头像</text>
							</view>
						</view>
						<view class="user-detail" v-else>
							<text class="user-name">请选择女用户</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 订单信息区域 -->
			<view class="order-info-area" v-if="showOrderInfo && orderInfo">
				<view class="order-info-card">
					<view class="order-info-title">
						<text>订单信息</text>
					</view>
					<view class="order-info-content">
						<view class="order-info-item">
							<text class="label">订单号：</text>
							<text class="value">{{orderInfo.orderNo || '--'}}</text>
						</view>
						<view class="order-info-item">
							<text class="label">订单时间：</text>
							<text class="value">{{orderInfo.orderTime || '--'}}</text>
						</view>
						<view class="order-info-item">
							<text class="label">订单状态：</text>
							<text class="value">{{orderInfo.status || '--'}}</text>
						</view>
						<view class="order-info-item">
							<text class="label">订单周期：</text>
							<text class="value">{{orderInfo.orderDays || '0'}}天/{{orderInfo.orderMonths || '0.00'}}月</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 订单阶段列表区域 -->
			<view class="order-stages-area" v-if="showOrderInfo && orderStages.length > 0">
				<view class="order-stage-card" v-for="(stage, stageIndex) in orderStages" :key="stageIndex">
					<view class="stage-header" :class="{'stage-header-pink': stage.isPink}">
						<text>{{stage.stageName}}</text>
					</view>
					
					<!-- 订单结束总结区域 -->
					<view class="order-summary-area" v-if="isOrderEndStage(stage)">
						<view class="order-summary-box">
							<view class="summary-title">
								<text>男生代理总结（{{selectedMaleStaff ? selectedMaleStaff.staff_name : '无'}}）</text>
								<text class="summary-count">{{maleSummary.length}}/50</text>
							</view>
							<view class="summary-readonly">{{maleSummary || '暂无总结内容'}}</view>
						</view>
						<view class="order-summary-box">
							<view class="summary-title">
								<text>女生代理总结（{{selectedFemaleStaff ? selectedFemaleStaff.staff_name : '无'}}）</text>
								<text class="summary-count">{{femaleSummary.length}}/50</text>
							</view>
							<view class="summary-readonly">{{femaleSummary || '暂无总结内容'}}</view>
						</view>
					</view>
					
					<!-- 该阶段下的服务列表 - 非结束阶段才显示 -->
					<view class="stage-services" v-if="getStageServices(stage.stageId).length > 0 && !isOrderEndStage(stage)">
						<view class="order-service-card" v-for="(service, index) in getStageServices(stage.stageId)" :key="index">
							<view class="service-header">
								<view class="service-info">
									<text class="service-name">{{service.serviceName}}</text>
									<text class="service-staff">({{service.staffNickname}})</text>
									<text class="service-price" v-if="service.price > 0">({{service.price}})</text>
								</view>
								<view class="service-time">{{service.serviceTime}}</view>
							</view>
						</view>
					</view>
					
					<!-- 男女同意区域 - 非结束阶段才显示 -->
					<view class="gender-agreement-area" v-if="!isOrderEndStage(stage)">
						<view class="gender-agreement-box">
							<text class="agreement-title">女生是否同意</text>
							<view class="agreement-options">
								<view class="custom-option" :class="{'option-selected': stage.femaleAgreed === true}">
									<view class="custom-radio">
										<view v-if="stage.femaleAgreed === true" class="radio-dot agree-dot"></view>
									</view>
									<text>同意</text>
								</view>
								<view class="custom-option" :class="{'option-selected': stage.femaleAgreed === false}">
									<view class="custom-radio">
										<view v-if="stage.femaleAgreed === false" class="radio-dot disagree-dot"></view>
									</view>
									<text>不同意</text>
								</view>
							</view>
						</view>
						<view class="gender-agreement-box">
							<text class="agreement-title">男生是否同意</text>
							<view class="agreement-options">
								<view class="custom-option" :class="{'option-selected': stage.maleAgreed === true}">
									<view class="custom-radio">
										<view v-if="stage.maleAgreed === true" class="radio-dot agree-dot"></view>
									</view>
									<text>同意</text>
								</view>
								<view class="custom-option" :class="{'option-selected': stage.maleAgreed === false}">
									<view class="custom-radio">
										<view v-if="stage.maleAgreed === false" class="radio-dot disagree-dot"></view>
									</view>
									<text>不同意</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 页面尾部 -->
			<view class="footer">
				<!-- 账户余额信息 -->
				<view class="account-info-card">
					<view class="account-balance">
						<text class="label">男生账户余额：</text>
						<text class="value">{{maleBalance}} <text class="quota">({{maleQuota}})</text></text>
					</view>
					<view class="account-balance">
						<text class="label">女生账户余额：</text>
						<text class="value">{{femaleBalance}} <text class="quota">({{femaleQuota}})</text></text>
					</view>
				</view>
			</view>
			
			<!-- 只读模式覆盖层 -->
			<!-- <view class="readonly-overlay">
				<view class="readonly-notice">
					<text class="readonly-icon">🔒</text>
					<text class="readonly-text">只读模式</text>
				</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	import { 
		getCurrentStaffInfo, 
		getShopsByLetter, 
		getStaffsByShopId, 
		getUsersByStaffId, 
		getUserDetail,
		createOrder,
		validateOrder,
		getCurrentOrderInfoFinish,
		getShopAndStaffInfo,
		getOrderStages,
		getOrderStaffs,
		saveOrderStage,
		getStageInfo,
		deleteOrderStage,
		checkStageCanDelete,
		getServiceItems,
		saveOrderService,
		getOrderServices,
		deleteOrderService,
		notifyColleague,
		cancelNotifyColleague,
		resetAllNotifications,
		getAllOrderStages,
		batchCheckServiceCanDelete,
		checkServiceCanDelete,
		getOrderSummary,
		saveOrderSummary,
		lockOrderFinish,
		updateDeleteAgreement,
		freezeOrder
	} from '@/api/work/appuser.js'
	
	export default {
		data() {
			return {
				// 城市信息
				cityName: '沈阳市',
				regionId: null,
				
				// 订单编号
				orderNo: '',
				
				// 用户操作标记
				userInitiated: false,
				
				// 男生相关数据
				maleShops: [], // 所有男生店铺
				selectedMaleShop: null,
				maleStaffs: [],
				selectedMaleStaff: null,
				maleUsers: [],
				selectedMaleUser: null,
				maleUserDetail: {},
				selectedMaleLetter: 'A', // 默认选中字母A
				showMaleLetterPicker: false,
				showMaleShopPicker: false,
				showMaleStaffPicker: false,
				showMaleUserPicker: false,
				
				// 女生相关数据
				femaleShops: [], // 所有女生店铺
				selectedFemaleShop: null,
				femaleStaffs: [],
				selectedFemaleStaff: null,
				femaleUsers: [],
				selectedFemaleUser: null,
				femaleUserDetail: {},
				selectedFemaleLetter: 'A', // 默认选中字母A
				showFemaleLetterPicker: false,
				showFemaleShopPicker: false,
				showFemaleStaffPicker: false,
				showFemaleUserPicker: false,
				
				// 账户余额
				maleBalance: 0,
				maleQuota: 150,
				femaleBalance: '--',
				femaleQuota: '--',
				
				// 代理同意状态
				maleAgentApproved: false,
				femaleAgentApproved: false,
				
				// 是否可以冻结订单
				canFreezeOrder: false,
				
				// 是否是男/女代理
				isMaleAgent: false,
				isFemaleAgent: false,
				
				// 字母选项
				maleLettersToShow: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
				femaleLettersToShow: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
				
				// 订单信息
				orderInfo: null,
				showOrderInfo: false,
				
				// 是否正在加载订单回显
				isLoadingOrder: false,
				
				// 新建阶段相关数据
				showStageModal: false,
				selectedStage: null,
				showStageList: false,
				selectedStageStaff: null,
				showStaffList: false,
				orderStaffList: [],
				stageList: [],
				orderStages: [], // 现在这个数组将存储多个阶段
				
				// 当前登录的店员ID
				currentStaffId: null,
				
				// 是否有权限删除阶段
				canDeleteStage: false,
				
				// 新建服务相关数据
				showServiceModal: false,
				selectedService: null,
				showServiceList: false,
				selectedServiceStaff: null,
				showServiceStaffList: false,
				serviceList: [],
				orderServices: [],
				currentStage: null,
				
				// 当前点击的阶段ID
				currentClickedStageId: null,
				
				// 订单总结相关数据
				maleSummary: '',
				femaleSummary: '',
				maleSummaryEdited: false,
				femaleSummaryEdited: false,
				
				// 是否可以锁单
				canLockOrder: false,
				
				// 订单结束阶段ID
				orderEndStageId: null,
				
				// 当前阶段ID（最新添加的阶段）
				currentActiveStageId: null
			}
		},
		computed: {
			// 根据选中字母筛选男生店铺
			filteredMaleShops() {
				if (!this.selectedMaleLetter) return this.maleShops;
				return this.maleShops.filter(shop => 
					shop.shop_code.startsWith(this.selectedMaleLetter)
				);
			},
			// 根据选中字母筛选女生店铺
			filteredFemaleShops() {
				if (!this.selectedFemaleLetter) return this.femaleShops;
				return this.femaleShops.filter(shop => 
					shop.shop_code.startsWith(this.selectedFemaleLetter)
				);
			}
		},
		mounted() {
			// 获取当前店员信息和城市
			this.getStaffInfo()
			
			// 添加点击事件监听器，用于关闭下拉菜单
			document.addEventListener('click', this.closeAllDropdowns)
			
			// 获取路由参数中的订单号
			const eventChannel = this.getOpenerEventChannel();
			const orderNo = this.$route ? this.$route.query.orderNo : '';
			console.log("caonima"+orderNo);
			// 从页面参数中获取订单号
			if (this.$mp && this.$mp.query && this.$mp.query.orderNo) {
				this.orderNo = this.$mp.query.orderNo;
				// 加载订单信息（回显）
				this.loadOrderInfo(this.orderNo);
			} else if (orderNo) {
				this.orderNo = orderNo;
				// 加载订单信息（回显）
				this.loadOrderInfo(this.orderNo);
			} else {
				// 如果没有获取到订单号，尝试从上一页面获取
				try {
					eventChannel && eventChannel.on('acceptDataFromOpener', (data) => {
						if (data && data.orderNo) {
							this.orderNo = data.orderNo;
							// 加载订单信息（回显）
							this.loadOrderInfo(this.orderNo);
						} else {
							// 没有订单号，直接加载订单信息
							this.loadOrderInfo();
						}
					});
				} catch (e) {
					console.error('获取上一页面数据失败:', e);
					// 没有订单号，直接加载订单信息
					this.loadOrderInfo();
				}
			}
		},
		beforeDestroy() {
			// 移除事件监听器
			document.removeEventListener('click', this.closeAllDropdowns)
		},
		watch: {
			// 监听订单总结内容变化，更新锁单按钮状态
			maleSummary() {
				this.updateLockOrderStatus();
			},
			femaleSummary() {
				this.updateLockOrderStatus();
			},
			// 监听订单阶段变化，更新锁单按钮状态
			orderStages: {
				handler() {
					this.updateLockOrderStatus();
				},
				deep: true
			}
		},
		methods: {
			// 关闭所有下拉菜单
			closeAllDropdowns(event) {
				// 如果点击的是下拉菜单本身，不处理
				if (event && event.target && event.target.closest && event.target.closest('.dropdown-wrapper')) {
					return;
				}
				
				this.showMaleLetterPicker = false;
				this.showMaleShopPicker = false;
				this.showMaleStaffPicker = false;
				this.showMaleUserPicker = false;
				this.showFemaleLetterPicker = false;
				this.showFemaleShopPicker = false;
				this.showFemaleStaffPicker = false;
				this.showFemaleUserPicker = false;
			},
			
			// 显示下拉框
			showDropdown(type, gender) {
				// 先关闭所有下拉框
				this.closeAllDropdowns();
				
				// 打开指定的下拉框
				if (gender === 'male') {
					if (type === 'letter') {
						this.showMaleLetterPicker = true;
					} else if (type === 'shop') {
						this.showMaleShopPicker = true;
					} else if (type === 'staff') {
						this.showMaleStaffPicker = true;
					} else if (type === 'user') {
						this.showMaleUserPicker = true;
					}
				} else {
					if (type === 'letter') {
						this.showFemaleLetterPicker = true;
					} else if (type === 'shop') {
						this.showFemaleShopPicker = true;
					} else if (type === 'staff') {
						this.showFemaleStaffPicker = true;
					} else if (type === 'user') {
						this.showFemaleUserPicker = true;
					}
				}
				
				// 阻止事件冒泡
				event.stopPropagation();
			},
			
			// 选择字母
			selectLetter(letter, gender) {
				// 标记为用户主动操作
				this.userInitiated = true;
				
				if (gender === 'male') {
					this.selectedMaleLetter = letter;
					this.showMaleLetterPicker = false;
					// 重置店铺、店员、用户选择
					this.selectedMaleShop = null;
					this.selectedMaleStaff = null;
					this.selectedMaleUser = null;
					this.maleStaffs = [];
					this.maleUsers = [];
					// 加载该字母对应的店铺
					this.loadShopsByLetter(letter, 'male');
					
					// 自动展开店铺选择
					this.showMaleShopPicker = true;
					
					// 如果只有一个店铺，自动选择
					setTimeout(() => {
						if (this.filteredMaleShops.length === 1) {
							this.selectShop(this.filteredMaleShops[0], 'male');
						}
					}, 300);
				} else {
					this.selectedFemaleLetter = letter;
					this.showFemaleLetterPicker = false;
					// 重置店铺、店员、用户选择
					this.selectedFemaleShop = null;
					this.selectedFemaleStaff = null;
					this.selectedFemaleUser = null;
					this.femaleStaffs = [];
					this.femaleUsers = [];
					// 加载该字母对应的店铺
					this.loadShopsByLetter(letter, 'female');
					
					// 自动展开店铺选择
					this.showFemaleShopPicker = true;
					
					// 如果只有一个店铺，自动选择
					setTimeout(() => {
						if (this.filteredFemaleShops.length === 1) {
							this.selectShop(this.filteredFemaleShops[0], 'female');
						}
					}, 300);
				}
			},
			
			// 获取当前店员信息和城市
			getStaffInfo() {
				getCurrentStaffInfo().then(res => {
					if (res.code === 200) {
						const data = res.data
						this.cityName = data.region_name
						this.regionId = data.region_id
						
						// 保存当前店员ID
						this.currentStaffId = data.staff_id
						
						// 获取到regionId后加载所有店铺，但不自动展开下拉框
						this.userInitiated = false; // 标记为非用户主动操作
						this.loadShopsByLetter('A', 'male');
						this.loadShopsByLetter('A', 'female');
						
						// 初始化判断用户是男代理还是女代理
						this.checkAgentGender();
					} else {
						uni.showToast({
							title: '获取店员信息失败',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取店员信息失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 加载订单信息（回显）
			loadOrderInfo(orderNo) {
				this.isLoadingOrder = true;
				getCurrentOrderInfoFinish(orderNo).then(res => {
					if (res.code === 200) {
						// 保存订单信息
						this.orderInfo = res.data;
						this.showOrderInfo = true;
						// 获取当前阶段ID（如果有）
						if (res.data.maleOrder && res.data.maleOrder.current_stage_id) {
							this.orderInfo.currentStageId = res.data.maleOrder.current_stage_id;
						} else if (res.data.femaleOrder && res.data.femaleOrder.current_stage_id) {
							this.orderInfo.currentStageId = res.data.femaleOrder.current_stage_id;
						}
						
						// 回显订单相关的用户、店员、店铺信息
						this.loadOrderRelatedInfo();
						
						// 清空现有阶段列表
						this.orderStages = [];
						
						// 清空订单总结
						this.maleSummary = '';
						this.femaleSummary = '';
						
						// 获取男性用户的阶段
						if (this.orderInfo.maleOrder && this.orderInfo.maleOrder.current_stage_id) {
							 
							this.loadCurrentStageName(this.orderInfo.maleOrder.current_stage_id,Boolean(Number(this.orderInfo.maleOrder.malestatus)),Boolean(Number(this.orderInfo.maleOrder.femalestatus)));
						}
						
						// 获取女性用户的阶段
						if (this.orderInfo.femaleOrder && this.orderInfo.femaleOrder.current_stage_id) {
							// 如果与男性用户的阶段不同，则加载
							if (!this.orderInfo.maleOrder || 
								!this.orderInfo.maleOrder.current_stage_id || 
								this.orderInfo.maleOrder.current_stage_id !== this.orderInfo.femaleOrder.current_stage_id) {
								this.loadCurrentStageName(this.orderInfo.femaleOrder.current_stage_id,Boolean(Number(this.orderInfo.femaleOrder.malestatus)),Boolean(Number(this.orderInfo.femaleOrder.femalestatus)));
							}
						}
						
						// 查询该订单的所有阶段
						this.loadAllOrderStages();
												
						// 加载订单服务列表
						this.loadOrderServices();
						// 加载订单总结内容
						this.loadOrderSummary();
						
						// 更新锁单按钮状态
						this.updateLockOrderStatus();
					} else {
						console.log('没有找到有效订单或获取订单失败:', res.msg);
						this.isLoadingOrder = false;
					}
				}).catch(err => {
					console.error('获取订单信息失败:', err);
					this.isLoadingOrder = false;
				});
			},
			
			// 加载所有现有阶段
			loadExistingStages() {
				if (!this.orderInfo || !this.orderInfo.orderNo) return;
				
				// 清空现有阶段列表
				this.orderStages = [];
				
				// 获取男性用户的阶段
				if (this.orderInfo.maleOrder && this.orderInfo.maleOrder.current_stage_id) {
					this.loadCurrentStageName(this.orderInfo.maleOrder.current_stage_id);
					// 设置当前活跃阶段
					this.currentActiveStageId = this.orderInfo.maleOrder.current_stage_id;
				}
				
				// 获取女性用户的阶段
				if (this.orderInfo.femaleOrder && this.orderInfo.femaleOrder.current_stage_id) {
					// 如果与男性用户的阶段不同，则加载
					if (!this.orderInfo.maleOrder || 
						!this.orderInfo.maleOrder.current_stage_id || 
						this.orderInfo.maleOrder.current_stage_id !== this.orderInfo.femaleOrder.current_stage_id) {
						this.loadCurrentStageName(this.orderInfo.femaleOrder.current_stage_id);
						// 如果没有设置活跃阶段，设置为女性阶段
						if (!this.currentActiveStageId) {
							this.currentActiveStageId = this.orderInfo.femaleOrder.current_stage_id;
						}
					}
				}
				
				// 查询该订单的所有阶段
				this.loadAllOrderStages();
			},
			
			// 加载订单的所有阶段
			loadAllOrderStages() {
				if (!this.orderInfo || !this.orderInfo.orderNo) return;
				
				// 设置加载状态
				uni.showLoading({
					title: '加载阶段信息...'
				});
				
				// 调用API获取订单的所有阶段
				getAllOrderStages(this.orderInfo.orderNo,this.currentStaffId).then(res => {
					uni.hideLoading();
					if (res.code === 200 && res.data && res.data.length > 0) {
						// 处理返回的阶段数据
						res.data.forEach(stageData => {
							// 检查是否已经加载了该阶段
							// console.log("aaaa="+JSON.stringify(stageData));
							// console.log("bbbb="+JSON.stringify(this.orderStages));
							const existingStage = this.orderStages.find(s => s.stageId === stageData.stage_id);
							
							if (!existingStage) {
								// 创建新阶段对象，从后端返回的数据中提取malestatus和femalestatus
								const newStage = {
									stageId: stageData.stage_id,
									stageName: stageData.stage_name,
									staffId: stageData.staff_id,
									isPink: true,
									canDelete: false, // 默认不可删除，后续检查权限
									maleAgreed: stageData.malestatus === "1", // 从后端数据中获取男生同意状态
									femaleAgreed: stageData.femalestatus === "1", // 从后端数据中获取女生同意状态
									// 保存原始数据，以便getStageAgreementStatus方法使用
									malestatus: stageData.malestatus,
									femalestatus: stageData.femalestatus
								};
								// 添加到阶段列表
								this.orderStages.push(newStage);

								// 检查是否有权限删除阶段
								this.checkDeletePermission(stageData.stage_id, newStage);
							}
						});
						
						// 对阶段进行排序 - 按照添加顺序排序，最新添加的放在最下方
						// 由于后端返回的数据已经是按照添加顺序排列的，这里不需要额外排序
						
						// 如果当前没有设置活跃阶段，但有阶段数据，则将最后一个阶段设为活跃
						if (!this.currentActiveStageId && this.orderStages.length > 0) {
							this.currentActiveStageId = this.orderStages[this.orderStages.length - 1].stageId;
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取订单阶段失败', err);
				});
			},
			
			// 加载订单相关的用户、店员、店铺信息
			loadOrderRelatedInfo() {
				if (!this.orderInfo) return;
				
				// 加载男性用户相关信息
				this.loadMaleUserRelatedInfo();
				
				// 加载女性用户相关信息
				this.loadFemaleUserRelatedInfo();
			},
			
			// 加载男性用户相关信息
			loadMaleUserRelatedInfo() {
				if (!this.orderInfo || !this.orderInfo.maleUserId || !this.orderInfo.maleStaffId || !this.orderInfo.maleShopId) return;
				
				// 1. 获取店铺信息
				getShopAndStaffInfo(this.orderInfo.maleStaffId).then(res => {
					if (res.code === 200) {
						const shopInfo = res.data;
						
						// 设置男性用户的字母筛选
						if (shopInfo.shop_code) {
							// 提取店铺编码的第一个字母
							const firstLetter = shopInfo.shop_code.charAt(0);
							if (/[A-Za-z]/.test(firstLetter)) {
								this.selectedMaleLetter = firstLetter.toUpperCase();
							}
						}
						
						// 加载店铺列表
						this.loadShopsByLetter(this.selectedMaleLetter, 'male', () => {
							// 在店铺列表加载完成后，选择对应的店铺
							const shop = this.maleShops.find(s => s.shop_id == shopInfo.shop_id);
							if (shop) {
								this.selectedMaleShop = shop;
								
								// 加载店员列表
								this.loadStaffsByShopId(shop.shop_id, 'male', () => {
									// 在店员列表加载完成后，选择对应的店员
									const staff = this.maleStaffs.find(s => s.staff_id == this.orderInfo.maleStaffId);
									if (staff) {
										this.selectedMaleStaff = staff;
										
										// 加载用户列表
										this.loadUsersByStaffId(staff.staff_id, 'male', () => {
											// 在用户列表加载完成后，选择对应的用户
											const user = this.maleUsers.find(u => u.user_id == this.orderInfo.maleUserId);
											
											if (user) {
												this.selectedMaleUser = user;
												
												// 加载用户详情
												this.getUserDetails(user.user_id, 'male');
											}
										});
									}
								});
							}
						});
					}
				});
			},
			
			// 加载女性用户相关信息
			loadFemaleUserRelatedInfo() {
				if (!this.orderInfo || !this.orderInfo.femaleUserId || !this.orderInfo.femaleStaffId || !this.orderInfo.femaleShopId) return;
				
				// 1. 获取店铺信息
				getShopAndStaffInfo(this.orderInfo.femaleStaffId).then(res => {
					if (res.code === 200) {
						const shopInfo = res.data;
						
						// 设置女性用户的字母筛选
						if (shopInfo.shop_code) {
							// 提取店铺编码的第一个字母
							const firstLetter = shopInfo.shop_code.charAt(0);
							if (/[A-Za-z]/.test(firstLetter)) {
								this.selectedFemaleLetter = firstLetter.toUpperCase();
							}
						}
						
						// 加载店铺列表
						this.loadShopsByLetter(this.selectedFemaleLetter, 'female', () => {
							// 在店铺列表加载完成后，选择对应的店铺
							const shop = this.femaleShops.find(s => s.shop_id == shopInfo.shop_id);
							if (shop) {
								this.selectedFemaleShop = shop;
								
								// 加载店员列表
								this.loadStaffsByShopId(shop.shop_id, 'female', () => {
									// 在店员列表加载完成后，选择对应的店员
									const staff = this.femaleStaffs.find(s => s.staff_id == this.orderInfo.femaleStaffId);
									if (staff) {
										this.selectedFemaleStaff = staff;
										
										// 加载用户列表
										this.loadUsersByStaffId(staff.staff_id, 'female', () => {
											// 在用户列表加载完成后，选择对应的用户
											const user = this.femaleUsers.find(u => u.user_id == this.orderInfo.femaleUserId);
											if (user) {
												this.selectedFemaleUser = user;
												
												// 加载用户详情
												this.getUserDetails(user.user_id, 'female');
												
																		// 所有数据加载完成
						this.isLoadingOrder = false;
						
						// 检查当前登录用户是男代理还是女代理
						this.checkAgentGender();
						
						// 获取订单删除同意状态
						this.getOrderDeleteStatus();
											}
										});
									}
								});
							}
						});
					}
				});
			},
			
			// 根据字母加载店铺，支持回调函数
			loadShopsByLetter(letter, gender, callback) {
				if (!this.regionId) {
					uni.showToast({
						title: '未获取到城市信息',
						icon: 'none'
					});
					return;
				}
				
				// 设置加载状态
				uni.showLoading({
					title: '加载中...'
				});
				
				// 调用API从数据库获取匹配字母的店铺
				getShopsByLetter({
					letter: letter,
					regionId: this.regionId
				}).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						const shopList = res.data || [];
						// 根据gender参数在前端筛选店铺，实际业务中可能需要调整
						if (gender === 'male') {
							this.maleShops = shopList;
						} else {
							this.femaleShops = shopList;
						}
						
						// 只在用户主动选择字母时自动选择唯一店铺，而不是页面初次加载时
						if (this.userInitiated) {
							setTimeout(() => {
								if (gender === 'male' && this.filteredMaleShops.length === 1) {
									this.selectShop(this.filteredMaleShops[0], 'male');
								} else if (gender === 'female' && this.filteredFemaleShops.length === 1) {
									this.selectShop(this.filteredFemaleShops[0], 'female');
								}
							}, 300);
						}
						
						// 如果提供了回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					} else {
						uni.showToast({
							title: res.msg || '获取店铺失败',
							icon: 'none'
						});
						
						// 出错时使用备用的模拟数据以便测试
						if (gender === 'male') {
							if (letter === 'A') {
								this.maleShops = [{ shop_id: 1, shop_code: 'AA1', shop_name: '店铺AA1' }];
							} else if (letter === 'B') {
								this.maleShops = [
									{ shop_id: 3, shop_code: 'B01', shop_name: '店铺B01' },
									{ shop_id: 4, shop_code: 'BB1', shop_name: '店铺BB1' }
								];
							} else {
								this.maleShops = [
									{ shop_id: Math.floor(Math.random() * 1000), shop_code: letter + '01', shop_name: '店铺' + letter + '01' }
								];
							}
						} else {
							if (letter === 'B') {
								this.femaleShops = [{ shop_id: 6, shop_code: 'BB05', shop_name: '店铺BB05' }];
							} else {
								this.femaleShops = [
									{ shop_id: Math.floor(Math.random() * 1000), shop_code: letter + '02', shop_name: '店铺' + letter + '02' }
								];
							}
						}
						
						// 如果提供了回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取店铺失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 加载店员数据，支持回调函数
			loadStaffsByShopId(shopId, gender, callback) {
				// 设置加载状态
				uni.showLoading({
					title: '加载店员...'
				});
				
				// 调用API从数据库获取店员
				getStaffsByShopId({
					shopId: shopId
				}).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						const staffList = res.data || [];
						if (gender === 'male') {
							this.maleStaffs = staffList;
							
							// 只有在非订单回显时才自动展开店员选择
							if (!this.isLoadingOrder) {
								this.showMaleStaffPicker = true;
								
								// 如果只有一个店员，自动选择
								if (staffList.length === 1) {
									this.selectStaff(staffList[0], 'male');
								}
							}
						} else {
							this.femaleStaffs = staffList;
							
							// 只有在非订单回显时才自动展开店员选择
							if (!this.isLoadingOrder) {
								this.showFemaleStaffPicker = true;
								
								// 如果只有一个店员，自动选择
								if (staffList.length === 1) {
									this.selectStaff(staffList[0], 'female');
								}
							}
						}
						
						// 如果提供了回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					} else {
						uni.showToast({
							title: res.msg || '获取店员失败',
							icon: 'none'
						});
						
						// 出错时使用备用的模拟数据以便测试
						if (gender === 'male') {
							this.maleStaffs = [
								{ staff_id: 1, staff_code: 'AA12', staff_name: '店员1' },
								{ staff_id: 2, staff_code: 'Q1', staff_name: '店员2' }
							];
							this.showMaleStaffPicker = true;
						} else {
							this.femaleStaffs = [
								{ staff_id: 3, staff_code: 'BB05', staff_name: '店员3' },
								{ staff_id: 4, staff_code: 'Q2', staff_name: '店员4' }
							];
							this.showFemaleStaffPicker = true;
						}
						
						// 如果提供了回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取店员失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 加载用户数据，支持回调函数
			loadUsersByStaffId(staffId, gender, callback) {
				// 设置加载状态
				uni.showLoading({
					title: '加载用户...'
				});
				
				// 调用API从数据库获取用户
				getUsersByStaffId({
					staffId: staffId
				}).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						const userList = res.data || [];
						if (gender === 'male') {
							this.maleUsers = userList;
							
							// 只有在非订单回显时才自动展开用户选择
							if (!this.isLoadingOrder) {
								this.showMaleUserPicker = true;
								
								// 如果只有一个用户，自动选择
								if (userList.length === 1) {
									this.selectUser(userList[0], 'male');
								}
							}
						} else {
							this.femaleUsers = userList;
							
							// 只有在非订单回显时才自动展开用户选择
							if (!this.isLoadingOrder) {
								this.showFemaleUserPicker = true;
								
								// 如果只有一个用户，自动选择
								if (userList.length === 1) {
									this.selectUser(userList[0], 'female');
								}
							}
						}
						
						// 如果提供了回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					} else {
						uni.showToast({
							title: res.msg || '获取用户失败',
							icon: 'none'
						});
						
						// 出错时使用备用的模拟数据以便测试
						if (gender === 'male') {
							this.maleUsers = [
								{ user_id: 1, user_code: '01#', user_name: '用户1' }
							];
							this.showMaleUserPicker = true;
						} else {
							this.femaleUsers = [
								{ user_id: 2, user_code: '02#', user_name: '用户2' }
							];
							this.showFemaleUserPicker = true;
						}
						
						// 如果提供了回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取用户失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 选择店铺
			selectShop(shop, gender) {
				if (gender === 'male') {
					this.selectedMaleShop = shop;
					this.selectedMaleStaff = null;
					this.selectedMaleUser = null;
					this.maleStaffs = [];
					this.maleUsers = [];
					this.showMaleShopPicker = false;
					
					// 加载店员数据
					this.loadStaffsByShopId(shop.shop_id, 'male');
				} else {
					this.selectedFemaleShop = shop;
					this.selectedFemaleStaff = null;
					this.selectedFemaleUser = null;
					this.femaleStaffs = [];
					this.femaleUsers = [];
					this.showFemaleShopPicker = false;
					
					// 加载店员数据
					this.loadStaffsByShopId(shop.shop_id, 'female');
				}
			},
			
			// 选择店员
			selectStaff(staff, gender) {
				if (gender === 'male') {
					this.selectedMaleStaff = staff;
					this.selectedMaleUser = null;
					this.maleUsers = [];
					this.showMaleStaffPicker = false;
					
					// 加载用户数据
					this.loadUsersByStaffId(staff.staff_id, 'male');
				} else {
					this.selectedFemaleStaff = staff;
					this.selectedFemaleUser = null;
					this.femaleUsers = [];
					this.showFemaleStaffPicker = false;
					
					// 加载用户数据
					this.loadUsersByStaffId(staff.staff_id, 'female');
				}
			},
			
			// 选择用户
			selectUser(user, gender) {
				// 性别校验：确保男生位置选择男性用户(gender='0')，女生位置选择女性用户(gender='1')
				if ((gender === 'male' && user.gender !== '0') || (gender === 'female' && user.gender !== '1')) {
					uni.showToast({
						title: gender === 'male' ? '请选择男性用户' : '请选择女性用户',
						icon: 'none'
					});
					return;
				}
				
				// 检查是否已选择了同性别的用户
				if (gender === 'male' && this.selectedFemaleUser && this.selectedFemaleUser.gender === '0') {
					uni.showToast({
						title: '已选择了一位男性用户，请选择女性用户',
						icon: 'none'
					});
					return;
				}
				
				if (gender === 'female' && this.selectedMaleUser && this.selectedMaleUser.gender === '1') {
					uni.showToast({
						title: '已选择了一位女性用户，请选择男性用户',
						icon: 'none'
					});
					return;
				}
				
				if (gender === 'male') {
					this.selectedMaleUser = user;
					this.showMaleUserPicker = false;
					
					// 获取用户详情
					this.getUserDetails(user.user_id, 'male');
				} else {
					this.selectedFemaleUser = user;
					this.showFemaleUserPicker = false;
					
					// 获取用户详情
					this.getUserDetails(user.user_id, 'female');
				}
			},
			
			// 获取用户详情
			getUserDetails(userId, gender) {
				// 设置加载状态
				uni.showLoading({
					title: '加载用户详情...'
				});
				
				// 调用API从数据库获取用户详情
				getUserDetail({
					userId: userId
				}).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						const userDetail = res.data || {};
						if (gender === 'male') {
							this.maleUserDetail = userDetail;
							this.maleBalance = userDetail.balance || 0;
						} else {
							this.femaleUserDetail = userDetail;
							this.femaleBalance = userDetail.balance || '--';
						}
					} else {
						uni.showToast({
							title: res.msg || '获取用户详情失败',
							icon: 'none'
						});
						
						// 出错时使用备用的模拟数据以便测试
						if (gender === 'male') {
							this.maleUserDetail = {
								user_name: '用户1',
								age: 28,
								occupation_name: '工程师',
								balance: 100
							};
							this.maleBalance = this.maleUserDetail.balance;
						} else {
							this.femaleUserDetail = {
								user_name: '用户2',
								age: 26,
								occupation_name: '教师',
								balance: 200
							};
							this.femaleBalance = this.femaleUserDetail.balance;
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取用户详情失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 创建订单
			createOrder() {
				if (!this.selectedMaleUser || !this.selectedFemaleUser) {
					uni.showToast({
						title: '请先选择男女用户',
						icon: 'none'
					})
					return
				}
				
				// if (!this.maleAgentApproved || !this.femaleAgentApproved) {
				// 	uni.showToast({
				// 		title: '需要男女代理都同意才能创建订单',
				// 		icon: 'none'
				// 	})
				// 	return
				// }
				
				// 验证是否可以创建订单
				validateOrder(this.selectedMaleUser.user_id, this.selectedFemaleUser.user_id).then(res => {
					if (res.code === 200) {
						// 可以创建订单
						const orderData = {
							currentStaffId:this.currentStaffId,
							maleUserId: this.selectedMaleUser.user_id,
							femaleUserId: this.selectedFemaleUser.user_id,
							maleStaffId: this.selectedMaleStaff.staff_id,
							femaleStaffId: this.selectedFemaleStaff.staff_id,
							maleShopId: this.selectedMaleShop.shop_id,
							femaleShopId: this.selectedFemaleShop.shop_id,
							regionId: this.regionId
						}
						
						createOrder(orderData).then(res => {
							if (res.code === 200) {
								uni.showToast({
									title: '订单创建成功',
									icon: 'success'
								})
								
								// 保存订单信息并显示
								this.orderInfo = res.data;
								this.showOrderInfo = true;
								
								// 不再清空选择
								// this.clearAll()
							} else {
								uni.showToast({
									title: res.msg || '创建订单失败',
									icon: 'none'
								})
							}
						})
					} else {
						uni.showToast({
							title: res.msg || '验证失败',
							icon: 'none'
						})
					}
				})
			},
			
			// 清空所有选择
			clearAll() {
				location.reload();
				// 重置字母选择
				// this.selectedMaleLetter = 'A'
				// this.selectedFemaleLetter = 'A'
				
				// // 清空男生数据
				// this.maleShops = []
				// this.selectedMaleShop = null
				// this.maleStaffs = []
				// this.selectedMaleStaff = null
				// this.maleUsers = []
				// this.selectedMaleUser = null
				// this.maleUserDetail = {}
				
				// // 清空女生数据
				// this.femaleShops = []
				// this.selectedFemaleShop = null
				// this.femaleStaffs = []
				// this.selectedFemaleStaff = null
				// this.femaleUsers = []
				// this.selectedFemaleUser = null
				// this.femaleUserDetail = {}
				
				// // 重置余额
				// this.maleBalance = 0
				// this.femaleBalance = '--'
				
				// // 清空订单信息
				// this.orderInfo = null
				// this.showOrderInfo = false
				
				// // 清空订单总结
				// this.maleSummary = '';
				// this.femaleSummary = '';
				
				// // 清空阶段列表
				// this.orderStages = [];
				// this.stage = [];
				// // 更新锁单按钮状态
				// this.updateLockOrderStatus();
				
				// // 重新加载初始数据
				// this.loadShopsByLetter('A', 'male')
				// this.loadShopsByLetter('A', 'female')
			},
			
			// 新建阶段
			createStage() {
				if (!this.orderInfo || !this.orderInfo.orderNo) {
					uni.showToast({
						title: '请先创建或选择订单',
						icon: 'none'
					});
					return;
				}
				
				// 先强制刷新阶段列表缓存
				this.forceRefreshStageList();
				
				// 清空之前选择的阶段
				this.selectedStage = null;
				
				// 加载阶段列表 - 使用回调确保数据加载完成后再显示弹窗
				this.loadOrderStagesWithCallback(() => {
					// 加载店员列表
					this.loadOrderStaffs();
					
					// 检查是否还有可选阶段
					if (this.stageList.length === 0) {
						uni.showToast({
							title: '没有可选阶段，所有阶段已创建',
							icon: 'none'
						});
						return;
					}
					
					// 显示弹窗
					this.showStageModal = true;
					
					console.log('打开新建阶段弹窗，当前有', this.stageList.length, '个可选阶段');
				});
			},
			
			// 加载阶段列表
			loadOrderStages() {
				this.loadOrderStagesWithCallback();
			},
			
			// 加载阶段列表并支持回调
			loadOrderStagesWithCallback(callback) {
				// 设置加载状态
				uni.showLoading({
					title: '加载阶段数据...'
				});
				
				// 获取当前订单的阶段ID
				let currentStageId = null;
				
				// 如果订单中有阶段ID，则获取下一阶段的选项
				if (this.orderInfo && this.orderInfo.currentStageId) {
					currentStageId = this.orderInfo.currentStageId;
				}
				
				// 调用API获取订单阶段列表
				getOrderStages(currentStageId).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						// 获取已创建阶段的ID列表
						const existingStageIds = this.orderStages.map(stage => stage.stageId);
						
						// 过滤掉已经创建的阶段
						const filteredStages = (res.data || []).filter(stage => 
							!existingStageIds.includes(stage.stage_id)
						);
						
						console.log('过滤前阶段数:', res.data ? res.data.length : 0, '过滤后阶段数:', filteredStages.length);
						
						this.stageList = filteredStages;
						
						// 默认选择第一个阶段
						if (this.stageList.length > 0) {
							this.selectedStage = this.stageList[0];
						} else {
							// 如果没有可选阶段，清空选择
							this.selectedStage = null;
						}
						
						// 如果有回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					} else {
						uni.showToast({
							title: res.msg || '获取阶段列表失败',
							icon: 'none'
						});
						
						// 使用模拟数据
						let mockStages = [
							{ stage_id: 1, stage_name: '匹配阶段', order_num: 1 },
							{ stage_id: 2, stage_name: '广告阶段', order_num: 2 },
							{ stage_id: 3, stage_name: '微聊阶段', order_num: 3 },
							{ stage_id: 4, stage_name: '约会阶段', order_num: 4 },
							{ stage_id: 5, stage_name: '交往阶段', order_num: 5 },
							{ stage_id: 6, stage_name: '婚礼筹备阶段', order_num: 6 },
							{ stage_id: 7, stage_name: '订单结束阶段', order_num: 7 }
						];
						
						// 获取已创建阶段的ID列表
						const existingStageIds = this.orderStages.map(stage => stage.stageId);
						
						// 过滤掉已经创建的阶段
						mockStages = mockStages.filter(stage => 
							!existingStageIds.includes(stage.stage_id)
						);
						
						this.stageList = mockStages;
						
						// 默认选择第一个阶段
						if (this.stageList.length > 0) {
							this.selectedStage = this.stageList[0];
						} else {
							// 如果没有可选阶段，清空选择
							this.selectedStage = null;
						}
						
						// 如果有回调函数，执行它
						if (typeof callback === 'function') {
							callback();
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取阶段列表失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
					
					// 即使出错，也尝试执行回调
					if (typeof callback === 'function') {
						callback();
					}
				});
			},
			
			// 加载订单相关的店员信息
			loadOrderStaffs() {
				if (!this.orderInfo || !this.orderInfo.orderNo) return;
				
				// 设置加载状态
				uni.showLoading({
					title: '加载店员数据...'
				});
				
				// 调用API获取订单相关的店员信息
				getOrderStaffs(this.orderInfo.orderNo).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						this.orderStaffList = res.data || [];
						
						// 默认选择第一个店员
						if (this.orderStaffList.length > 0) {
							this.selectedStageStaff = this.orderStaffList[0];
							// 也为服务弹窗设置默认店员
							this.selectedServiceStaff = this.orderStaffList[0];
						}
					} else {
						uni.showToast({
							title: res.msg || '获取店员信息失败',
							icon: 'none'
						});
						
						// 使用模拟数据
						this.orderStaffList = [
							{ staff_id: 1, staff_name: '高蒂', nickname: '高蒂', phone: '13800138000' },
							{ staff_id: 2, staff_name: '李宇', nickname: '李宇', phone: '13900139000' }
						];
						
						// 默认选择第一个店员
						if (this.orderStaffList.length > 0) {
							this.selectedStageStaff = this.orderStaffList[0];
							// 也为服务弹窗设置默认店员
							this.selectedServiceStaff = this.orderStaffList[0];
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取店员信息失败', err);
					
					// 如果提供了回调函数，执行它
					if (typeof callback === 'function') {
						callback();
					}
				});
			},
			
			// 锁定订单
			lockOrder() {
				// 再次检查是否可以锁单
				if (!this.canLockOrder) {
					let message = '';
					if (!this.orderInfo || !this.orderInfo.orderNo) {
						message = '请先创建订单';
					} else if (!this.hasOrderEndStage()) {
						message = '必须添加"订单结束总结"阶段才能锁单';
					} else if (!this.maleSummary.trim() || !this.femaleSummary.trim()) {
						message = '请先完成男女双方的订单总结';
					}
					
					uni.showToast({
						title: message || '当前无法锁单',
						icon: 'none'
					});
					return;
				}
				
				// 显示确认框
				uni.showModal({
					title: '确认锁单',
					content: '锁单后，订单将变为已完成状态，且无法再编辑。确定要锁单吗？',
					success: (res) => {
						if (res.confirm) {
							// 提交锁单请求
							this.submitLockOrder();
						}
					}
				});
			},
			
			// 提交锁单请求
			submitLockOrder() {
				// 设置加载状态
				uni.showLoading({
					title: '处理中...'
				});
				
				// 获取订单结束阶段ID
				const endStageId = this.getOrderEndStageId();
				
				const orderData = {
					orderNo: this.orderInfo.orderNo,
					maleSummary: this.maleSummary,
					femaleSummary: this.femaleSummary,
					staffId: this.currentStaffId,
					endStageId: endStageId
				};
				
				// 调用锁单API
				lockOrderFinish(orderData).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '锁单成功，订单已完成',
							icon: 'success'
						});
						
						// 保存订单信息
						if (res.data) {
							this.orderInfo = res.data;
						}
						
						// 调用清空方法
						setTimeout(() => {
							this.clearAll();
						}, 1500);
					} else {
						uni.showToast({
							title: res.msg || '锁单失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('锁单失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 刷新数据
			refreshData() {
				// 刷新整个页面
				location.reload();
			},
			
			// 保存订单
			saveOrder() {
				uni.showToast({
					title: '保存功能待实现',
					icon: 'none'
				})
			},
			
			// 更新女生代理同意状态
			updateFemaleAgentApproval(e) {
				if (!this.orderInfo || !this.orderInfo.orderNo) {
					uni.showToast({
						title: '请先创建订单',
						icon: 'none'
					});
					return;
				}
				
				// 更新同意状态
				this.updateDeleteAgreement('female', e.detail.value);
			},
			
			// 更新男生代理同意状态
			updateMaleAgentApproval(e) {
				if (!this.orderInfo || !this.orderInfo.orderNo) {
					uni.showToast({
						title: '请先创建订单',
						icon: 'none'
					});
					return;
				}
				
				// 更新同意状态
				this.updateDeleteAgreement('male', e.detail.value);
			},
			
			// 更新删除订单同意状态
			updateDeleteAgreement(gender, isAgree) {
				// 设置加载状态
				uni.showLoading({
					title: '更新中...'
				});
				
				const data = {
					orderNo: this.orderInfo.orderNo,
					staffId: this.currentStaffId,
					gender: gender,
					isAgree: isAgree ? '1' : '0'
				};
				
				// 调用API更新同意状态
				updateDeleteAgreement(data).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '更新成功',
							icon: 'success'
						});
						
						// 更新本地状态
						if (gender === 'male') {
							this.maleAgentApproved = isAgree;
						} else {
							this.femaleAgentApproved = isAgree;
						}
						
						// 检查是否可以冻结订单
						this.checkCanFreezeOrder();
					} else {
						uni.showToast({
							title: res.msg || '更新失败',
							icon: 'none'
						});
						
						// 失败时恢复状态
						if (gender === 'male') {
							this.maleAgentApproved = !isAgree;
						} else {
							this.femaleAgentApproved = !isAgree;
						}
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('更新同意状态失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
					
					// 失败时恢复状态
					if (gender === 'male') {
						this.maleAgentApproved = !isAgree;
					} else {
						this.femaleAgentApproved = !isAgree;
					}
				});
			},
			
			// 检查是否可以冻结订单
			checkCanFreezeOrder() {
				this.canFreezeOrder = this.maleAgentApproved && this.femaleAgentApproved;
			},
			
			// 冻结订单操作
			freezeOrderAction() {
				if (!this.canFreezeOrder) {
					uni.showToast({
						title: '需要男女代理都同意才能冻结订单',
						icon: 'none'
					});
					return;
				}
				
				// 弹出确认框
				uni.showModal({
					title: '确认冻结',
					content: '确定要冻结此订单吗？冻结后订单将无法继续操作。',
					success: (res) => {
						if (res.confirm) {
							this.submitFreezeOrder();
						}
					}
				});
			},
			
			// 提交冻结订单
			submitFreezeOrder() {
				// 设置加载状态
				uni.showLoading({
					title: '处理中...'
				});
				
				const data = {
					orderNo: this.orderInfo.orderNo,
					staffId: this.currentStaffId
				};
				
				// 调用冻结订单API
				freezeOrder(data).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '订单冻结成功',
							icon: 'success'
						});
						
						// 更新订单状态
						if (this.orderInfo) {
							this.orderInfo.status = '2'; // 已冻结
						}
						
						// 延迟后清空数据
						setTimeout(() => {
							this.clearAll();
						}, 1500);
					} else {
						uni.showToast({
							title: res.msg || '冻结失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('冻结订单失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 新建阶段弹窗关闭
			closeStageModal() {
				this.showStageModal = false;
				this.showStageList = false;
				this.showStaffList = false;
				this.selectedStage = null;
				this.selectedStageStaff = null;
				
				// 清空阶段列表，确保下次打开时重新获取
				this.stageList = [];
			},
			
			// 新建阶段弹窗确定
			confirmCreateStage() {
				// 确保选择了阶段
				if (!this.selectedStage) {
					uni.showToast({
						title: '请选择阶段',
						icon: 'none'
					});
					return;
				}
				
				if (!this.selectedStageStaff) {
					uni.showToast({
						title: '请选择店员',
						icon: 'none'
					});
					return;
				}
				
				// 检查阶段是否已经创建过
				const existingStage = this.orderStages.find(stage => stage.stageId === this.selectedStage.stage_id);
				if (existingStage) {
					uni.showToast({
						title: '该阶段已创建，请选择其他阶段',
						icon: 'none'
					});
					return;
				}
				
				// 设置加载状态
				uni.showLoading({
					title: '保存中...'
				});
				
				// 准备保存数据
				const stageData = {
					currentStaffId: this.currentStaffId,
					maleUserId: this.selectedMaleUser.user_id,
							femaleUserId: this.selectedFemaleUser.user_id,
							maleStaffId: this.selectedMaleStaff.staff_id,
							femaleStaffId: this.selectedFemaleStaff.staff_id,
							maleShopId: this.selectedMaleShop.shop_id,
							femaleShopId: this.selectedFemaleShop.shop_id,
							regionId: this.regionId,
					orderNo: this.orderInfo.orderNo,
					stageId: this.selectedStage.stage_id,
					staffId: this.selectedStageStaff.staff_id
				};
				
				// 调用API保存订单阶段
				saveOrderStage(stageData).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '阶段创建成功',
							icon: 'success'
						});
						
						// 关闭弹窗
						this.closeStageModal();
						
						// 添加新阶段到阶段列表（不再替换现有阶段）
						const newStage = {
							stageId: res.data.stageId,
							stageName: res.data.stageName,
							staffId: stageData.staffId,
							//staffName: this.selectedStageStaff.staff_name,
							isPink: true,
							canDelete: false, // 默认不可删除，后续检查权限
							maleAgreed: false, // 默认男生不同意，后端数据返回后会更新
							femaleAgreed: false, // 默认女生不同意，后端数据返回后会更新
							malestatus: "0", // 默认为"0"，表示不同意
							femalestatus: "0" // 默认为"0"，表示不同意
						};
						
						// 将新阶段添加到阶段列表
						this.orderStages.push(newStage);
						
						// 更新订单信息中的当前阶段
						if (this.orderInfo) {
							this.orderInfo.currentStageId = res.data.stageId;
						}
						
						// 设置当前活跃阶段ID
						this.currentActiveStageId = res.data.stageId;
						
						// 检查是否有权限删除阶段
						this.checkDeletePermission(res.data.stageId, newStage);
						
						// 获取阶段的同意状态
						this.getStageAgreementStatus(res.data.stageId, newStage);
						
						// 加载该阶段的服务列表
						this.loadOrderServices();
						
						// 重新加载阶段列表，因为新建阶段后，可选的下一阶段会发生变化
						this.loadOrderStagesWithCallback();
						
						// 强制清空阶段列表缓存
						this.forceRefreshStageList();
					} else {
						uni.showToast({
							title: res.msg || '创建阶段失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('创建阶段失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 切换阶段列表显示
			toggleStageList() {
				// 每次展开阶段列表时，确保数据是最新的
				if (!this.showStageList) {
					// 清空之前选择的阶段
					this.selectedStage = null;
					
					// 强制刷新阶段列表缓存
					this.forceRefreshStageList();
					
					// 如果要展开列表，先获取最新数据并过滤掉已创建的阶段
					this.loadOrderStagesWithCallback(() => {
						// 检查是否有可选阶段
						if (this.stageList.length === 0) {
							uni.showToast({
								title: '没有可选阶段，所有阶段已创建',
								icon: 'none'
							});
							return;
						}
						
						this.showStageList = true;
						this.showStaffList = false;
						
						console.log('展开阶段列表，当前有', this.stageList.length, '个可选阶段');
					});
				} else {
					// 如果是收起列表，直接切换状态
					this.showStageList = false;
				}
			},
			
			// 切换店员列表显示
			toggleStaffList() {
				this.showStaffList = !this.showStaffList;
				if (this.showStaffList) {
					this.showStageList = false;
				}
			},
			
			// 选择阶段
			selectStage(stage) {
				console.log('选择阶段:', stage.stage_name);
				this.selectedStage = stage;
				this.showStageList = false;
			},
			
			// 选择店员
			selectStageStaff(staff) {
				this.selectedStageStaff = staff;
				this.showStaffList = false;
			},
			
			// 添加服务
			addService(stage) {
				if (!this.orderInfo || !this.orderInfo.orderNo) {
					uni.showToast({
						title: '请先创建或选择订单',
						icon: 'none'
					});
					return;
				}
				
				// 检查是否是当前活跃阶段
				if (stage.stageId !== this.currentActiveStageId) {
					uni.showToast({
						title: '只能在当前阶段添加服务',
						icon: 'none'
					});
					return;
				}
				
				// 保存当前阶段
				this.currentStage = stage;
				
				// 保存当前点击的阶段ID
				this.currentClickedStageId = stage.stageId;
				
				// 检查是否是交往阶段
				if (stage.stageName === '交往阶段') {
					// 获取交往阶段当前的服务数量
					const stageServices = this.getStageServices(stage.stageId);
					if (stageServices && stageServices.length > 0) {
						uni.showToast({
							title: '交往阶段的服务将自动生成，无需手动添加',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				}
				
				// 加载服务项目列表
				this.loadServiceItems();
				
				// 加载订单相关的店员信息并确保有默认选择
				if (!this.orderStaffList || this.orderStaffList.length === 0) {
					this.loadOrderStaffs();
				} else if (!this.selectedServiceStaff && this.orderStaffList.length > 0) {
					this.selectedServiceStaff = this.orderStaffList[0];
				}
				
				// 显示弹窗
				this.showServiceModal = true;
			},
			
			// 加载服务项目列表
			loadServiceItems() {
				// 设置加载状态
				uni.showLoading({
					title: '加载服务项目...'
				});
				
				// 调用API获取服务项目列表
				getServiceItems(this.currentStaffId).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						this.serviceList = res.data || [];
						
						// 如果是交往阶段，根据服务数量决定使用哪个服务项目
						if (this.currentStage && this.currentStage.stageName === '交往阶段') {
							const stageServices = this.getStageServices(this.currentStage.stageId);
							const count = stageServices ? stageServices.length : 0;
							
							// 排序服务列表（按service_code排序）
							this.serviceList.sort((a, b) => {
								return a.service_code > b.service_code ? 1 : -1;
							});
							
							// 第1次使用排第一个，第2次使用排第二个，第3次使用排第三个，第4次使用排第一个，依此循环
							if (this.serviceList.length > 0) {
								const index = count % this.serviceList.length;
								this.selectedService = this.serviceList[index];
							}
						} else if (this.serviceList.length > 0) {
							// 默认选择第一个服务项目
							this.selectedService = this.serviceList[0];
						}
					} else {
						uni.showToast({
							title: res.msg || '获取服务项目失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取服务项目失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 新建服务弹窗关闭
			closeServiceModal() {
				this.showServiceModal = false;
				this.showServiceList = false;
				this.showServiceStaffList = false;
				this.selectedService = null;
				this.selectedServiceStaff = null;
			},
			
			// 新建服务弹窗确定
			confirmCreateService() {
				if (!this.selectedService) {
					uni.showToast({
						title: '请选择服务项目',
						icon: 'none'
					});
					return;
				}
				
				if (!this.selectedServiceStaff) {
					uni.showToast({
						title: '请选择店员',
						icon: 'none'
					});
					return;
				}
				
				// 设置加载状态
				uni.showLoading({
					title: '保存中...'
				});
				
				console.log('创建服务，当前点击的阶段ID:', this.currentClickedStageId);
				console.log('当前阶段对象:', this.currentStage);
				
				// 准备保存数据
				const serviceData = {
					currentStaffId: this.currentStaffId,
					currentStageId: this.currentClickedStageId, // 确保传递当前阶段ID
					orderNo: this.orderInfo.orderNo,
					serviceId: this.selectedService.service_id,
					serviceName: this.selectedService.service_name,
					price: this.selectedService.price || 0,
					staffId: this.selectedServiceStaff.staff_id
				};
				
				console.log('提交的服务数据:', JSON.stringify(serviceData));
				
				// 调用API保存服务
				saveOrderService(serviceData).then(res => {
					uni.hideLoading();
					console.log('保存服务API返回:', JSON.stringify(res));
					
					if (res.code === 200) {
						uni.showToast({
							title: '服务创建成功',
							icon: 'success'
						});
						
						// 关闭弹窗
						this.closeServiceModal();
						
						// 重置所有通知状态
						this.resetAllNotifications();
						
						// 重新加载服务列表
						this.loadOrderServices();
					} else {
						uni.showToast({
							title: res.msg || '创建服务失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('创建服务失败', err);
					
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 切换服务项目列表显示
			toggleServiceList() {
				this.showServiceList = !this.showServiceList;
				if (this.showServiceList) {
					this.showServiceStaffList = false;
				}
			},
			
			// 切换店员列表显示
			toggleServiceStaffList() {
				this.showServiceStaffList = !this.showServiceStaffList;
				if (this.showServiceStaffList) {
					this.showServiceList = false;
				}
			},
			
			// 选择服务项目
			selectService(service) {
				this.selectedService = service;
				this.showServiceList = false;
			},
			
			// 选择店员
			selectServiceStaff(staff) {
				this.selectedServiceStaff = staff;
				this.showServiceStaffList = false;
			},
			
			// 加载订单服务列表
			loadOrderServices() {
				if (!this.orderInfo || !this.orderInfo.orderNo) return;
				
				// 设置加载状态
				uni.showLoading({
					title: '加载服务列表...'
				});
				
				console.log('开始加载服务列表, 订单号:', this.orderInfo.orderNo);
				
				// 调用API获取订单服务列表
				getOrderServices(this.orderInfo.orderNo).then(res => {
					uni.hideLoading();
					console.log('服务列表API返回:', JSON.stringify(res));
					
					if (res.code === 200) {
						// 清空当前服务列表
						this.orderServices = [];
						
						// 处理返回的数据
						if (res.data && res.data.length > 0) {
							console.log('服务数据条数:', res.data.length);
							
							// 先添加所有服务项到列表中，但暂时不设置canDelete属性
							res.data.forEach(service => {
								// 确保数据类型正确
								const serviceItem = {
									id: service.id,
									serviceId: Number(service.serviceId),
									serviceName: service.serviceName,
									price: Number(service.price) || 0,
									staffId: Number(service.staffId),
									staffNickname: service.staffNickname || '',
									serviceTime: service.serviceTime || '',
									notify: service.notify || '0',
									canDelete: false, // 默认设置为false，后续通过API获取
									stageId: Number(service.current_stage_id || 0) // 添加stageId字段，使用后端返回的current_stage_id
								};
								
								// 添加到服务列表
								this.orderServices.push(serviceItem);
							});
							//console.log("服务AAAA"+JSON.stringify(this.orderServices));
							// 收集所有服务ID
							const serviceIds = this.orderServices.map(service => service.id);
							
							// 批量检查删除权限
							if (serviceIds.length > 0) {
								batchCheckServiceCanDelete(serviceIds).then(checkRes => {
									if (checkRes.code === 200) {
										const permissionMap = checkRes.data;
										
										// 更新每个服务的canDelete属性
										this.orderServices.forEach(service => {
											service.canDelete = permissionMap[service.id] === true;
											console.log(`服务ID ${service.id} 可删除: ${service.canDelete}`);
										});
										
										console.log('更新权限后的服务列表:', JSON.stringify(this.orderServices));
									} else {
										console.error('获取删除权限失败:', checkRes.msg);
									}
								}).catch(err => {
									console.error('检查删除权限失败:', err);
								});
							}
							
							console.log('初始服务列表:', JSON.stringify(this.orderServices));
						} else {
							console.log('没有服务数据');
						}
					} else {
						uni.showToast({
							title: res.msg || '获取服务列表失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取服务列表失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 删除服务
			deleteService(service) {
				// 先检查服务是否可以删除
				checkServiceCanDelete(service.id).then(res => {
					if (res.code === 200) {
						const canDelete = res.data === true;
						
						// 更新本地服务的canDelete属性
						service.canDelete = canDelete;
						
						if (!canDelete) {
							uni.showToast({
								title: '您没有权限删除此服务',
								icon: 'none'
							});
							return;
						}
						
						// 显示删除确认对话框
						uni.showModal({
							title: '删除确认',
							content: '确定要删除此服务吗？删除后无法恢复。',
							success: (res) => {
								if (res.confirm) {
									// 用户点击确定，执行删除操作
									this.confirmDeleteService(service);
								}
							}
						});
					} else {
						uni.showToast({
							title: res.msg || '检查删除权限失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					console.error('检查删除权限失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 确认删除服务
			confirmDeleteService(service) {
				// 设置加载状态
				uni.showLoading({
					title: '删除中...'
				});
				
				// 调用API删除服务
				deleteOrderService(service.id).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '服务删除成功',
							icon: 'success'
						});
						
						// 重新加载服务列表
						this.loadOrderServices();
					} else {
						uni.showToast({
							title: res.msg || '删除服务失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('删除服务失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 通知同事
			notifyColleague(service) {
				// 设置加载状态
				uni.showLoading({
					title: '发送通知中...'
				});
				
				// 调用API发送通知
				notifyColleague(service.id).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '通知发送成功',
							icon: 'success'
						});
						
						// 更新本地服务状态
						service.notify = '1';
						
						// 刷新服务列表
						this.loadOrderServices();
					} else {
						uni.showToast({
							title: res.msg || '发送通知失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('发送通知失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 切换通知状态
			toggleNotify(service) {
				// 如果已通知，则取消通知；如果未通知，则发送通知
				if (service.notify === '1') {
					this.cancelNotify(service);
				} else {
					this.notifyColleague(service);
				}
			},
			
			// 取消通知
			cancelNotify(service) {
				// 设置加载状态
				uni.showLoading({
					title: '取消通知中...'
				});
				
				// 调用API取消通知
				cancelNotifyColleague(service.id).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '已取消通知',
							icon: 'success'
						});
						
						// 更新本地服务状态
						service.notify = '0';
						
						// 刷新服务列表
						this.loadOrderServices();
					} else {
						uni.showToast({
							title: res.msg || '取消通知失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('取消通知失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 重置所有通知状态
			resetAllNotifications() {
				// 如果没有订单信息，不执行
				if (!this.orderInfo || !this.orderInfo.orderNo) return;
				
				// 设置加载状态
				uni.showLoading({
					title: '重置通知状态...'
				});
				
				// 调用API重置所有通知状态
				resetAllNotifications(this.orderInfo.orderNo).then(res => {
					uni.hideLoading();
					if (res.code !== 200) {
						console.error('重置通知状态失败:', res.msg);
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('重置通知状态失败', err);
				});
			},
			
			// 加载当前阶段名称
			loadCurrentStageName(stageId,malestatus,femalestatus) {

				// 设置加载状态
				uni.showLoading({
					title: '加载阶段信息...'
				});
				
				// 如果没有设置当前活跃阶段，则设置为此阶段
				if (!this.currentActiveStageId) {
					this.currentActiveStageId = stageId;
				}
				
				// 调用API获取阶段信息
				getStageInfo(stageId).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						// 获取阶段名称
						const stageName = res.data.stage_name;
						//console.log("dsfffff="+JSON.stringify(res.data));
						// 创建新阶段对象
						const newStage = {
							stageId: stageId,
							stageName: stageName,
							isPink: true,
							canDelete: false, // 默认不可删除，后续检查权限
							maleAgreed: malestatus, // 默认男生不同意
							femaleAgreed: femalestatus // 默认女生不同意
						};
						// console.log("kkkkkk"+JSON.stringify(newStage));
						// 添加到阶段列表
						this.orderStages.push(newStage);
						
						// 检查是否有权限删除阶段
						this.checkDeletePermission(stageId, newStage);
						
						// 获取阶段的同意状态
						this.getStageAgreementStatus(stageId, newStage);
					} else {
						// 如果获取失败，使用默认名称
						const newStage = {
							stageId: stageId,
							stageName: "阶段 " + stageId,
							isPink: true,
							canDelete: false,
							maleAgreed: malestatus, // 默认男生不同意
							femaleAgreed: femalestatus // 默认女生不同意
						};
						
						// 添加到阶段列表
						this.orderStages.push(newStage);
						
						// 检查是否有权限删除阶段
						this.checkDeletePermission(stageId, newStage);
						
						// 获取阶段的同意状态
						this.getStageAgreementStatus(stageId, newStage);
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取阶段信息失败', err);
					
					// 如果获取失败，使用默认名称
					const newStage = {
						stageId: stageId,
						stageName: "阶段 " + stageId,
						isPink: true,
						canDelete: false,
						maleAgreed: false, // 默认男生不同意
						femaleAgreed: false // 默认女生不同意
					};
					
					// 添加到阶段列表
					this.orderStages.push(newStage);
					
					// 检查是否有权限删除阶段
					this.checkDeletePermission(stageId, newStage);
					
					// 获取阶段的同意状态
					this.getStageAgreementStatus(stageId, newStage);
				});
			},
			
			// 检查是否有权限删除阶段
			checkDeletePermission(stageId, stage) {
				if (!this.orderInfo || !this.orderInfo.orderNo || !this.currentStaffId) {
					if (stage) {
						stage.canDelete = false;
					} else {
						this.canDeleteStage = false;
					}
					return;
				}
				
				// 检查当前登录的店员是否有权限删除阶段
				// 权限判断：哪个员工编号所对应的那条订单信息表biz_order中的current_stage_id字段有值，哪个员工就有权力删除
				const maleStaffId = this.orderInfo.maleStaffId;
				const femaleStaffId = this.orderInfo.femaleStaffId;
				const orderNo = this.orderInfo.orderNo;
				
				if (this.currentStaffId === maleStaffId || this.currentStaffId === femaleStaffId) {
					// 检查该阶段是否可以删除（没有服务及收费）
					checkStageCanDelete(stageId, orderNo, this.currentStaffId).then(res => {
						if (res.code === 200) {
							if (stage) {
								stage.canDelete = true;
							} else {
								this.canDeleteStage = true;
							}
						} else {
							if (stage) {
								stage.canDelete = false;
							} else {
								this.canDeleteStage = false;
							}
							console.log('阶段不可删除:', res.msg);
						}
					}).catch(err => {
						console.error('检查阶段是否可删除失败', err);
						if (stage) {
							stage.canDelete = false;
						} else {
							this.canDeleteStage = false;
						}
					});
				} else {
					if (stage) {
						stage.canDelete = false;
					} else {
						this.canDeleteStage = false;
					}
				}
			},
			
			// 删除阶段
			deleteStage(stage) {
				if (!stage.canDelete) {
					uni.showToast({
						title: '您没有权限删除此阶段',
						icon: 'none'
					});
					return;
				}
				
				// 显示删除确认对话框
				uni.showModal({
					title: '删除确认',
					content: '确定要删除此阶段吗？删除后无法恢复。',
					success: (res) => {
						if (res.confirm) {
							// 用户点击确定，执行删除操作
							this.confirmDeleteStage(stage);
						}
					}
				});
			},
			
			// 确认删除阶段
			confirmDeleteStage(stage) {
				// 设置加载状态
				uni.showLoading({
					title: '删除中...'
				});
				
				const stageData = {
					orderNo: this.orderInfo.orderNo,
					stageId: stage.stageId,
					staffId: this.currentStaffId
				};
				
				// 保存被删除阶段的信息，以便后续添加到可选阶段列表中
				const deletedStageId = stage.stageId;
				const deletedStageName = stage.stageName;
				
				// 调用API删除阶段
				deleteOrderStage(stageData).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '阶段删除成功',
							icon: 'success'
						});
						
						// 找出被删除阶段的索引位置
						const index = this.orderStages.findIndex(s => s.stageId === stage.stageId);
						
						// 从阶段列表中移除该阶段
						if (index !== -1) {
							this.orderStages.splice(index, 1);
							
							// 如果删除的是当前活跃阶段，则将上一个阶段设为活跃
							if (stage.stageId === this.currentActiveStageId) {
								// 如果有上一个阶段，将其设为活跃
								if (index > 0) {
									this.currentActiveStageId = this.orderStages[index - 1].stageId;
								} 
								// 否则将最后一个阶段设为活跃（如果有的话）
								else if (this.orderStages.length > 0) {
									this.currentActiveStageId = this.orderStages[this.orderStages.length - 1].stageId;
								}
								// 如果没有阶段了，设为null
								else {
									this.currentActiveStageId = null;
								}
							}
						}
						
						// 重新加载服务列表
						this.loadOrderServices();
						
						// 重新加载阶段列表，因为删除阶段后，可选的下一阶段会发生变化
						this.loadOrderStagesWithCallback();
						
						// 强制清空阶段列表缓存
						this.forceRefreshStageList();
						
						// 获取被删除阶段的详细信息，并添加到可选阶段列表中
						getStageInfo(deletedStageId).then(stageRes => {
							if (stageRes.code === 200) {
								// 检查stageList中是否已存在该阶段
								const existsInList = this.stageList.some(s => s.stage_id === deletedStageId);
								
								// 如果不存在，则添加
								if (!existsInList) {
									this.stageList.push(stageRes.data);
									console.log('已将删除的阶段添加回可选列表:', deletedStageName);
								}
							}
						});
					} else {
						uni.showToast({
							title: res.msg || '删除阶段失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('删除阶段失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 强制刷新阶段列表缓存
			forceRefreshStageList() {
				// 清空阶段列表
				this.stageList = [];
				this.selectedStage = null;
				
				// 将currentStageId临时保存
				const tempCurrentStageId = this.currentActiveStageId;
				
				// 清空当前阶段ID，确保下次获取时会重新请求完整列表
				if (this.orderInfo) {
					this.orderInfo.currentStageId = null;
				}
				
				console.log('强制刷新阶段列表缓存，清空当前阶段ID');
				
				// 加载最新的阶段列表
				this.loadOrderStagesWithCallback(() => {
					// 恢复当前阶段ID
					if (this.orderInfo) {
						this.orderInfo.currentStageId = tempCurrentStageId;
					}
					console.log('阶段列表已强制刷新，现有', this.stageList.length, '个可选阶段');
				});
			},
			
			// 获取特定阶段的服务列表
			getStageServices(stageId) {
				if (!this.orderServices || this.orderServices.length === 0) {
					return [];
				}
				
				console.log('当前阶段ID:', stageId);
				console.log('所有服务列表:', JSON.stringify(this.orderServices));
				
				// 过滤出属于当前阶段的服务
				const filteredServices = this.orderServices.filter(service => {
					// 优先使用stageId字段进行匹配
					if (service.stageId && Number(service.stageId) === Number(stageId)) {
						console.log('通过stageId匹配成功:', service.stageId, stageId);
						return true;
					}
					
					// 如果没有stageId字段，回退到使用serviceId与阶段ID匹配（兼容旧数据）
				//	console.log("service是"+JSON.stringify(service));
					const serviceStageId = Number(service.stageId);
					const currentStageId = Number(stageId);
					console.log('比较serviceId:', serviceStageId, currentStageId, serviceStageId === currentStageId);
					return serviceStageId === currentStageId;
				});
				
				console.log('过滤后的服务:', JSON.stringify(filteredServices));
				return filteredServices;
			},
			
			// 查看同意状态（由于单选按钮已禁用，该方法只用于显示状态）
			viewAgreementStatus(stage, gender) {
				if (!stage) return;
				// 只显示提示，不更改状态
				const status = gender === 'male' ? (stage.maleAgreed ? '同意' : '不同意') : (stage.femaleAgreed ? '同意' : '不同意');
				
				uni.showToast({
					title: `${gender === 'male' ? '男生' : '女生'}当前状态: ${status}`,
					icon: 'none'
				});
				
				// 实际项目中的API调用应该类似于：
				/*
				// 设置加载状态
				uni.showLoading({
					title: '更新同意状态...'
				});
				
				// 调用API更新同意状态
				updateStageAgreementStatus(stage.stageId, gender, value).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '同意状态已更新',
							icon: 'success'
						});
					} else {
						// 如果API调用失败，恢复原状态
						if (gender === 'male') {
							stage.maleAgreed = !value;
						} else {
							stage.femaleAgreed = !value;
						}
						
						uni.showToast({
							title: res.msg || '更新同意状态失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('更新同意状态失败', err);
					
					// 如果API调用失败，恢复原状态
					if (gender === 'male') {
						stage.maleAgreed = !value;
					} else {
						stage.femaleAgreed = !value;
					}
					
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
				*/
			},
			
			// 获取阶段的同意状态
			getStageAgreementStatus(stageId, stage) {
				if (!this.orderInfo || !this.orderInfo.orderNo) return;
				// 获取的阶段数据中已包含男女同意状态
				// 在getAllOrderStages方法的返回数据中，已经包含了malestatus和femalestatus字段
				// 不需要单独调用API，直接使用返回的数据设置同意状态
				
				// 从orderStages中查找对应ID的阶段数据
				const stageData = this.orderStages.find(s => s.stageId === stageId);
				// console.log("3");
				// console.log("1="+stageData.malestatus);
				// console.log("2="+stageData.femalestatus);
				if (stageData && stageData.malestatus !== undefined && stageData.femalestatus !== undefined) {
					if (stage) {

						// 设置男生同意状态（0不同意 1同意）
						stage.maleAgreed = stageData.malestatus === "1";
						// 设置女生同意状态（0不同意 1同意）
						stage.femaleAgreed = stageData.femalestatus === "1";
					}
				}
				
				// 实际项目中的API调用应该类似于：
				/*
				getStageAgreementStatus(stageId).then(res => {
					if (res.code === 200 && stage) {
						stage.maleAgreed = res.data.maleAgreed;
						stage.femaleAgreed = res.data.femaleAgreed;
					}
				}).catch(err => {
					console.error('获取阶段同意状态失败', err);
				});
				*/
			},
			
			// 加载订单总结内容
			loadOrderSummary() {
				// if (!this.orderInfo || !this.orderInfo.orderNo) return;
				
				// // 检查是否有订单结束总结阶段
				// const endStageId = this.getOrderEndStageId();
				// console.log("检查是否有订单结束总结阶段"+endStageId);
				// if (!endStageId) return;
				
				// 设置加载状态
				uni.showLoading({
					title: '加载订单总结...'
				});
				
				// 发送请求获取订单总结内容
				getOrderSummary(this.orderInfo.orderNo).then(res => {
					uni.hideLoading();
					if (res.code === 200 && res.data) {
						// 设置订单总结内容
						this.maleSummary = res.data.maleSummary || '';
						this.femaleSummary = res.data.femaleSummary || '';
						
						// 重置编辑状态
						this.maleSummaryEdited = false;
						this.femaleSummaryEdited = false;
						
						// 更新锁单按钮状态
						this.updateLockOrderStatus();
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('获取订单总结失败', err);
				});
			},
			
			// 判断是否为订单结束总结阶段
			isOrderEndStage(stage) {
				return stage && stage.stageName && stage.stageName.includes("订单结束总结");
			},
			
			// 判断是否有订单结束总结阶段
			hasOrderEndStage() {
				if (!this.orderStages || this.orderStages.length === 0) return false;
				return this.orderStages.some(stage => this.isOrderEndStage(stage));
			},
			
			// 获取订单结束阶段ID
			getOrderEndStageId() {
				if (!this.orderStages || this.orderStages.length === 0) return null;
				const endStage = this.orderStages.find(stage => this.isOrderEndStage(stage));
				return endStage ? endStage.stageId : null;
			},
			
			// 判断是否可以锁单
			canLockOrderNow() {
				// 首先要有订单号
				if (!this.orderInfo || !this.orderInfo.orderNo) return false;
				
				// 如果有订单结束总结阶段，需要检查是否两个总结都填写了
				if (this.hasOrderEndStage()) {
					return this.maleSummary.trim().length > 0 && this.femaleSummary.trim().length > 0;
				}
				
				// 如果没有订单结束总结阶段，则不允许锁单
				return false;
			},
			
			// 更新锁单按钮状态
			updateLockOrderStatus() {
				this.canLockOrder = this.canLockOrderNow();
			},
			
			// 保存总结
			saveSummary() {
				if (!this.orderInfo || !this.orderInfo.orderNo) {
					uni.showToast({
						title: '请先创建订单',
						icon: 'none'
					});
					return;
				}
				
				// 设置加载状态
				uni.showLoading({
					title: '保存中...'
				});
				
				// 构建请求数据
				const summaryData = {
					orderNo: this.orderInfo.orderNo,
					staffid: this.currentStaffId
				};
				
				// 只传递已编辑的字段
				if (this.maleSummaryEdited || this.currentStaffId == this.selectedMaleStaff.staff_id) {
					summaryData.summary = this.maleSummary;
				}
				
				if (this.femaleSummaryEdited || this.currentStaffId == this.selectedFemaleStaff.staff_id) {
					summaryData.summary = this.femaleSummary;
				}
				
				// 调用保存总结API
				saveOrderSummary(summaryData).then(res => {
					uni.hideLoading();
					if (res.code === 200) {
						uni.showToast({
							title: '总结保存成功',
							icon: 'success'
						});
						
						// 重置编辑状态
						this.maleSummaryEdited = false;
						this.femaleSummaryEdited = false;
						
						// 更新锁单按钮状态
						this.updateLockOrderStatus();
					} else {
						uni.showToast({
							title: res.msg || '保存失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('保存总结失败', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},
			
			// 检查当前登录用户是男代理还是女代理
			checkAgentGender() {
				if (!this.currentStaffId) return;
				
				// 根据当前店员ID和订单信息判断是男代理还是女代理
				this.isMaleAgent = false;
				this.isFemaleAgent = false;
				
				// 如果订单信息已加载，直接判断
				if (this.orderInfo) {
					this.isMaleAgent = this.currentStaffId == this.orderInfo.maleStaffId;
					this.isFemaleAgent = this.currentStaffId == this.orderInfo.femaleStaffId;
				}
			},
			
			// 获取订单删除同意状态
			getOrderDeleteStatus() {
				if (!this.orderInfo || !this.orderInfo.orderNo) return;
				
				// 男女订单可能在不同对象中
				if (this.orderInfo.maleOrder) {
					this.maleAgentApproved = this.orderInfo.maleOrder.isdelete === '1';
				}
				
				if (this.orderInfo.femaleOrder) {
					this.femaleAgentApproved = this.orderInfo.femaleOrder.isdelete === '1';
				}
				
				// 更新冻结按钮状态
				this.checkCanFreezeOrder();
			},
			
			// 更新女生代理同意状态
			updateFemaleApproval(e) {
				this.femaleAgentApproved = e.detail.value;
			},
			
			// 更新男生代理同意状态
			updateMaleApproval(e) {
				this.maleAgentApproved = e.detail.value;
			},
			
			// 切换女生代理同意状态
			toggleFemaleApproval() {
				if (!this.isFemaleAgent) return; // 如果不是女生代理，不允许切换
				
				if (!this.orderInfo || !this.orderInfo.orderNo) {
					uni.showToast({
						title: '请先创建订单',
						icon: 'none'
					});
					return;
				}
				
				// 更新同意状态
				this.updateDeleteAgreement('female', !this.femaleAgentApproved);
			},
			
			// 切换男生代理同意状态
			toggleMaleApproval() {
				if (!this.isMaleAgent) return; // 如果不是男生代理，不允许切换
				
				if (!this.orderInfo || !this.orderInfo.orderNo) {
					uni.showToast({
						title: '请先创建订单',
						icon: 'none'
					});
					return;
				}
				
				// 更新同意状态
				this.updateDeleteAgreement('male', !this.maleAgentApproved);
			},
		}
	}
</script>

<style lang="scss">
/* 添加图标字体 */
@font-face {
	font-family: "iconfont";
	src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAO8AA0AAAAAB1wAAANnAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP0ZGVE0cGh4GVgCCUhEICoMUgm8LDAABNgIkAxQEIAWFAgdKG58IUZQwTg7ZjwlDzmOi2P0neIEYhbw/ngu4XIwl3PgYD/+N37f3zZ39Z9Wikphc3UQq0QiFxEuERCwJcanhNplnNE2v/v+/n+vfAiNSwXDHcoryHcl7m0xKHgi3NlAmbYev4vq2e2ZKqX7mmd1bXp7pmwoScRKJQnNfhtMaiLcDFjlFH9qgpY5sYlwFnGDSbED05STm1EGBVgXipM5gAOJNhTxa/v8oezwuEoWuBn81EH+W3BVrR+jjOh2PfHja0DcC/A0CZJwBGORMPQGbE0vHSZrDAJCSE5djAhueczQHXs5UiD0Bhc5xxhILFCAIT6tgAgLA+B4XdAsRgJKRkQ4QyAZEsABCxjzA8DCKdwYROPMWgJDDrS/A5ILQCyjX73UG4rQGDm01XQcNK+BneQj3Bijj3tu8vtYZPLbpCMYYMNh2CHpO9fXMk28M8VUvDnTeezZe9W3vCM04KjpQ/NaA6TGomrd/Ru9T36mzUlCFO7cGTO70GFfejCEdOGxMDThj9z3zlFtsv3eicWwZZz+m74dN5vWPpqdZR8lox+SoUpP7T/d7+aHX6vco4fTpCUWnlvY3lVfw+FSfz71qT01IlLOlZ5eWSaW9o7cEl/RJ5pWyk4ZF9e4dJS1afj0lUhp7dtUrUsWLfJA0OblAklWGQVapCjOlp5fmmvpt26Ffkko5572vuX5/m9eOOGIddMQROw4fspfzA7njs+UOlxPHNff67Xm5fao/SsbW7u+/Pii6ehv/NXxvZkI+hGS9Rx75vz8xD206+/P056/PTp5Gi3CybfYX0aZNpPnNt+i8CcOHz8YXcsAEAIS0QcABXM0aMBUBKHeM6ctcHAAOHY0sGBkqxTavoSvp+N8Y/8s9nfdbGzZNaODJFkjQDC2I0IBcNBpYal09rFhryPTQanRdaOBIMGCGo/Oi2Vg2XLV6DDDAJcCXqg1IUI52IMMZ/RYTnLF2Ma4ZVa1qPExbW71b6qXwMu0YWNUPhBw6klqFviNN4YgepWj0nyC14gDRir6h8ZIXPbzP2L8Y4cz4D0JzyqZYYKOpAm8E3rZNsDz0CmUB3bYNumGxpi41F5AxWu9H5ClCtzORLBTcb5DjMTNFE7WB6KIZFtjYATFrADfIz2C0fYlAErKSDgltk9UTjW5TZtAAApkDIxSWIy2QXW5Fyll7CVZzE2AZFBcwbwzgtgCWYSqCLhFYD+/oFcjJNvuFhosVFAb7BOiOheUAA6awOoBbz5aAkm2TrAAcEg9tDwJzTWZtc38AAAAAA4ANHAQACgDgpPgJAACkNQBgEmMnwPHSBMDyzIWAs1IQAprVgQRNOhAgRDfQwYhNaEGGTjRAAPwLwiccmyg4cQQQnAYCqBgOQsj4DIRw4hUI0M0n6BDCP7Qgkw+NEACiEa9wNIBLlK2q1ppo9gHpSYVVqvsfuW1wpzGTXvBL1KA1J6fs4R05NRY9KBVlJscYCC4xRG/q5tklCg44lzMkSaJIaqOroqq1JppbnZr0qOInVan7n8htg3uOzdWXX6IGrclHZw+rdS52ari5PRBVph5KG2O8BgLVCl94beqCnC1gi9bjXM7Q0JIkWvQ0Xa+1dvQP2tPza53tC/5CKVFGJdXUUU9D1/lfuT4hc1TvhIyFl+UFFjrbyWPdxSOa6tA3R4gmokR2WFxMVAAAAAA=') format('woff2');
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-arrow-down:before {
	content: "\e600";
}

.icon-arrow-up-small:before {
	content: "\e601";
}

.icon-plus:before {
	content: "\e602";
}

.icon-refresh:before {
	content: "\e603";
}

.icon-save:before {
	content: "\e604";
}

.icon-data:before {
	content: "\e605";
}

.icon-input:before {
	content: "\e606";
}
.new-order-container {
	padding: 0;
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f8f9fa;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	color: #333;
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16px;
		background-color: #fff;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
		position: relative;
		z-index: 10;
		
		.city-select {
			display: flex;
			align-items: center;
			font-size: 16px;
			font-weight: 500;
			color: #333;
			
			.iconfont {
				font-size: 12px;
				margin-left: 4px;
				color: #666;
			}
		}
		
		.header-buttons {
			display: flex;
			gap: 10px;
		}
	}
	
	.option-rows {
		margin: 0;
		padding: 12px;
		background-color: #fff;
		
		.option-row {
			display: flex;
			align-items: center;
			margin-bottom: 12px;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
		
		.option-indicator {
			width: 24px;
			height: 24px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 8px;
			
			.option-dot {
				width: 10px;
				height: 10px;
				border-radius: 50%;
			}
		}
		
		.red-dot {
			background-color: #ff3b30;
		}
		
		.green-dot {
			background-color: #4cd964;
		}
		
		.dropdown-group {
			flex: 1;
			display: flex;
			justify-content: space-between;
			gap: 8px;
		}
		
		.dropdown-wrapper {
			position: relative;
			flex: 1;
			
			.dropdown-label {
				padding: 8px 12px;
				background-color: #f0f0f0;
				border-radius: 4px;
				border: 1px solid #ddd;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 14px;
				cursor: pointer;
				
				.iconfont {
					font-size: 12px;
					color: #666;
				}
			}
			
			.dropdown-menu {
				position: absolute;
				top: 100%;
				left: 0;
				right: 0;
				z-index: 100;
				background-color: #fff;
				border-radius: 4px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
				margin-top: 4px;
				max-height: 300px;
				overflow-y: auto;
				border: 1px solid #eee;
			}
			
			.dropdown-item {
				padding: 8px 12px;
				font-size: 14px;
				transition: background-color 0.2s;
				
				&:hover, &:active {
					background-color: #f5f5f5;
				}
			}
		}
	}
	
	.data-display-area {
		flex: 1;
		margin: 16px;
		border: 2px dashed #c8e6c9;
		border-radius: 8px;
		background-color: rgba(200, 230, 201, 0.1);
		display: flex;
		justify-content: center;
		align-items: center;
		
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 20px;
			
			.iconfont {
				font-size: 32px;
				color: #4caf50;
				margin-bottom: 12px;
			}
			
			.placeholder-text {
				color: #4caf50;
				font-size: 18px;
				font-weight: 500;
				margin-bottom: 8px;
			}
			
			.sub-text {
				color: #8bc34a;
				font-size: 14px;
			}
		}
	}
	
	.footer {
		margin-top: auto;
		padding: 0 0 24px 0;
		
		.account-info-card {
			background-color: #2196f3;
			color: white;
			padding: 14px 16px;
			margin: 16px;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(33, 150, 243, 0.2);
			
			.account-balance {
				display: flex;
				justify-content: space-between;
				font-size: 14px;
				line-height: 1.6;
				
				.label {
					color: rgba(255, 255, 255, 0.9);
				}
				
				.value {
					font-weight: 600;
					
					.quota {
						opacity: 0.75;
						font-weight: normal;
					}
				}
			}
		}
		
		.footer-buttons {
			display: flex;
			justify-content: space-between;
			padding: 0 16px;
			margin-bottom: 20px;
			
			.btn {
				flex: 1;
				margin: 0 4px;
				height: 36px;
				min-width: 80px;
				padding: 0;
				
				&:first-child {
					margin-left: 0;
				}
				
				&:last-child {
					margin-right: 0;
				}
				
				.btn-text {
					font-size: 14px;
					white-space: nowrap;
					display: inline-block;
				}
				
				&.new-stage-btn {
					background-color: #2196F3;
				}
			}
		}
		
		.order-freeze-options {
			padding: 16px;
			background-color: #f9f9f9;
			border-radius: 12px;
			margin: 0 16px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
			
			.freeze-option {
				display: flex;
				align-items: center;
				margin-bottom: 16px;
				padding: 10px;
				background-color: white;
				border-radius: 8px;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
				
				.option-label {
					flex: 1;
					font-size: 14px;
					color: #333;
					font-weight: 500;
				}
				
				.custom-switch {
					transform: scale(0.8);
				}
			}
			
			.input-button-area {
				display: flex;
				justify-content: center;
				margin: 24px 0 12px;
			}
			
			.page-indicator {
				display: flex;
				justify-content: center;
				gap: 6px;
				margin-top: 16px;
				
				.indicator-dot {
					width: 8px;
					height: 8px;
					border-radius: 50%;
					background-color: #ddd;
					
					&.active {
						background-color: #999;
						width: 16px;
						border-radius: 4px;
					}
				}
			}
		}
	}
	
	.btn {
		padding: 10px 16px;
		border-radius: 6px;
		font-size: 14px;
		font-weight: 500;
		border: none;
		text-align: center;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s ease;
		
		.iconfont {
			margin-right: 4px;
		}
		
		&:active {
			opacity: 0.8;
		}
		
		&.btn-primary {
			background-color: #007aff;
			color: white;
			box-shadow: 0 2px 4px rgba(0, 122, 255, 0.2);
		}
		
		&.btn-outline {
			background-color: transparent;
			color: #007aff;
			border: 1px solid #007aff;
		}
		
		&.btn-light {
			background-color: #f0f0f0;
			color: #333;
		}
		
		&.btn-with-icon {
			.iconfont {
				margin-right: 4px;
			}
		}
		
		&.btn-input {
			background: linear-gradient(to right, #757575, #9e9e9e);
			color: white;
			border-radius: 8px;
			padding: 12px 0;
			width: 120px;
			box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
			
			.iconfont {
				margin-right: 4px;
				font-size: 14px;
			}
		}
		
		&.small-btn {
			font-size: 13px;
			padding: 8px 12px;
		}
		
		&.btn-disabled {
			background-color: #ccc;
			color: #666;
			cursor: not-allowed;
			opacity: 0.6;
			box-shadow: none;
			
			&:active {
				opacity: 0.6;
			}
		}
		
		&.btn-active {
			background-color: #4cd964;
			color: white;
			box-shadow: 0 2px 4px rgba(76, 217, 100, 0.2);
			
			&:active {
				opacity: 0.8;
			}
		}
	}
	
	/* 用户信息展示区样式 */
	.user-info-display {
		width: 100%;
		height: 100%;
		display: flex;
		padding: 20px;
		
		.user-card {
			flex: 1;
			background-color: white;
			border-radius: 10px;
			margin: 0 10px;
			box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			overflow: hidden;
			max-height: 100%;
			overflow-y: auto;
			
			.user-header {
				padding: 12px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 1px solid #eee;
				position: sticky;
				top: 0;
				background-color: white;
				z-index: 10;
				
				.user-gender {
					font-size: 16px;
					font-weight: bold;
				}
				
				.user-code {
					font-size: 14px;
					color: #666;
				}
			}
			
			.user-detail {
				padding: 16px;
				
				.user-name {
					font-size: 18px;
					font-weight: bold;
					margin-bottom: 8px;
					text-align: center;
					color: #999;
				}
				
				.detail-item {
					display: flex;
					margin-bottom: 12px;
					line-height: 1.4;
					font-size: 14px;
					
					.label {
						width: 110px;
						color: #666;
						flex-shrink: 0;
					}
					
					.value {
						flex: 1;
						color: #333;
					}
					
					&.staff-info {
						background-color: #f5f5f5;
						padding: 8px;
						border-radius: 6px;
						margin-bottom: 16px;
						
						.phone {
							margin-left: 10px;
							color: #0066cc;
						}
					}
				}
				
				.user-avatar {
					width: 100px;
					height: 120px;
					margin: 10px auto;
					border: 1px solid #ddd;
					border-radius: 6px;
					overflow: hidden;
					
					image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
					
					&.empty-avatar {
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: #f5f5f5;
						color: #999;
						font-size: 12px;
					}
				}
			}
		}
		
		.male-card {
			.user-header {
				background-color: rgba(33, 150, 243, 0.1);
			}
		}
		
		.female-card {
			.user-header {
				background-color: rgba(233, 30, 99, 0.1);
			}
		}
	}
	
	.option-tabs-scroll {
		flex: 1;
		white-space: nowrap;
	}
	
	.option-tabs {
		display: inline-flex;
		gap: 8px;
		padding-right: 16px;
	}
	
	.option-tab {
		padding: 8px 14px;
		border-radius: 6px;
		background-color: #f0f0f0;
		font-size: 14px;
		font-weight: 500;
		color: #333;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		border: 1px solid transparent;
		transition: all 0.2s ease;
		
		.iconfont {
			font-size: 12px;
			margin-left: 4px;
			color: #666;
		}
		
		&.active {
			background-color: #007aff;
			color: white;
			box-shadow: 0 2px 4px rgba(0, 122, 255, 0.2);
			
			.iconfont {
				color: rgba(255, 255, 255, 0.8);
			}
		}
		
		&:active {
			opacity: 0.8;
		}
	}
	
	/* 订单信息区域样式 */
	.order-info-area {
		margin: 0 16px 16px;
		
		.order-info-card {
			background-color: #fff;
			border-radius: 10px;
			box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			overflow: hidden;
			
			.order-info-title {
				padding: 12px 16px;
				background-color: #f8f9fa;
				border-bottom: 1px solid #eee;
				font-size: 16px;
				font-weight: 500;
				color: #333;
			}
			
			.order-info-content {
				padding: 16px;
				
				.order-info-item {
					display: flex;
					margin-bottom: 12px;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.label {
						width: 100px;
						color: #666;
						font-size: 14px;
					}
					
					.value {
						flex: 1;
						color: #333;
						font-size: 14px;
						font-weight: 500;
					}
				}
			}
		}
	}
}

/* 新建阶段弹窗样式 */
.stage-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	
	.stage-modal-content {
		background-color: white;
		border-radius: 4px;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
		width: 90%;
		max-width: 400px;
		
		.stage-modal-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			border-bottom: 1px solid #ddd;
			
			.stage-modal-title {
				font-size: 16px;
				font-weight: bold;
			}
			
			.stage-modal-close {
				font-size: 18px;
				font-weight: bold;
				color: #333;
				cursor: pointer;
			}
		}
		
		.stage-modal-body {
			padding: 10px;
			
			.stage-columns {
				display: flex;
				justify-content: space-between;
				gap: 10px;
				
				.stage-column {
					flex: 1;
					
					.column-header {
						padding: 8px;
						border: 1px solid #ddd;
						border-radius: 4px;
						font-size: 14px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						cursor: pointer;
						background-color: #f5f5f5;
						
						.iconfont {
							font-size: 12px;
							color: #666;
						}
					}
					
					.column-content {
						border: 1px solid #ddd;
						border-radius: 4px;
						margin-top: 5px;
						max-height: 200px;
						overflow-y: auto;
						
						.stage-item, .staff-item {
							padding: 8px;
							font-size: 14px;
							border-bottom: 1px solid #eee;
							
							&:last-child {
								border-bottom: none;
							}
							
							&:hover, &:active {
								background-color: #f5f5f5;
							}
						}
					}
				}
			}
		}
		
		.stage-modal-footer {
			display: flex;
			justify-content: flex-end;
			padding: 10px;
			border-top: 1px solid #ddd;
			gap: 10px;
			
			.btn {
				padding: 6px 12px;
				font-size: 14px;
				border-radius: 4px;
				
				&.btn-light {
					background-color: #f5f5f5;
					color: #333;
					border: 1px solid #ddd;
				}
				
				&.btn-primary {
					background-color: #007aff;
					color: white;
				}
			}
		}
	}
}

/* 订单阶段列表区域样式 */
.order-stages-area {
	margin: 16px;
	
	.order-stage-card {
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
		margin-bottom: 16px;
		overflow: hidden;
		
		.stage-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 16px;
			background-color: #fff;
			
			&.stage-header-pink {
				background-color: #ffeef5;
			}
			
			text {
				font-size: 15px;
				font-weight: 500;
			}
			
			.stage-actions {
				display: flex;
				gap: 10px;
				
				.btn {
					padding: 6px 12px;
					font-size: 13px;
					height: auto;
					min-width: auto;
					
					&.btn-danger {
						background-color: #ff5252;
						color: white;
						width: 30px;
						height: 30px;
						border-radius: 4px;
						padding: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 18px;
					}
				}
			}
		}
		
		.stage-services {
			padding: 0 12px 12px;
			
			.order-service-card {
				background-color: #f8f9fa;
				border-radius: 6px;
				margin-top: 8px;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
				
				.service-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 10px 12px;
					
					.service-info {
						flex: 1;
						
						.service-name {
							font-size: 14px;
							font-weight: 500;
							margin-right: 5px;
						}
						
						.service-staff {
							font-size: 13px;
							color: #666;
						}
						
						.service-price {
							font-size: 13px;
							color: #ff3b30;
							margin-left: 5px;
						}
					}
					
					.service-time {
						font-size: 12px;
						color: #999;
						margin: 0 10px;
					}
					
					.service-actions {
						display: flex;
						gap: 8px;
						
						.btn {
							padding: 4px 8px;
							font-size: 12px;
							height: auto;
							min-width: auto;
							
							&.btn-danger {
								background-color: #ff5252;
								color: white;
							}
							
							&.btn-disabled {
								background-color: #ccc;
								color: #666;
								cursor: not-allowed;
								opacity: 0.6;
								
								&:active {
									opacity: 0.6;
								}
							}
							
							&.btn-notified {
								background-color: #999;
								color: #fff;
								transform: translateY(1px);
								box-shadow: none;
							}
						}
					}
				}
			}
		}
		
		/* 男女同意区域样式 */
		.gender-agreement-area {
			padding: 10px 16px;
			background-color: #f8f9fa;
			border-top: 1px solid #eee;
			display: flex;
			justify-content: space-between;
			
			.gender-agreement-box {
				flex: 1;
				border: 1px solid #ddd;
				border-radius: 4px;
				padding: 8px;
				margin: 0 4px;
				background-color: #fff;
				
				.agreement-title {
					font-size: 14px;
					color: #333;
					margin-bottom: 8px;
					display: block;
				}
				
				.agreement-options {
					display: flex;
					justify-content: space-around;
					
					.custom-option {
						display: flex;
						align-items: center;
						font-size: 14px;
						padding: 4px;
						
						.custom-radio {
							width: 18px;
							height: 18px;
							border-radius: 50%;
							border: 1px solid #ccc;
							display: flex;
							align-items: center;
							justify-content: center;
							background-color: #e1e1e1;
						}
						
						.radio-dot {
							width: 10px;
							height: 10px;
							border-radius: 50%;
						}
						
						.agree-dot {
							background-color: #4cd964;
						}
						
						.disagree-dot {
							background-color: #ff3b30;
						}
						
						text {
							margin-left: 4px;
						}
					}
				}
			}
		}
	}
}

/* 新建服务弹窗样式 */
.service-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	
	.service-modal-content {
		background-color: white;
		border-radius: 4px;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
		width: 90%;
		max-width: 400px;
		
		.service-modal-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			border-bottom: 1px solid #ddd;
			
			.service-modal-title {
				font-size: 16px;
				font-weight: bold;
			}
			
			.service-modal-close {
				font-size: 18px;
				font-weight: bold;
				color: #333;
				cursor: pointer;
			}
		}
		
		.service-modal-body {
			padding: 10px;
			
			.service-columns {
				display: flex;
				justify-content: space-between;
				gap: 10px;
				
				.service-column {
					flex: 1;
					
					.column-header {
						padding: 8px;
						border: 1px solid #ddd;
						border-radius: 4px;
						font-size: 14px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						cursor: pointer;
						background-color: #f5f5f5;
						
						.iconfont {
							font-size: 12px;
							color: #666;
						}
					}
					
					.column-content {
						border: 1px solid #ddd;
						border-radius: 4px;
						margin-top: 5px;
						max-height: 200px;
						overflow-y: auto;
						
						.service-item, .staff-item {
							padding: 8px;
							font-size: 14px;
							border-bottom: 1px solid #eee;
							
							&:last-child {
								border-bottom: none;
							}
							
							&:hover, &:active {
								background-color: #f5f5f5;
							}
						}
					}
				}
			}
		}
		
		.service-modal-footer {
			display: flex;
			justify-content: flex-end;
			padding: 10px;
			border-top: 1px solid #ddd;
			gap: 10px;
			
			.btn {
				padding: 6px 12px;
				font-size: 14px;
				border-radius: 4px;
				
				&.btn-light {
					background-color: #f5f5f5;
					color: #333;
					border: 1px solid #ddd;
				}
				
				&.btn-primary {
					background-color: #007aff;
					color: white;
				}
			}
		}
	}
}

/* 订单服务列表区域样式 */
.order-services-area {
	margin: 0 16px 16px;
	
	.order-service-card {
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
		margin-bottom: 16px;
		overflow: hidden;
		
		.service-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 16px;
			background-color: #f8f9fa;
			
			.service-info {
				flex: 1;
				
				.service-name {
					font-size: 15px;
					font-weight: 500;
					margin-right: 5px;
				}
				
				.service-staff {
					font-size: 14px;
					color: #666;
				}
				
				.service-price {
					font-size: 14px;
					color: #ff3b30;
					margin-left: 5px;
				}
			}
			
			.service-time {
				font-size: 12px;
				color: #999;
				margin: 0 10px;
			}
			
			.service-actions {
				display: flex;
				gap: 10px;
				
				.btn {
					padding: 4px 8px;
					font-size: 12px;
					height: auto;
					min-width: auto;
					
					&.btn-danger {
						background-color: #ff5252;
						color: white;
					}
					
					&.btn-disabled {
						background-color: #ccc;
						color: #666;
						cursor: not-allowed;
						opacity: 0.6;
						
						&:active {
							opacity: 0.6;
						}
					}
					
					&.btn-notified {
						background-color: #999;
						color: #fff;
						transform: translateY(1px);
						box-shadow: none;
					}
				}
			}
		}
	}
}

/* 订单总结区域样式 */
.order-summary-area {
	padding: 16px;
	background-color: #f8f8f8;
	
	.order-summary-box {
		margin-bottom: 16px;
		
		&:last-child {
			margin-bottom: 16px;
		}
		
		.summary-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10px;
			font-size: 14px;
			font-weight: 500;
			
			.summary-count {
				color: #999;
				font-size: 12px;
				font-weight: normal;
			}
		}
		
		.summary-input {
			width: 100%;
			height: 80px;
			border: 1px solid #ddd;
			border-radius: 4px;
			padding: 10px;
			font-size: 14px;
			background-color: #fff;
		}
	}
	
	.summary-actions {
		display: flex;
		justify-content: center;
		margin-top: 10px;
		
		.btn {
			padding: 8px 16px;
			font-size: 14px;
			border-radius: 4px;
			
			&.btn-primary {
				background-color: #007aff;
				color: white;
			}
		}
	}
}

.modern-switch-wrapper {
	display: flex;
	align-items: center;
}

.modern-switch {
	width: 40px;
	height: 20px;
	border-radius: 10px;
	background-color: #ccc;
	position: relative;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.modern-switch.switch-on {
	background-color: #4CAF50;
}

.modern-switch.switch-disabled {
	background-color: #ccc;
	cursor: not-allowed;
}

.switch-handle {
	width: 16px;
	height: 16px;
	border-radius: 50%;
	background-color: #fff;
	position: absolute;
	top: 2px;
	left: 2px;
	transition: transform 0.3s ease;
}

.modern-switch.switch-on .switch-handle {
	transform: translateX(20px);
}

.switch-status {
	margin-left: 10px;
	font-size: 14px;
	color: #333;
}

.btn-freeze {
	background: linear-gradient(to right, #e53935, #f44336);
	color: white;
	border-radius: 8px;
	padding: 12px 0;
	width: 140px;
	box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
	transition: all 0.3s ease;
	opacity: 0.6;
	transform: scale(0.95);
}

.btn-freeze-active {
	opacity: 1;
	transform: scale(1);
	background: linear-gradient(to right, #d32f2f, #f44336);
	box-shadow: 0 4px 10px rgba(244, 67, 54, 0.4);
}

.btn-freeze:active {
	transform: scale(0.97);
}

/* 添加只读模式相关样式 */
.readonly-overlay {
	position: fixed;
	top: 60px; /* 调整顶部位置，不覆盖返回箭头区域 */
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.01);
	pointer-events: all;
	z-index: 999;
	display: flex;
	justify-content: center;
	align-items: flex-start;
}

.readonly-notice {
	background-color: rgba(0, 0, 0, 0.7);
	color: white;
	padding: 8px 16px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	font-size: 14px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
	margin-top: 10px; /* 添加顶部间距 */
}

.readonly-icon {
	font-size: 16px;
	margin-right: 6px;
}

.readonly-text {
	font-weight: 500;
}

.summary-readonly {
	width: 100%;
	min-height: 60px;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 10px;
	font-size: 14px;
	background-color: #f9f9f9;
	color: #333;
}

/* 使下拉框显示为禁用状态 */
.dropdown-label {
	opacity: 0.8;
	background-color: #f5f5f5 !important;
	color: #666;
}

/* 按钮和交互元素显示为禁用状态 */
.btn {
	opacity: 0.6;
	cursor: not-allowed;
}

/* 使文本域显示为只读状态 */
textarea {
	background-color: #f5f5f5;
	color: #666;
}
  </style>