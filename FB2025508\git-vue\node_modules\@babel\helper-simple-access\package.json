{"_from": "@babel/helper-simple-access@^7.22.5", "_id": "@babel/helper-simple-access@7.22.5", "_inBundle": false, "_integrity": "sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==", "_location": "/@babel/helper-simple-access", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-simple-access@^7.22.5", "name": "@babel/helper-simple-access", "escapedName": "@babel%2fhelper-simple-access", "scope": "@babel", "rawSpec": "^7.22.5", "saveSpec": null, "fetchSpec": "^7.22.5"}, "_requiredBy": ["/@babel/helper-module-transforms", "/@babel/plugin-transform-modules-commonjs"], "_resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "_shasum": "4938357dc7d782b80ed6dbb03a0fba3d22b1d5de", "_spec": "@babel/helper-simple-access@^7.22.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-module-transforms", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.5"}, "deprecated": false, "description": "Babel helper for ensuring that access to a given value is performed through simple accesses", "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-simple-access", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-simple-access", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-simple-access"}, "type": "commonjs", "version": "7.22.5"}