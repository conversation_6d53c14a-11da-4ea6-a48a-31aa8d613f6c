{"_from": "@babel/plugin-syntax-decorators@^7.24.0", "_id": "@babel/plugin-syntax-decorators@7.24.0", "_inBundle": false, "_integrity": "sha512-MXW3pQCu9gUiVGzqkGqsgiINDVYXoAnrY8FYF/rmb+OfufNF0zHMpHPN4ulRrinxYT8Vk/aZJxYqOKsDECjKAw==", "_location": "/@babel/plugin-syntax-decorators", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-decorators@^7.24.0", "name": "@babel/plugin-syntax-decorators", "escapedName": "@babel%2fplugin-syntax-decorators", "scope": "@babel", "rawSpec": "^7.24.0", "saveSpec": null, "fetchSpec": "^7.24.0"}, "_requiredBy": ["/@babel/plugin-proposal-decorators"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.24.0.tgz", "_shasum": "7a15e20aeaf412469c53ed0d5666f31a1fc41215", "_spec": "@babel/plugin-syntax-decorators@^7.24.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\plugin-proposal-decorators", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "deprecated": false, "description": "Allow parsing of decorators", "devDependencies": {"@babel/core": "^7.24.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-decorators", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-decorators", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-decorators"}, "type": "commonjs", "version": "7.24.0"}