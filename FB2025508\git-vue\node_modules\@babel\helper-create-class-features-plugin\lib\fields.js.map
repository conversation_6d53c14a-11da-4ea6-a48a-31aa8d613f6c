{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "_helperEnvironmentVisitor", "_helperMemberExpressionToFunctions", "_helperOptimiseCallExpression", "_helperAnnotateAsPure", "_helperSkipTransparentExpressionWrappers", "ts", "newHelpers", "file", "availableHelper", "buildPrivateNamesMap", "className", "privateFieldsAsSymbolsOrProperties", "props", "privateNamesMap", "Map", "classBrandId", "prop", "isPrivate", "name", "node", "key", "id", "update", "get", "isMethod", "isProperty", "isStatic", "static", "initAdded", "_classBrandId", "scope", "generateUidIdentifier", "method", "set", "isClassPrivateMethod", "kind", "body", "$", "length", "t", "isReturnStatement", "isCallExpression", "argument", "arguments", "isThisExpression", "isIdentifier", "callee", "getId", "cloneNode", "getterDeclared", "params", "isExpressionStatement", "expression", "setId", "setter<PERSON><PERSON><PERSON>ed", "methodId", "buildPrivateNamesNodes", "privateFieldsAsProperties", "privateFieldsAsSymbols", "state", "initNodes", "injectedIds", "Set", "value", "isGetterOrSetter", "init", "callExpression", "addHelper", "stringLiteral", "identifier", "has", "add", "newExpression", "annotateAsPure", "push", "template", "statement", "ast", "privateNameVisitorFactory", "visitor", "nestedVisitor", "traverse", "visitors", "merge", "Object", "assign", "environmentVisitor", "privateNameVisitor", "Class", "path", "visiblePrivateNames", "redeclared", "delete", "<PERSON><PERSON><PERSON>", "PrivateName", "noDocumentAll", "parentPath", "isMemberExpression", "property", "isOptionalMemberExpression", "includes", "handle", "unshadow", "innerBinding", "_scope", "hasBinding", "bindingIdentifierEquals", "rename", "parent", "buildCheckInRHS", "rhs", "inRHSIsObject", "privateInVisitor", "BinaryExpression", "operator", "left", "right", "isPrivateName", "classRef", "replaceWith", "readOnlyError", "writeOnlyError", "console", "warn", "buildUndefinedNode", "buildStaticPrivateFieldAccess", "expr", "noUninitializedPrivateFieldAccess", "memberExpression", "privateNameHandlerSpec", "memoise", "member", "count", "object", "memo", "maybeGenerateMemoised", "memoiser", "receiver", "helper<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "err", "sequenceExpression", "boundGet", "assignmentExpression", "destructureSet", "helper", "_unused", "Error", "getCall", "ref", "pop", "setCall", "buildCodeFrameError", "args", "computed", "arrayExpression", "slice", "call", "optimiseCall", "optionalCall", "privateNameHandlerLoose", "BASE", "REF", "PROP", "simpleSet", "optionalCallExpression", "transformPrivateNamesUsage", "size", "handler", "memberExpressionToFunctions", "buildPrivateFieldInitLoose", "inheritPropComments", "buildPrivateInstanceFieldInitSpec", "expressionStatement", "thisExpression", "buildPrivateStaticFieldInitSpec", "privateName", "variableDeclaration", "variableDeclarator", "buildPrivateStaticFieldInitSpecOld", "buildPrivateMethodInitLoose", "buildPrivateInstanceMethodInitSpec", "buildPrivateAccessorInitialization", "buildPrivateInstanceMethodInitialization", "buildPublicFieldInitLoose", "isLiteral", "buildPublicFieldInitSpec", "buildPrivateStaticMethodInitLoose", "buildPrivateMethodDeclaration", "generator", "async", "isGetter", "isSetter", "thisArg", "thisRef", "argumentsPath", "thisContextVisitor", "argumentsId", "unshift", "declId", "functionDeclaration", "Identifier", "UnaryExpression", "skipTransparentExprWrapperNodes", "booleanLiteral", "ThisExpression", "needsClassRef", "MetaProperty", "meta", "innerReferencesVisitor", "ReferencedIdentifier", "replaceThisContext", "innerBindingRef", "_state$thisRef", "isNameOrLength", "type", "inheritLeadingComments", "inheritInnerComments", "buildFieldsInitNodes", "superRef", "setPublicClassFields", "constant<PERSON>uper", "_ref", "_ref2", "classRefFlags", "injectSuperRef", "staticNodes", "instanceNodes", "lastInstanceNodeReturnsThis", "pureStaticNodes", "classBindingNode", "getSuperRef", "_injectSuperRef", "generateUidIdentifierBasedOnNode", "classRefForInnerBinding", "isClassProperty", "assertFieldTransformed", "isStaticBlock", "isInstance", "isPublic", "isField", "ReplaceSupers", "methodPath", "refToPreserve", "getObjectRef", "replace", "replaced", "blockBody", "inheritsComments", "filter", "Boolean", "wrapClass", "leadingComments", "remove", "superClass", "isClassExpression"], "sources": ["../src/fields.ts"], "sourcesContent": ["import { template, traverse, types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { Node<PERSON><PERSON>, Visitor, Scope } from \"@babel/traverse\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport memberExpressionToFunctions from \"@babel/helper-member-expression-to-functions\";\nimport type {\n  Hand<PERSON>,\n  HandlerState,\n} from \"@babel/helper-member-expression-to-functions\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { skipTransparentExprWrapperNodes } from \"@babel/helper-skip-transparent-expression-wrappers\";\n\nimport * as ts from \"./typescript.ts\";\n\ninterface PrivateNameMetadata {\n  id: t.Identifier;\n  static: boolean;\n  method: boolean;\n  getId?: t.Identifier;\n  setId?: t.Identifier;\n  methodId?: t.Identifier;\n  initAdded?: boolean;\n  getterDeclared?: boolean;\n  setterDeclared?: boolean;\n}\n\ntype PrivateNamesMapGeneric<V> = Map<string, V>;\n\ntype PrivateNamesMap = PrivateNamesMapGeneric<PrivateNameMetadata>;\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var newHelpers = (file: File) => {\n    if (!process.env.IS_PUBLISH) {\n      const { comments } = file.ast;\n      // This is needed for the test in\n      // babel-plugin-transform-class-properties/test/fixtures/regression/old-helpers\n      if (comments?.some(c => c.value.includes(\"@force-old-private-helpers\"))) {\n        return false;\n      }\n    }\n    return file.availableHelper(\"classPrivateFieldGet2\");\n  };\n}\n\nexport function buildPrivateNamesMap(\n  className: string,\n  privateFieldsAsSymbolsOrProperties: boolean,\n  props: PropPath[],\n  file: File,\n) {\n  const privateNamesMap: PrivateNamesMap = new Map();\n  let classBrandId: t.Identifier;\n  for (const prop of props) {\n    if (prop.isPrivate()) {\n      const { name } = prop.node.key.id;\n      let update: PrivateNameMetadata = privateNamesMap.get(name);\n      if (!update) {\n        const isMethod = !prop.isProperty();\n        const isStatic = prop.node.static;\n        let initAdded = false;\n        let id: t.Identifier;\n        if (\n          !privateFieldsAsSymbolsOrProperties &&\n          (process.env.BABEL_8_BREAKING || newHelpers(file)) &&\n          isMethod &&\n          !isStatic\n        ) {\n          initAdded = !!classBrandId;\n          classBrandId ??= prop.scope.generateUidIdentifier(\n            `${className}_brand`,\n          );\n          id = classBrandId;\n        } else {\n          id = prop.scope.generateUidIdentifier(name);\n        }\n        update = { id, static: isStatic, method: isMethod, initAdded };\n        privateNamesMap.set(name, update);\n      }\n      if (prop.isClassPrivateMethod()) {\n        if (prop.node.kind === \"get\") {\n          const { body } = prop.node.body;\n          let $: t.Node;\n          if (\n            // If we have\n            //   get #foo() { return _some_fn(this); }\n            // we can use _some_fn directly.\n            body.length === 1 &&\n            t.isReturnStatement(($ = body[0])) &&\n            t.isCallExpression(($ = $.argument)) &&\n            $.arguments.length === 1 &&\n            t.isThisExpression($.arguments[0]) &&\n            t.isIdentifier(($ = $.callee))\n          ) {\n            update.getId = t.cloneNode($);\n            update.getterDeclared = true;\n          } else {\n            update.getId = prop.scope.generateUidIdentifier(`get_${name}`);\n          }\n        } else if (prop.node.kind === \"set\") {\n          const { params } = prop.node;\n          const { body } = prop.node.body;\n          let $: t.Node;\n          if (\n            // If we have\n            //   set #foo(val) { _some_fn(this, val); }\n            // we can use _some_fn directly.\n            body.length === 1 &&\n            t.isExpressionStatement(($ = body[0])) &&\n            t.isCallExpression(($ = $.expression)) &&\n            $.arguments.length === 2 &&\n            t.isThisExpression($.arguments[0]) &&\n            t.isIdentifier($.arguments[1], {\n              name: (params[0] as t.Identifier).name,\n            }) &&\n            t.isIdentifier(($ = $.callee))\n          ) {\n            update.setId = t.cloneNode($);\n            update.setterDeclared = true;\n          } else {\n            update.setId = prop.scope.generateUidIdentifier(`set_${name}`);\n          }\n        } else if (prop.node.kind === \"method\") {\n          update.methodId = prop.scope.generateUidIdentifier(name);\n        }\n      }\n      privateNamesMap.set(name, update);\n    }\n  }\n  return privateNamesMap;\n}\n\nexport function buildPrivateNamesNodes(\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsProperties: boolean,\n  privateFieldsAsSymbols: boolean,\n  state: File,\n) {\n  const initNodes: t.Statement[] = [];\n\n  const injectedIds = new Set<string>();\n\n  for (const [name, value] of privateNamesMap) {\n    // - When the privateFieldsAsProperties assumption is enabled,\n    //   both static and instance fields are transpiled using a\n    //   secret non-enumerable property. Hence, we also need to generate that\n    //   key (using the classPrivateFieldLooseKey helper).\n    // - When the privateFieldsAsSymbols assumption is enabled,\n    //   both static and instance fields are transpiled using a\n    //   unique Symbol to define a non-enumerable property.\n    // - In spec mode, only instance fields need a \"private name\" initializer\n    //   because static fields are directly assigned to a variable in the\n    //   buildPrivateStaticFieldInitSpec function.\n    const { static: isStatic, method: isMethod, getId, setId } = value;\n    const isGetterOrSetter = getId || setId;\n    const id = t.cloneNode(value.id);\n\n    let init: t.Expression;\n\n    if (privateFieldsAsProperties) {\n      init = t.callExpression(state.addHelper(\"classPrivateFieldLooseKey\"), [\n        t.stringLiteral(name),\n      ]);\n    } else if (privateFieldsAsSymbols) {\n      init = t.callExpression(t.identifier(\"Symbol\"), [t.stringLiteral(name)]);\n    } else if (!isStatic) {\n      if (injectedIds.has(id.name)) continue;\n      injectedIds.add(id.name);\n\n      init = t.newExpression(\n        t.identifier(\n          isMethod &&\n            (process.env.BABEL_8_BREAKING ||\n              !isGetterOrSetter ||\n              newHelpers(state))\n            ? \"WeakSet\"\n            : \"WeakMap\",\n        ),\n        [],\n      );\n    }\n\n    if (init) {\n      if (!privateFieldsAsSymbols) {\n        annotateAsPure(init);\n      }\n      initNodes.push(template.statement.ast`var ${id} = ${init}`);\n    }\n  }\n\n  return initNodes;\n}\n\nexport interface PrivateNameVisitorState<V> {\n  privateNamesMap: PrivateNamesMapGeneric<V>;\n  redeclared?: string[];\n}\n\n// Traverses the class scope, handling private name references. If an inner\n// class redeclares the same private name, it will hand off traversal to the\n// restricted visitor (which doesn't traverse the inner class's inner scope).\nexport function privateNameVisitorFactory<S, V>(\n  visitor: Visitor<PrivateNameVisitorState<V> & S>,\n) {\n  // Traverses the outer portion of a class, without touching the class's inner\n  // scope, for private names.\n  const nestedVisitor = traverse.visitors.merge([\n    { ...visitor },\n    environmentVisitor,\n  ]);\n\n  // @ts-expect-error: TS2590: Expression produces a union type that is too complex to represent.\n  const privateNameVisitor: Visitor<\n    PrivateNameVisitorState<PrivateNameMetadata> & S\n  > = {\n    ...visitor,\n\n    Class(path) {\n      const { privateNamesMap } = this;\n      const body = path.get(\"body.body\");\n\n      const visiblePrivateNames = new Map(privateNamesMap);\n      const redeclared = [];\n      for (const prop of body) {\n        if (!prop.isPrivate()) continue;\n        const { name } = prop.node.key.id;\n        visiblePrivateNames.delete(name);\n        redeclared.push(name);\n      }\n\n      // If the class doesn't redeclare any private fields, we can continue with\n      // our overall traversal.\n      if (!redeclared.length) {\n        return;\n      }\n\n      // This class redeclares some private field. We need to process the outer\n      // environment with access to all the outer privates, then we can process\n      // the inner environment with only the still-visible outer privates.\n      path.get(\"body\").traverse(nestedVisitor, {\n        ...this,\n        redeclared,\n      });\n      path.traverse(privateNameVisitor, {\n        ...this,\n        privateNamesMap: visiblePrivateNames,\n      });\n\n      // We'll eventually hit this class node again with the overall Class\n      // Features visitor, which'll process the redeclared privates.\n      path.skipKey(\"body\");\n    },\n  };\n\n  return privateNameVisitor;\n}\n\ninterface PrivateNameState {\n  privateNamesMap: PrivateNamesMap;\n  classRef: t.Identifier;\n  file: File;\n  noDocumentAll: boolean;\n  noUninitializedPrivateFieldAccess: boolean;\n  innerBinding?: t.Identifier;\n}\n\nconst privateNameVisitor = privateNameVisitorFactory<\n  HandlerState<PrivateNameState> & PrivateNameState,\n  PrivateNameMetadata\n>({\n  PrivateName(path, { noDocumentAll }) {\n    const { privateNamesMap, redeclared } = this;\n    const { node, parentPath } = path;\n\n    if (\n      !parentPath.isMemberExpression({ property: node }) &&\n      !parentPath.isOptionalMemberExpression({ property: node })\n    ) {\n      return;\n    }\n    const { name } = node.id;\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared && redeclared.includes(name)) return;\n\n    this.handle(parentPath, noDocumentAll);\n  },\n});\n\n// rename all bindings that shadows innerBinding\nfunction unshadow(\n  name: string,\n  scope: Scope,\n  innerBinding: t.Identifier | undefined,\n) {\n  // in some cases, scope.getBinding(name) === undefined\n  // so we check hasBinding to avoid keeping looping\n  // see: https://github.com/babel/babel/pull/13656#discussion_r686030715\n  while (\n    scope?.hasBinding(name) &&\n    !scope.bindingIdentifierEquals(name, innerBinding)\n  ) {\n    scope.rename(name);\n    scope = scope.parent;\n  }\n}\n\nexport function buildCheckInRHS(\n  rhs: t.Expression,\n  file: File,\n  inRHSIsObject?: boolean,\n) {\n  if (inRHSIsObject || !file.availableHelper?.(\"checkInRHS\")) return rhs;\n  return t.callExpression(file.addHelper(\"checkInRHS\"), [rhs]);\n}\n\nconst privateInVisitor = privateNameVisitorFactory<\n  {\n    classRef: t.Identifier;\n    file: File;\n    innerBinding?: t.Identifier;\n    privateFieldsAsProperties: boolean;\n  },\n  PrivateNameMetadata\n>({\n  BinaryExpression(path, { file }) {\n    const { operator, left, right } = path.node;\n    if (operator !== \"in\") return;\n    if (!t.isPrivateName(left)) return;\n\n    const { privateFieldsAsProperties, privateNamesMap, redeclared } = this;\n\n    const { name } = left.id;\n\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared && redeclared.includes(name)) return;\n\n    // if there are any local variable shadowing classRef, unshadow it\n    // see #12960\n    unshadow(this.classRef.name, path.scope, this.innerBinding);\n\n    if (privateFieldsAsProperties) {\n      const { id } = privateNamesMap.get(name);\n      path.replaceWith(template.expression.ast`\n        Object.prototype.hasOwnProperty.call(${buildCheckInRHS(\n          right,\n          file,\n        )}, ${t.cloneNode(id)})\n      `);\n      return;\n    }\n\n    const { id, static: isStatic } = privateNamesMap.get(name);\n\n    if (isStatic) {\n      path.replaceWith(\n        template.expression.ast`${buildCheckInRHS(\n          right,\n          file,\n        )} === ${t.cloneNode(this.classRef)}`,\n      );\n      return;\n    }\n\n    path.replaceWith(\n      template.expression.ast`${t.cloneNode(id)}.has(${buildCheckInRHS(\n        right,\n        file,\n      )})`,\n    );\n  },\n});\n\ninterface Receiver {\n  receiver(\n    this: HandlerState<PrivateNameState> & PrivateNameState,\n    member: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n  ): t.Expression;\n}\n\nfunction readOnlyError(file: File, name: string) {\n  return t.callExpression(file.addHelper(\"readOnlyError\"), [\n    t.stringLiteral(`#${name}`),\n  ]);\n}\n\nfunction writeOnlyError(file: File, name: string) {\n  if (\n    !process.env.BABEL_8_BREAKING &&\n    !file.availableHelper(\"writeOnlyError\")\n  ) {\n    console.warn(\n      `@babel/helpers is outdated, update it to silence this warning.`,\n    );\n    return t.buildUndefinedNode();\n  }\n  return t.callExpression(file.addHelper(\"writeOnlyError\"), [\n    t.stringLiteral(`#${name}`),\n  ]);\n}\n\nfunction buildStaticPrivateFieldAccess<N extends t.Expression>(\n  expr: N,\n  noUninitializedPrivateFieldAccess: boolean,\n) {\n  if (noUninitializedPrivateFieldAccess) return expr;\n  return t.memberExpression(expr, t.identifier(\"_\"));\n}\n\nconst privateNameHandlerSpec: Handler<PrivateNameState & Receiver> & Receiver =\n  {\n    memoise(member, count) {\n      const { scope } = member;\n      const { object } = member.node as { object: t.Expression };\n\n      const memo = scope.maybeGenerateMemoised(object);\n      if (!memo) {\n        return;\n      }\n\n      this.memoiser.set(object, memo, count);\n    },\n\n    receiver(member) {\n      const { object } = member.node as { object: t.Expression };\n\n      if (this.memoiser.has(object)) {\n        return t.cloneNode(this.memoiser.get(object));\n      }\n\n      return t.cloneNode(object);\n    },\n\n    get(member) {\n      const {\n        classRef,\n        privateNamesMap,\n        file,\n        innerBinding,\n        noUninitializedPrivateFieldAccess,\n      } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        methodId,\n        getId,\n        setId,\n      } = privateNamesMap.get(name);\n      const isGetterOrSetter = getId || setId;\n\n      if (isStatic) {\n        // if there are any local variable shadowing classRef, unshadow it\n        // see #12960\n        unshadow(classRef.name, member.scope, innerBinding);\n\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          // NOTE: This package has a peerDependency on @babel/core@^7.0.0, but these\n          // helpers have been introduced in @babel/helpers@7.1.0.\n          const helperName =\n            isMethod && !isGetterOrSetter\n              ? \"classStaticPrivateMethodGet\"\n              : \"classStaticPrivateFieldSpecGet\";\n\n          return t.callExpression(file.addHelper(helperName), [\n            this.receiver(member),\n            t.cloneNode(classRef),\n            t.cloneNode(id),\n          ]);\n        }\n\n        const receiver = this.receiver(member);\n        const skipCheck =\n          t.isIdentifier(receiver) && receiver.name === classRef.name;\n\n        if (!isMethod) {\n          if (skipCheck) {\n            return buildStaticPrivateFieldAccess(\n              t.cloneNode(id),\n              noUninitializedPrivateFieldAccess,\n            );\n          }\n\n          return buildStaticPrivateFieldAccess(\n            t.callExpression(file.addHelper(\"assertClassBrand\"), [\n              t.cloneNode(classRef),\n              receiver,\n              t.cloneNode(id),\n            ]),\n            noUninitializedPrivateFieldAccess,\n          );\n        }\n\n        if (getId) {\n          if (skipCheck) {\n            return t.callExpression(t.cloneNode(getId), [receiver]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateGetter\"), [\n            t.cloneNode(classRef),\n            receiver,\n            t.cloneNode(getId),\n          ]);\n        }\n\n        if (setId) {\n          const err = t.buildUndefinedNode(); // TODO: writeOnlyError(file, name)\n          if (skipCheck) return err;\n          return t.sequenceExpression([\n            t.callExpression(file.addHelper(\"assertClassBrand\"), [\n              t.cloneNode(classRef),\n              receiver,\n            ]),\n            err,\n          ]);\n        }\n\n        if (skipCheck) return t.cloneNode(id);\n        return t.callExpression(file.addHelper(\"assertClassBrand\"), [\n          t.cloneNode(classRef),\n          receiver,\n          t.cloneNode(id),\n        ]);\n      }\n\n      if (isMethod) {\n        if (isGetterOrSetter) {\n          if (!getId) {\n            return t.sequenceExpression([\n              this.receiver(member),\n              writeOnlyError(file, name),\n            ]);\n          }\n          if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n            return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n              this.receiver(member),\n              t.cloneNode(id),\n            ]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateGetter\"), [\n            t.cloneNode(id),\n            this.receiver(member),\n            t.cloneNode(getId),\n          ]);\n        }\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          return t.callExpression(file.addHelper(\"classPrivateMethodGet\"), [\n            this.receiver(member),\n            t.cloneNode(id),\n            t.cloneNode(methodId),\n          ]);\n        }\n        return t.callExpression(file.addHelper(\"assertClassBrand\"), [\n          t.cloneNode(id),\n          this.receiver(member),\n          t.cloneNode(methodId),\n        ]);\n      }\n      if (process.env.BABEL_8_BREAKING || newHelpers(file)) {\n        return t.callExpression(file.addHelper(\"classPrivateFieldGet2\"), [\n          t.cloneNode(id),\n          this.receiver(member),\n        ]);\n      }\n\n      return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n        this.receiver(member),\n        t.cloneNode(id),\n      ]);\n    },\n\n    boundGet(member) {\n      this.memoise(member, 1);\n\n      return t.callExpression(\n        t.memberExpression(this.get(member), t.identifier(\"bind\")),\n        [this.receiver(member)],\n      );\n    },\n\n    set(member, value) {\n      const {\n        classRef,\n        privateNamesMap,\n        file,\n        noUninitializedPrivateFieldAccess,\n      } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        setId,\n        getId,\n      } = privateNamesMap.get(name);\n      const isGetterOrSetter = getId || setId;\n\n      if (isStatic) {\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          const helperName =\n            isMethod && !isGetterOrSetter\n              ? \"classStaticPrivateMethodSet\"\n              : \"classStaticPrivateFieldSpecSet\";\n\n          return t.callExpression(file.addHelper(helperName), [\n            this.receiver(member),\n            t.cloneNode(classRef),\n            t.cloneNode(id),\n            value,\n          ]);\n        }\n\n        const receiver = this.receiver(member);\n        const skipCheck =\n          t.isIdentifier(receiver) && receiver.name === classRef.name;\n\n        if (isMethod && !setId) {\n          const err = readOnlyError(file, name);\n          if (skipCheck) return t.sequenceExpression([value, err]);\n          return t.sequenceExpression([\n            value,\n            t.callExpression(file.addHelper(\"assertClassBrand\"), [\n              t.cloneNode(classRef),\n              receiver,\n            ]),\n            readOnlyError(file, name),\n          ]);\n        }\n\n        if (setId) {\n          if (skipCheck) {\n            return t.callExpression(t.cloneNode(setId), [receiver, value]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateSetter\"), [\n            t.cloneNode(classRef),\n            t.cloneNode(setId),\n            receiver,\n            value,\n          ]);\n        }\n        return t.assignmentExpression(\n          \"=\",\n          buildStaticPrivateFieldAccess(\n            t.cloneNode(id),\n            noUninitializedPrivateFieldAccess,\n          ),\n          skipCheck\n            ? value\n            : t.callExpression(file.addHelper(\"assertClassBrand\"), [\n                t.cloneNode(classRef),\n                receiver,\n                value,\n              ]),\n        );\n      }\n      if (isMethod) {\n        if (setId) {\n          if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n            return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n              this.receiver(member),\n              t.cloneNode(id),\n              value,\n            ]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateSetter\"), [\n            t.cloneNode(id),\n            t.cloneNode(setId),\n            this.receiver(member),\n            value,\n          ]);\n        }\n        return t.sequenceExpression([\n          this.receiver(member),\n          value,\n          readOnlyError(file, name),\n        ]);\n      }\n\n      if (process.env.BABEL_8_BREAKING || newHelpers(file)) {\n        return t.callExpression(file.addHelper(\"classPrivateFieldSet2\"), [\n          t.cloneNode(id),\n          this.receiver(member),\n          value,\n        ]);\n      }\n\n      return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n        this.receiver(member),\n        t.cloneNode(id),\n        value,\n      ]);\n    },\n\n    destructureSet(member) {\n      const {\n        classRef,\n        privateNamesMap,\n        file,\n        noUninitializedPrivateFieldAccess,\n      } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        setId,\n      } = privateNamesMap.get(name);\n\n      if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n        if (isStatic) {\n          try {\n            // classStaticPrivateFieldDestructureSet was introduced in 7.13.10\n            // eslint-disable-next-line no-var\n            var helper = file.addHelper(\n              \"classStaticPrivateFieldDestructureSet\",\n            );\n          } catch {\n            throw new Error(\n              \"Babel can not transpile `[C.#p] = [0]` with @babel/helpers < 7.13.10, \\n\" +\n                \"please update @babel/helpers to the latest version.\",\n            );\n          }\n          return t.memberExpression(\n            t.callExpression(helper, [\n              this.receiver(member),\n              t.cloneNode(classRef),\n              t.cloneNode(id),\n            ]),\n            t.identifier(\"value\"),\n          );\n        }\n\n        return t.memberExpression(\n          t.callExpression(file.addHelper(\"classPrivateFieldDestructureSet\"), [\n            this.receiver(member),\n            t.cloneNode(id),\n          ]),\n          t.identifier(\"value\"),\n        );\n      }\n\n      if (isMethod && !setId) {\n        return t.memberExpression(\n          t.sequenceExpression([\n            // @ts-ignore(Babel 7 vs Babel 8) member.node.object is not t.Super\n            member.node.object,\n            readOnlyError(file, name),\n          ]),\n          t.identifier(\"_\"),\n        );\n      }\n\n      if (isStatic && !isMethod) {\n        const getCall = this.get(member);\n        if (\n          !noUninitializedPrivateFieldAccess ||\n          !t.isCallExpression(getCall)\n        ) {\n          return getCall;\n        }\n        const ref = getCall.arguments.pop();\n        getCall.arguments.push(template.expression.ast`(_) => ${ref} = _`);\n        return t.memberExpression(\n          t.callExpression(file.addHelper(\"toSetter\"), [getCall]),\n          t.identifier(\"_\"),\n        );\n      }\n\n      const setCall = this.set(member, t.identifier(\"_\"));\n      if (\n        !t.isCallExpression(setCall) ||\n        !t.isIdentifier(setCall.arguments[setCall.arguments.length - 1], {\n          name: \"_\",\n        })\n      ) {\n        throw member.buildCodeFrameError(\n          \"Internal Babel error while compiling this code. This is a Babel bug. \" +\n            \"Please report it at https://github.com/babel/babel/issues.\",\n        );\n      }\n\n      // someHelper(foo, bar, _) -> someHelper, [foo, bar]\n      // aFn.call(foo, bar, _) -> aFn, [bar], foo\n      let args: t.Expression[];\n      if (\n        t.isMemberExpression(setCall.callee, { computed: false }) &&\n        t.isIdentifier(setCall.callee.property) &&\n        setCall.callee.property.name === \"call\"\n      ) {\n        args = [\n          // @ts-ignore(Babel 7 vs Babel 8) member.node.object is not t.Super\n          setCall.callee.object,\n          t.arrayExpression(\n            // Remove '_'\n            (setCall.arguments as t.Expression[]).slice(1, -1),\n          ),\n          setCall.arguments[0] as t.Expression,\n        ];\n      } else {\n        args = [\n          setCall.callee as t.Expression,\n          t.arrayExpression(\n            // Remove '_'\n            (setCall.arguments as t.Expression[]).slice(0, -1),\n          ),\n        ];\n      }\n\n      return t.memberExpression(\n        t.callExpression(file.addHelper(\"toSetter\"), args),\n        t.identifier(\"_\"),\n      );\n    },\n\n    call(member, args: (t.Expression | t.SpreadElement)[]) {\n      // The first access (the get) should do the memo assignment.\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, false);\n    },\n\n    optionalCall(member, args: (t.Expression | t.SpreadElement)[]) {\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, true);\n    },\n\n    delete() {\n      throw new Error(\n        \"Internal Babel error: deleting private elements is a parsing error.\",\n      );\n    },\n  };\n\nconst privateNameHandlerLoose: Handler<PrivateNameState> = {\n  get(member) {\n    const { privateNamesMap, file } = this;\n    const { object } = member.node;\n    const { name } = (member.node.property as t.PrivateName).id;\n\n    return template.expression`BASE(REF, PROP)[PROP]`({\n      BASE: file.addHelper(\"classPrivateFieldLooseBase\"),\n      REF: t.cloneNode(object),\n      PROP: t.cloneNode(privateNamesMap.get(name).id),\n    });\n  },\n\n  set() {\n    // noop\n    throw new Error(\"private name handler with loose = true don't need set()\");\n  },\n\n  boundGet(member) {\n    return t.callExpression(\n      t.memberExpression(this.get(member), t.identifier(\"bind\")),\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      [t.cloneNode(member.node.object as t.Expression)],\n    );\n  },\n\n  simpleSet(member) {\n    return this.get(member);\n  },\n\n  destructureSet(member) {\n    return this.get(member);\n  },\n\n  call(member, args) {\n    return t.callExpression(this.get(member), args);\n  },\n\n  optionalCall(member, args) {\n    return t.optionalCallExpression(this.get(member), args, true);\n  },\n\n  delete() {\n    throw new Error(\n      \"Internal Babel error: deleting private elements is a parsing error.\",\n    );\n  },\n};\n\nexport function transformPrivateNamesUsage(\n  ref: t.Identifier,\n  path: NodePath<t.Class>,\n  privateNamesMap: PrivateNamesMap,\n  {\n    privateFieldsAsProperties,\n    noUninitializedPrivateFieldAccess,\n    noDocumentAll,\n    innerBinding,\n  }: {\n    privateFieldsAsProperties: boolean;\n    noUninitializedPrivateFieldAccess: boolean;\n    noDocumentAll: boolean;\n    innerBinding: t.Identifier;\n  },\n  state: File,\n) {\n  if (!privateNamesMap.size) return;\n\n  const body = path.get(\"body\");\n  const handler = privateFieldsAsProperties\n    ? privateNameHandlerLoose\n    : privateNameHandlerSpec;\n\n  memberExpressionToFunctions<PrivateNameState>(body, privateNameVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    ...handler,\n    noDocumentAll,\n    noUninitializedPrivateFieldAccess,\n    innerBinding,\n  });\n  body.traverse(privateInVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    privateFieldsAsProperties,\n    innerBinding,\n  });\n}\n\nfunction buildPrivateFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    template.statement.ast`\n      Object.defineProperty(${ref}, ${t.cloneNode(id)}, {\n        // configurable is false by default\n        // enumerable is false by default\n        writable: true,\n        value: ${value}\n      });\n    ` as t.ExpressionStatement,\n    prop,\n  );\n}\n\nfunction buildPrivateInstanceFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`${t.cloneNode(id)}.set(${ref}, {\n          // configurable is always false for private elements\n          // enumerable is always false for private elements\n          writable: true,\n          value: ${value},\n        })` as t.ExpressionStatement,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return inheritPropComments(\n    t.expressionStatement(\n      t.callExpression(helper, [\n        t.thisExpression(),\n        t.cloneNode(id),\n        process.env.BABEL_8_BREAKING || newHelpers(state)\n          ? value\n          : template.expression.ast`{ writable: true, value: ${value} }`,\n      ]),\n    ),\n    prop,\n  );\n}\n\nfunction buildPrivateStaticFieldInitSpec(\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n  noUninitializedPrivateFieldAccess: boolean,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n\n  const value = noUninitializedPrivateFieldAccess\n    ? prop.node.value\n    : template.expression.ast`{\n        _: ${prop.node.value || t.buildUndefinedNode()}\n      }`;\n\n  return inheritPropComments(\n    t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.cloneNode(privateName.id), value),\n    ]),\n    prop,\n  );\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var buildPrivateStaticFieldInitSpecOld = function (\n    prop: NodePath<t.ClassPrivateProperty>,\n    privateNamesMap: PrivateNamesMap,\n  ) {\n    const privateName = privateNamesMap.get(prop.node.key.id.name);\n    const { id, getId, setId, initAdded } = privateName;\n    const isGetterOrSetter = getId || setId;\n\n    if (!prop.isProperty() && (initAdded || !isGetterOrSetter)) return;\n\n    if (isGetterOrSetter) {\n      privateNamesMap.set(prop.node.key.id.name, {\n        ...privateName,\n        initAdded: true,\n      });\n\n      return inheritPropComments(\n        template.statement.ast`\n          var ${t.cloneNode(id)} = {\n            // configurable is false by default\n            // enumerable is false by default\n            // writable is false by default\n            get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n            set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n          }\n        `,\n        prop,\n      );\n    }\n\n    const value = prop.node.value || prop.scope.buildUndefinedNode();\n    return inheritPropComments(\n      template.statement.ast`\n        var ${t.cloneNode(id)} = {\n          // configurable is false by default\n          // enumerable is false by default\n          writable: true,\n          value: ${value}\n        };\n      `,\n      prop,\n    );\n  };\n}\n\nfunction buildPrivateMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { methodId, id, getId, setId, initAdded } = privateName;\n  if (initAdded) return;\n\n  if (methodId) {\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          value: ${methodId.name}\n        });\n      ` as t.ExpressionStatement,\n      prop,\n    );\n  }\n  const isGetterOrSetter = getId || setId;\n  if (isGetterOrSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n          set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n        });\n      ` as t.ExpressionStatement,\n      prop,\n    );\n  }\n}\n\nfunction buildPrivateInstanceMethodInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n\n  if (privateName.initAdded) return;\n\n  if (!process.env.BABEL_8_BREAKING && !newHelpers(state)) {\n    const isGetterOrSetter = privateName.getId || privateName.setId;\n    if (isGetterOrSetter) {\n      return buildPrivateAccessorInitialization(\n        ref,\n        prop,\n        privateNamesMap,\n        state,\n      );\n    }\n  }\n\n  return buildPrivateInstanceMethodInitialization(\n    ref,\n    prop,\n    privateNamesMap,\n    state,\n  );\n}\n\nfunction buildPrivateAccessorInitialization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, getId, setId } = privateName;\n\n  privateNamesMap.set(prop.node.key.id.name, {\n    ...privateName,\n    initAdded: true,\n  });\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`\n          ${id}.set(${ref}, {\n            get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n            set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n          });\n        ` as t.ExpressionStatement,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return inheritPropComments(\n    template.statement.ast`${helper}(\n      ${t.thisExpression()},\n      ${t.cloneNode(id)},\n      {\n        get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n        set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n      },\n    )` as t.ExpressionStatement,\n    prop,\n  );\n}\n\nfunction buildPrivateInstanceMethodInitialization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id } = privateName;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateMethodInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`${id}.add(${ref})` as t.ExpressionStatement,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateMethodInitSpec\");\n  return inheritPropComments(\n    template.statement.ast`${helper}(\n      ${t.thisExpression()},\n      ${t.cloneNode(id)}\n    )` as t.ExpressionStatement,\n    prop,\n  );\n}\n\nfunction buildPublicFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(ref, key, computed || t.isLiteral(key)),\n        value,\n      ),\n    ),\n    prop,\n  );\n}\n\nfunction buildPublicFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n  state: File,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    t.expressionStatement(\n      t.callExpression(state.addHelper(\"defineProperty\"), [\n        ref,\n        computed || t.isLiteral(key)\n          ? key\n          : t.stringLiteral((key as t.Identifier).name),\n        value,\n      ]),\n    ),\n    prop,\n  );\n}\n\nfunction buildPrivateStaticMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  state: File,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, methodId, getId, setId, initAdded } = privateName;\n\n  if (initAdded) return;\n\n  const isGetterOrSetter = getId || setId;\n  if (isGetterOrSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n          set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n        })\n      `,\n      prop,\n    );\n  }\n\n  return inheritPropComments(\n    template.statement.ast`\n      Object.defineProperty(${ref}, ${id}, {\n        // configurable is false by default\n        // enumerable is false by default\n        // writable is false by default\n        value: ${methodId.name}\n      });\n    `,\n    prop,\n  );\n}\n\nfunction buildPrivateMethodDeclaration(\n  file: File,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsSymbolsOrProperties = false,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const {\n    id,\n    methodId,\n    getId,\n    setId,\n    getterDeclared,\n    setterDeclared,\n    static: isStatic,\n  } = privateName;\n  const { params, body, generator, async } = prop.node;\n  const isGetter = getId && params.length === 0;\n  const isSetter = setId && params.length > 0;\n\n  if ((isGetter && getterDeclared) || (isSetter && setterDeclared)) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n    return null;\n  }\n\n  if (\n    (process.env.BABEL_8_BREAKING || newHelpers(file)) &&\n    (isGetter || isSetter) &&\n    !privateFieldsAsSymbolsOrProperties\n  ) {\n    const scope = prop.get(\"body\").scope;\n    const thisArg = scope.generateUidIdentifier(\"this\");\n    const state: ReplaceThisState = {\n      thisRef: thisArg,\n      argumentsPath: [],\n    };\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    prop.traverse(thisContextVisitor, state);\n    if (state.argumentsPath.length) {\n      const argumentsId = scope.generateUidIdentifier(\"arguments\");\n      scope.push({\n        id: argumentsId,\n        init: template.expression.ast`[].slice.call(arguments, 1)`,\n      });\n      for (const path of state.argumentsPath) {\n        path.replaceWith(t.cloneNode(argumentsId));\n      }\n    }\n\n    params.unshift(t.cloneNode(thisArg));\n  }\n\n  let declId = methodId;\n\n  if (isGetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      getterDeclared: true,\n      initAdded: true,\n    });\n    declId = getId;\n  } else if (isSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      setterDeclared: true,\n      initAdded: true,\n    });\n    declId = setId;\n  } else if (isStatic && !privateFieldsAsSymbolsOrProperties) {\n    declId = id;\n  }\n\n  return inheritPropComments(\n    t.functionDeclaration(\n      t.cloneNode(declId),\n      // @ts-expect-error params for ClassMethod has TSParameterProperty\n      params,\n      body,\n      generator,\n      async,\n    ),\n    prop,\n  );\n}\n\ntype ReplaceThisState = {\n  thisRef: t.Identifier;\n  needsClassRef?: boolean;\n  innerBinding?: t.Identifier | null;\n  argumentsPath?: NodePath<t.Identifier>[];\n};\n\ntype ReplaceInnerBindingReferenceState = ReplaceThisState;\n\nconst thisContextVisitor = traverse.visitors.merge<ReplaceThisState>([\n  {\n    Identifier(path, state) {\n      if (state.argumentsPath && path.node.name === \"arguments\") {\n        state.argumentsPath.push(path);\n      }\n    },\n    UnaryExpression(path) {\n      // Replace `delete this` with `true`\n      const { node } = path;\n      if (node.operator === \"delete\") {\n        const argument = skipTransparentExprWrapperNodes(node.argument);\n        if (t.isThisExpression(argument)) {\n          path.replaceWith(t.booleanLiteral(true));\n        }\n      }\n    },\n    ThisExpression(path, state) {\n      state.needsClassRef = true;\n      path.replaceWith(t.cloneNode(state.thisRef));\n    },\n    MetaProperty(path) {\n      const { node, scope } = path;\n      // if there are `new.target` in static field\n      // we should replace it with `undefined`\n      if (node.meta.name === \"new\" && node.property.name === \"target\") {\n        path.replaceWith(scope.buildUndefinedNode());\n      }\n    },\n  },\n  environmentVisitor,\n]);\n\nconst innerReferencesVisitor: Visitor<ReplaceInnerBindingReferenceState> = {\n  ReferencedIdentifier(path, state) {\n    if (\n      path.scope.bindingIdentifierEquals(path.node.name, state.innerBinding)\n    ) {\n      state.needsClassRef = true;\n      path.node.name = state.thisRef.name;\n    }\n  },\n};\n\nfunction replaceThisContext(\n  path: PropPath,\n  ref: t.Identifier,\n  innerBindingRef: t.Identifier | null,\n) {\n  const state: ReplaceThisState = {\n    thisRef: ref,\n    needsClassRef: false,\n    innerBinding: innerBindingRef,\n  };\n  if (!path.isMethod()) {\n    // replace `this` in property initializers and static blocks\n    path.traverse(thisContextVisitor, state);\n  }\n\n  // todo: use innerBinding.referencePaths to avoid full traversal\n  if (\n    innerBindingRef != null &&\n    state.thisRef?.name &&\n    state.thisRef.name !== innerBindingRef.name\n  ) {\n    path.traverse(innerReferencesVisitor, state);\n  }\n\n  return state.needsClassRef;\n}\n\nexport type PropNode =\n  | t.ClassProperty\n  | t.ClassPrivateMethod\n  | t.ClassPrivateProperty\n  | t.StaticBlock;\nexport type PropPath = NodePath<PropNode>;\n\nfunction isNameOrLength({ key, computed }: t.ClassProperty) {\n  if (key.type === \"Identifier\") {\n    return !computed && (key.name === \"name\" || key.name === \"length\");\n  }\n  if (key.type === \"StringLiteral\") {\n    return key.value === \"name\" || key.value === \"length\";\n  }\n  return false;\n}\n\n/**\n * Inherit comments from class members. This is a reduced version of\n * t.inheritsComments: the trailing comments are not inherited because\n * for most class members except the last one, their trailing comments are\n * the next sibling's leading comments.\n *\n * @template T transformed class member type\n * @param {T} node transformed class member\n * @param {PropPath} prop class member\n * @returns transformed class member type with comments inherited\n */\nfunction inheritPropComments<T extends t.Node>(node: T, prop: PropPath) {\n  t.inheritLeadingComments(node, prop.node);\n  t.inheritInnerComments(node, prop.node);\n  return node;\n}\n\n/**\n * ClassRefFlag records the requirement of the class binding reference.\n *\n * @enum {number}\n */\nconst enum ClassRefFlag {\n  None,\n  /**\n   * When this flag is enabled, the binding reference can be the class id,\n   * if exists, or the uid identifier generated for class expression. The\n   * reference is safe to be consumed by [[Define]].\n   */\n  ForDefine = 1 << 0,\n  /**\n   * When this flag is enabled, the reference must be a uid, because the outer\n   * class binding can be mutated by user codes.\n   * E.g.\n   * class C { static p = C }; const oldC = C; C = null; oldC.p;\n   * we must memoize class `C` before defining the property `p`.\n   */\n  ForInnerBinding = 1 << 1,\n}\n\nexport function buildFieldsInitNodes(\n  ref: t.Identifier | null,\n  superRef: t.Expression | undefined,\n  props: PropPath[],\n  privateNamesMap: PrivateNamesMap,\n  file: File,\n  setPublicClassFields: boolean,\n  privateFieldsAsSymbolsOrProperties: boolean,\n  noUninitializedPrivateFieldAccess: boolean,\n  constantSuper: boolean,\n  innerBindingRef: t.Identifier | null,\n) {\n  let classRefFlags = ClassRefFlag.None;\n  let injectSuperRef: t.Identifier;\n  const staticNodes: t.Statement[] = [];\n  const instanceNodes: t.ExpressionStatement[] = [];\n  let lastInstanceNodeReturnsThis = false;\n  // These nodes are pure and can be moved to the closest statement position\n  const pureStaticNodes: t.FunctionDeclaration[] = [];\n  let classBindingNode: t.ExpressionStatement | null = null;\n\n  const getSuperRef = t.isIdentifier(superRef)\n    ? () => superRef\n    : () => {\n        injectSuperRef ??=\n          props[0].scope.generateUidIdentifierBasedOnNode(superRef);\n        return injectSuperRef;\n      };\n\n  const classRefForInnerBinding =\n    ref ??\n    props[0].scope.generateUidIdentifier(innerBindingRef?.name || \"Class\");\n  ref ??= t.cloneNode(innerBindingRef);\n\n  for (const prop of props) {\n    prop.isClassProperty() && ts.assertFieldTransformed(prop);\n\n    // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n    const isStatic = !t.isStaticBlock?.(prop.node) && prop.node.static;\n    const isInstance = !isStatic;\n    const isPrivate = prop.isPrivate();\n    const isPublic = !isPrivate;\n    const isField = prop.isProperty();\n    const isMethod = !isField;\n    const isStaticBlock = prop.isStaticBlock?.();\n\n    if (isStatic) classRefFlags |= ClassRefFlag.ForDefine;\n\n    if (isStatic || (isMethod && isPrivate) || isStaticBlock) {\n      new ReplaceSupers({\n        methodPath: prop,\n        constantSuper,\n        file: file,\n        refToPreserve: innerBindingRef,\n        getSuperRef,\n        getObjectRef() {\n          classRefFlags |= ClassRefFlag.ForInnerBinding;\n          if (isStatic || isStaticBlock) {\n            return classRefForInnerBinding;\n          } else {\n            return t.memberExpression(\n              classRefForInnerBinding,\n              t.identifier(\"prototype\"),\n            );\n          }\n        },\n      }).replace();\n\n      const replaced = replaceThisContext(\n        prop,\n        classRefForInnerBinding,\n        innerBindingRef,\n      );\n      if (replaced) {\n        classRefFlags |= ClassRefFlag.ForInnerBinding;\n      }\n    }\n\n    lastInstanceNodeReturnsThis = false;\n\n    // TODO(ts): there are so many `ts-expect-error` inside cases since\n    // ts can not infer type from pre-computed values (or a case test)\n    // even change `isStaticBlock` to `t.isStaticBlock(prop)` will not make prop\n    // a `NodePath<t.StaticBlock>`\n    // this maybe a bug for ts\n    switch (true) {\n      case isStaticBlock: {\n        const blockBody = (prop.node as t.StaticBlock).body;\n        // We special-case the single expression case to avoid the iife, since\n        // it's common.\n        if (blockBody.length === 1 && t.isExpressionStatement(blockBody[0])) {\n          staticNodes.push(inheritPropComments(blockBody[0], prop));\n        } else {\n          staticNodes.push(\n            t.inheritsComments(\n              template.statement.ast`(() => { ${blockBody} })()`,\n              prop.node,\n            ),\n          );\n        }\n        break;\n      }\n      case isStatic &&\n        isPrivate &&\n        isField &&\n        privateFieldsAsSymbolsOrProperties:\n        staticNodes.push(\n          buildPrivateFieldInitLoose(t.cloneNode(ref), prop, privateNamesMap),\n        );\n        break;\n      case isStatic &&\n        isPrivate &&\n        isField &&\n        !privateFieldsAsSymbolsOrProperties:\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          staticNodes.push(\n            buildPrivateStaticFieldInitSpecOld(prop, privateNamesMap),\n          );\n        } else {\n          staticNodes.push(\n            buildPrivateStaticFieldInitSpec(\n              prop,\n              privateNamesMap,\n              noUninitializedPrivateFieldAccess,\n            ),\n          );\n        }\n        break;\n      case isStatic && isPublic && isField && setPublicClassFields:\n        // Functions always have non-writable .name and .length properties,\n        // so we must always use [[Define]] for them.\n        // It might still be possible to a computed static fields whose resulting\n        // key is \"name\" or \"length\", but the assumption is telling us that it's\n        // not going to happen.\n        // @ts-expect-error checked in switch\n        if (!isNameOrLength(prop.node)) {\n          // @ts-expect-error checked in switch\n          staticNodes.push(buildPublicFieldInitLoose(t.cloneNode(ref), prop));\n          break;\n        }\n      // falls through\n      case isStatic && isPublic && isField && !setPublicClassFields:\n        staticNodes.push(\n          // @ts-expect-error checked in switch\n          buildPublicFieldInitSpec(t.cloneNode(ref), prop, file),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isField &&\n        privateFieldsAsSymbolsOrProperties:\n        instanceNodes.push(\n          buildPrivateFieldInitLoose(t.thisExpression(), prop, privateNamesMap),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isField &&\n        !privateFieldsAsSymbolsOrProperties:\n        instanceNodes.push(\n          buildPrivateInstanceFieldInitSpec(\n            t.thisExpression(),\n            prop,\n            privateNamesMap,\n            file,\n          ),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isMethod &&\n        privateFieldsAsSymbolsOrProperties:\n        instanceNodes.unshift(\n          buildPrivateMethodInitLoose(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isMethod &&\n        !privateFieldsAsSymbolsOrProperties:\n        instanceNodes.unshift(\n          buildPrivateInstanceMethodInitSpec(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            file,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isStatic &&\n        isPrivate &&\n        isMethod &&\n        !privateFieldsAsSymbolsOrProperties:\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          staticNodes.unshift(\n            // @ts-expect-error checked in switch\n            buildPrivateStaticFieldInitSpecOld(prop, privateNamesMap),\n          );\n        }\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isStatic &&\n        isPrivate &&\n        isMethod &&\n        privateFieldsAsSymbolsOrProperties:\n        staticNodes.unshift(\n          buildPrivateStaticMethodInitLoose(\n            t.cloneNode(ref),\n            // @ts-expect-error checked in switch\n            prop,\n            file,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isInstance && isPublic && isField && setPublicClassFields:\n        // @ts-expect-error checked in switch\n        instanceNodes.push(buildPublicFieldInitLoose(t.thisExpression(), prop));\n        break;\n      case isInstance && isPublic && isField && !setPublicClassFields:\n        lastInstanceNodeReturnsThis = true;\n        instanceNodes.push(\n          // @ts-expect-error checked in switch\n          buildPublicFieldInitSpec(t.thisExpression(), prop, file),\n        );\n        break;\n      default:\n        throw new Error(\"Unreachable.\");\n    }\n  }\n\n  if (classRefFlags & ClassRefFlag.ForInnerBinding && innerBindingRef != null) {\n    classBindingNode = t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.cloneNode(classRefForInnerBinding),\n        t.cloneNode(innerBindingRef),\n      ),\n    );\n  }\n\n  return {\n    staticNodes: staticNodes.filter(Boolean),\n    instanceNodes: instanceNodes.filter(Boolean),\n    lastInstanceNodeReturnsThis,\n    pureStaticNodes: pureStaticNodes.filter(Boolean),\n    classBindingNode,\n    wrapClass(path: NodePath<t.Class>) {\n      for (const prop of props) {\n        // Delete leading comments so that they don't get attached as\n        // trailing comments of the previous sibling.\n        // When transforming props, we explicitly attach their leading\n        // comments to the transformed node with `inheritPropComments`\n        // above.\n        prop.node.leadingComments = null;\n        prop.remove();\n      }\n\n      if (injectSuperRef) {\n        path.scope.push({ id: t.cloneNode(injectSuperRef) });\n        path.set(\n          \"superClass\",\n          t.assignmentExpression(\"=\", injectSuperRef, path.node.superClass),\n        );\n      }\n\n      if (classRefFlags !== ClassRefFlag.None) {\n        if (path.isClassExpression()) {\n          path.scope.push({ id: ref });\n          path.replaceWith(\n            t.assignmentExpression(\"=\", t.cloneNode(ref), path.node),\n          );\n        } else {\n          if (innerBindingRef == null) {\n            // export anonymous class declaration\n            path.node.id = ref;\n          }\n          if (classBindingNode != null) {\n            path.scope.push({ id: classRefForInnerBinding });\n          }\n        }\n      }\n\n      return path;\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,kCAAA,GAAAH,OAAA;AAKA,IAAAI,6BAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AACA,IAAAM,wCAAA,GAAAN,OAAA;AAEA,IAAAO,EAAA,GAAAP,OAAA;AAkBmC;EAEjC,IAAIQ,UAAU,GAAIC,IAAU,IAAK;IAAA;IAS/B,OAAOA,IAAI,CAACC,eAAe,CAAC,uBAAuB,CAAC;EACtD,CAAC;AACH;AAEO,SAASC,oBAAoBA,CAClCC,SAAiB,EACjBC,kCAA2C,EAC3CC,KAAiB,EACjBL,IAAU,EACV;EACA,MAAMM,eAAgC,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClD,IAAIC,YAA0B;EAC9B,KAAK,MAAMC,IAAI,IAAIJ,KAAK,EAAE;IACxB,IAAII,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;MACpB,MAAM;QAAEC;MAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE;MACjC,IAAIC,MAA2B,GAAGT,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;MAC3D,IAAI,CAACI,MAAM,EAAE;QACX,MAAME,QAAQ,GAAG,CAACR,IAAI,CAACS,UAAU,CAAC,CAAC;QACnC,MAAMC,QAAQ,GAAGV,IAAI,CAACG,IAAI,CAACQ,MAAM;QACjC,IAAIC,SAAS,GAAG,KAAK;QACrB,IAAIP,EAAgB;QACpB,IACE,CAACV,kCAAkC,IACFL,UAAU,CAACC,IAAI,CAAC,IACjDiB,QAAQ,IACR,CAACE,QAAQ,EACT;UAAA,IAAAG,aAAA;UACAD,SAAS,GAAG,CAAC,CAACb,YAAY;UAC1B,CAAAc,aAAA,GAAAd,YAAY,YAAAc,aAAA,GAAZd,YAAY,GAAKC,IAAI,CAACc,KAAK,CAACC,qBAAqB,CAC9C,GAAErB,SAAU,QACf,CAAC;UACDW,EAAE,GAAGN,YAAY;QACnB,CAAC,MAAM;UACLM,EAAE,GAAGL,IAAI,CAACc,KAAK,CAACC,qBAAqB,CAACb,IAAI,CAAC;QAC7C;QACAI,MAAM,GAAG;UAAED,EAAE;UAAEM,MAAM,EAAED,QAAQ;UAAEM,MAAM,EAAER,QAAQ;UAAEI;QAAU,CAAC;QAC9Df,eAAe,CAACoB,GAAG,CAACf,IAAI,EAAEI,MAAM,CAAC;MACnC;MACA,IAAIN,IAAI,CAACkB,oBAAoB,CAAC,CAAC,EAAE;QAC/B,IAAIlB,IAAI,CAACG,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;UAC5B,MAAM;YAAEC;UAAK,CAAC,GAAGpB,IAAI,CAACG,IAAI,CAACiB,IAAI;UAC/B,IAAIC,CAAS;UACb,IAIED,IAAI,CAACE,MAAM,KAAK,CAAC,IACjBC,WAAC,CAACC,iBAAiB,CAAEH,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAE,CAAC,IAClCG,WAAC,CAACE,gBAAgB,CAAEJ,CAAC,GAAGA,CAAC,CAACK,QAAS,CAAC,IACpCL,CAAC,CAACM,SAAS,CAACL,MAAM,KAAK,CAAC,IACxBC,WAAC,CAACK,gBAAgB,CAACP,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC,IAClCJ,WAAC,CAACM,YAAY,CAAER,CAAC,GAAGA,CAAC,CAACS,MAAO,CAAC,EAC9B;YACAxB,MAAM,CAACyB,KAAK,GAAGR,WAAC,CAACS,SAAS,CAACX,CAAC,CAAC;YAC7Bf,MAAM,CAAC2B,cAAc,GAAG,IAAI;UAC9B,CAAC,MAAM;YACL3B,MAAM,CAACyB,KAAK,GAAG/B,IAAI,CAACc,KAAK,CAACC,qBAAqB,CAAE,OAAMb,IAAK,EAAC,CAAC;UAChE;QACF,CAAC,MAAM,IAAIF,IAAI,CAACG,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;UACnC,MAAM;YAAEe;UAAO,CAAC,GAAGlC,IAAI,CAACG,IAAI;UAC5B,MAAM;YAAEiB;UAAK,CAAC,GAAGpB,IAAI,CAACG,IAAI,CAACiB,IAAI;UAC/B,IAAIC,CAAS;UACb,IAIED,IAAI,CAACE,MAAM,KAAK,CAAC,IACjBC,WAAC,CAACY,qBAAqB,CAAEd,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAE,CAAC,IACtCG,WAAC,CAACE,gBAAgB,CAAEJ,CAAC,GAAGA,CAAC,CAACe,UAAW,CAAC,IACtCf,CAAC,CAACM,SAAS,CAACL,MAAM,KAAK,CAAC,IACxBC,WAAC,CAACK,gBAAgB,CAACP,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC,IAClCJ,WAAC,CAACM,YAAY,CAACR,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,EAAE;YAC7BzB,IAAI,EAAGgC,MAAM,CAAC,CAAC,CAAC,CAAkBhC;UACpC,CAAC,CAAC,IACFqB,WAAC,CAACM,YAAY,CAAER,CAAC,GAAGA,CAAC,CAACS,MAAO,CAAC,EAC9B;YACAxB,MAAM,CAAC+B,KAAK,GAAGd,WAAC,CAACS,SAAS,CAACX,CAAC,CAAC;YAC7Bf,MAAM,CAACgC,cAAc,GAAG,IAAI;UAC9B,CAAC,MAAM;YACLhC,MAAM,CAAC+B,KAAK,GAAGrC,IAAI,CAACc,KAAK,CAACC,qBAAqB,CAAE,OAAMb,IAAK,EAAC,CAAC;UAChE;QACF,CAAC,MAAM,IAAIF,IAAI,CAACG,IAAI,CAACgB,IAAI,KAAK,QAAQ,EAAE;UACtCb,MAAM,CAACiC,QAAQ,GAAGvC,IAAI,CAACc,KAAK,CAACC,qBAAqB,CAACb,IAAI,CAAC;QAC1D;MACF;MACAL,eAAe,CAACoB,GAAG,CAACf,IAAI,EAAEI,MAAM,CAAC;IACnC;EACF;EACA,OAAOT,eAAe;AACxB;AAEO,SAAS2C,sBAAsBA,CACpC3C,eAAgC,EAChC4C,yBAAkC,EAClCC,sBAA+B,EAC/BC,KAAW,EACX;EACA,MAAMC,SAAwB,GAAG,EAAE;EAEnC,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAS,CAAC;EAErC,KAAK,MAAM,CAAC5C,IAAI,EAAE6C,KAAK,CAAC,IAAIlD,eAAe,EAAE;IAW3C,MAAM;MAAEc,MAAM,EAAED,QAAQ;MAAEM,MAAM,EAAER,QAAQ;MAAEuB,KAAK;MAAEM;IAAM,CAAC,GAAGU,KAAK;IAClE,MAAMC,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IACvC,MAAMhC,EAAE,GAAGkB,WAAC,CAACS,SAAS,CAACe,KAAK,CAAC1C,EAAE,CAAC;IAEhC,IAAI4C,IAAkB;IAEtB,IAAIR,yBAAyB,EAAE;MAC7BQ,IAAI,GAAG1B,WAAC,CAAC2B,cAAc,CAACP,KAAK,CAACQ,SAAS,CAAC,2BAA2B,CAAC,EAAE,CACpE5B,WAAC,CAAC6B,aAAa,CAAClD,IAAI,CAAC,CACtB,CAAC;IACJ,CAAC,MAAM,IAAIwC,sBAAsB,EAAE;MACjCO,IAAI,GAAG1B,WAAC,CAAC2B,cAAc,CAAC3B,WAAC,CAAC8B,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC9B,WAAC,CAAC6B,aAAa,CAAClD,IAAI,CAAC,CAAC,CAAC;IAC1E,CAAC,MAAM,IAAI,CAACQ,QAAQ,EAAE;MACpB,IAAImC,WAAW,CAACS,GAAG,CAACjD,EAAE,CAACH,IAAI,CAAC,EAAE;MAC9B2C,WAAW,CAACU,GAAG,CAAClD,EAAE,CAACH,IAAI,CAAC;MAExB+C,IAAI,GAAG1B,WAAC,CAACiC,aAAa,CACpBjC,WAAC,CAAC8B,UAAU,CACV7C,QAAQ,KAEJ,CAACwC,gBAAgB,IACjB1D,UAAU,CAACqD,KAAK,CAAC,IACjB,SAAS,GACT,SACN,CAAC,EACD,EACF,CAAC;IACH;IAEA,IAAIM,IAAI,EAAE;MACR,IAAI,CAACP,sBAAsB,EAAE;QAC3B,IAAAe,6BAAc,EAACR,IAAI,CAAC;MACtB;MACAL,SAAS,CAACc,IAAI,CAACC,cAAQ,CAACC,SAAS,CAACC,GAAI,OAAMxD,EAAG,MAAK4C,IAAK,EAAC,CAAC;IAC7D;EACF;EAEA,OAAOL,SAAS;AAClB;AAUO,SAASkB,yBAAyBA,CACvCC,OAAgD,EAChD;EAGA,MAAMC,aAAa,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAAC,MAAA,CAAAC,MAAA,KACvCN,OAAO,GACZO,iCAAkB,CACnB,CAAC;EAGF,MAAMC,kBAEL,GAAAH,MAAA,CAAAC,MAAA,KACIN,OAAO;IAEVS,KAAKA,CAACC,IAAI,EAAE;MACV,MAAM;QAAE5E;MAAgB,CAAC,GAAG,IAAI;MAChC,MAAMuB,IAAI,GAAGqD,IAAI,CAAClE,GAAG,CAAC,WAAW,CAAC;MAElC,MAAMmE,mBAAmB,GAAG,IAAI5E,GAAG,CAACD,eAAe,CAAC;MACpD,MAAM8E,UAAU,GAAG,EAAE;MACrB,KAAK,MAAM3E,IAAI,IAAIoB,IAAI,EAAE;QACvB,IAAI,CAACpB,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;QACvB,MAAM;UAAEC;QAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE;QACjCqE,mBAAmB,CAACE,MAAM,CAAC1E,IAAI,CAAC;QAChCyE,UAAU,CAACjB,IAAI,CAACxD,IAAI,CAAC;MACvB;MAIA,IAAI,CAACyE,UAAU,CAACrD,MAAM,EAAE;QACtB;MACF;MAKAmD,IAAI,CAAClE,GAAG,CAAC,MAAM,CAAC,CAAC0D,QAAQ,CAACD,aAAa,EAAAI,MAAA,CAAAC,MAAA,KAClC,IAAI;QACPM;MAAU,EACX,CAAC;MACFF,IAAI,CAACR,QAAQ,CAACM,kBAAkB,EAAAH,MAAA,CAAAC,MAAA,KAC3B,IAAI;QACPxE,eAAe,EAAE6E;MAAmB,EACrC,CAAC;MAIFD,IAAI,CAACI,OAAO,CAAC,MAAM,CAAC;IACtB;EAAC,EACF;EAED,OAAON,kBAAkB;AAC3B;AAWA,MAAMA,kBAAkB,GAAGT,yBAAyB,CAGlD;EACAgB,WAAWA,CAACL,IAAI,EAAE;IAAEM;EAAc,CAAC,EAAE;IACnC,MAAM;MAAElF,eAAe;MAAE8E;IAAW,CAAC,GAAG,IAAI;IAC5C,MAAM;MAAExE,IAAI;MAAE6E;IAAW,CAAC,GAAGP,IAAI;IAEjC,IACE,CAACO,UAAU,CAACC,kBAAkB,CAAC;MAAEC,QAAQ,EAAE/E;IAAK,CAAC,CAAC,IAClD,CAAC6E,UAAU,CAACG,0BAA0B,CAAC;MAAED,QAAQ,EAAE/E;IAAK,CAAC,CAAC,EAC1D;MACA;IACF;IACA,MAAM;MAAED;IAAK,CAAC,GAAGC,IAAI,CAACE,EAAE;IACxB,IAAI,CAACR,eAAe,CAACyD,GAAG,CAACpD,IAAI,CAAC,EAAE;IAChC,IAAIyE,UAAU,IAAIA,UAAU,CAACS,QAAQ,CAAClF,IAAI,CAAC,EAAE;IAE7C,IAAI,CAACmF,MAAM,CAACL,UAAU,EAAED,aAAa,CAAC;EACxC;AACF,CAAC,CAAC;AAGF,SAASO,QAAQA,CACfpF,IAAY,EACZY,KAAY,EACZyE,YAAsC,EACtC;EAIA,OACE,CAAAC,MAAA,GAAA1E,KAAK,aAAL0E,MAAA,CAAOC,UAAU,CAACvF,IAAI,CAAC,IACvB,CAACY,KAAK,CAAC4E,uBAAuB,CAACxF,IAAI,EAAEqF,YAAY,CAAC,EAClD;IAAA,IAAAC,MAAA;IACA1E,KAAK,CAAC6E,MAAM,CAACzF,IAAI,CAAC;IAClBY,KAAK,GAAGA,KAAK,CAAC8E,MAAM;EACtB;AACF;AAEO,SAASC,eAAeA,CAC7BC,GAAiB,EACjBvG,IAAU,EACVwG,aAAuB,EACvB;EACA,IAAIA,aAAa,IAAI,EAACxG,IAAI,CAACC,eAAe,YAApBD,IAAI,CAACC,eAAe,CAAG,YAAY,CAAC,GAAE,OAAOsG,GAAG;EACtE,OAAOvE,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC2C,GAAG,CAAC,CAAC;AAC9D;AAEA,MAAME,gBAAgB,GAAGlC,yBAAyB,CAQhD;EACAmC,gBAAgBA,CAACxB,IAAI,EAAE;IAAElF;EAAK,CAAC,EAAE;IAC/B,MAAM;MAAE2G,QAAQ;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAG3B,IAAI,CAACtE,IAAI;IAC3C,IAAI+F,QAAQ,KAAK,IAAI,EAAE;IACvB,IAAI,CAAC3E,WAAC,CAAC8E,aAAa,CAACF,IAAI,CAAC,EAAE;IAE5B,MAAM;MAAE1D,yBAAyB;MAAE5C,eAAe;MAAE8E;IAAW,CAAC,GAAG,IAAI;IAEvE,MAAM;MAAEzE;IAAK,CAAC,GAAGiG,IAAI,CAAC9F,EAAE;IAExB,IAAI,CAACR,eAAe,CAACyD,GAAG,CAACpD,IAAI,CAAC,EAAE;IAChC,IAAIyE,UAAU,IAAIA,UAAU,CAACS,QAAQ,CAAClF,IAAI,CAAC,EAAE;IAI7CoF,QAAQ,CAAC,IAAI,CAACgB,QAAQ,CAACpG,IAAI,EAAEuE,IAAI,CAAC3D,KAAK,EAAE,IAAI,CAACyE,YAAY,CAAC;IAE3D,IAAI9C,yBAAyB,EAAE;MAC7B,MAAM;QAAEpC;MAAG,CAAC,GAAGR,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;MACxCuE,IAAI,CAAC8B,WAAW,CAAC5C,cAAQ,CAACvB,UAAU,CAACyB,GAAI;AAC/C,+CAA+CgC,eAAe,CACpDO,KAAK,EACL7G,IACF,CAAE,KAAIgC,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE;AAC9B,OAAO,CAAC;MACF;IACF;IAEA,MAAM;MAAEA,EAAE;MAAEM,MAAM,EAAED;IAAS,CAAC,GAAGb,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAE1D,IAAIQ,QAAQ,EAAE;MACZ+D,IAAI,CAAC8B,WAAW,CACd5C,cAAQ,CAACvB,UAAU,CAACyB,GAAI,GAAEgC,eAAe,CACvCO,KAAK,EACL7G,IACF,CAAE,QAAOgC,WAAC,CAACS,SAAS,CAAC,IAAI,CAACsE,QAAQ,CAAE,EACtC,CAAC;MACD;IACF;IAEA7B,IAAI,CAAC8B,WAAW,CACd5C,cAAQ,CAACvB,UAAU,CAACyB,GAAI,GAAEtC,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE,QAAOwF,eAAe,CAC9DO,KAAK,EACL7G,IACF,CAAE,GACJ,CAAC;EACH;AACF,CAAC,CAAC;AASF,SAASiH,aAAaA,CAACjH,IAAU,EAAEW,IAAY,EAAE;EAC/C,OAAOqB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,eAAe,CAAC,EAAE,CACvD5B,WAAC,CAAC6B,aAAa,CAAE,IAAGlD,IAAK,EAAC,CAAC,CAC5B,CAAC;AACJ;AAEA,SAASuG,cAAcA,CAAClH,IAAU,EAAEW,IAAY,EAAE;EAChD,IAEE,CAACX,IAAI,CAACC,eAAe,CAAC,gBAAgB,CAAC,EACvC;IACAkH,OAAO,CAACC,IAAI,CACT,gEACH,CAAC;IACD,OAAOpF,WAAC,CAACqF,kBAAkB,CAAC,CAAC;EAC/B;EACA,OAAOrF,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,gBAAgB,CAAC,EAAE,CACxD5B,WAAC,CAAC6B,aAAa,CAAE,IAAGlD,IAAK,EAAC,CAAC,CAC5B,CAAC;AACJ;AAEA,SAAS2G,6BAA6BA,CACpCC,IAAO,EACPC,iCAA0C,EAC1C;EACA,IAAIA,iCAAiC,EAAE,OAAOD,IAAI;EAClD,OAAOvF,WAAC,CAACyF,gBAAgB,CAACF,IAAI,EAAEvF,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAAC,CAAC;AACpD;AAEA,MAAM4D,sBAAuE,GAC3E;EACEC,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACrB,MAAM;MAAEtG;IAAM,CAAC,GAAGqG,MAAM;IACxB,MAAM;MAAEE;IAAO,CAAC,GAAGF,MAAM,CAAChH,IAAgC;IAE1D,MAAMmH,IAAI,GAAGxG,KAAK,CAACyG,qBAAqB,CAACF,MAAM,CAAC;IAChD,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IAEA,IAAI,CAACE,QAAQ,CAACvG,GAAG,CAACoG,MAAM,EAAEC,IAAI,EAAEF,KAAK,CAAC;EACxC,CAAC;EAEDK,QAAQA,CAACN,MAAM,EAAE;IACf,MAAM;MAAEE;IAAO,CAAC,GAAGF,MAAM,CAAChH,IAAgC;IAE1D,IAAI,IAAI,CAACqH,QAAQ,CAAClE,GAAG,CAAC+D,MAAM,CAAC,EAAE;MAC7B,OAAO9F,WAAC,CAACS,SAAS,CAAC,IAAI,CAACwF,QAAQ,CAACjH,GAAG,CAAC8G,MAAM,CAAC,CAAC;IAC/C;IAEA,OAAO9F,WAAC,CAACS,SAAS,CAACqF,MAAM,CAAC;EAC5B,CAAC;EAED9G,GAAGA,CAAC4G,MAAM,EAAE;IACV,MAAM;MACJb,QAAQ;MACRzG,eAAe;MACfN,IAAI;MACJgG,YAAY;MACZwB;IACF,CAAC,GAAG,IAAI;IACR,MAAM;MAAE7G;IAAK,CAAC,GAAIiH,MAAM,CAAChH,IAAI,CAAC+E,QAAQ,CAAmB7E,EAAE;IAC3D,MAAM;MACJA,EAAE;MACFM,MAAM,EAAED,QAAQ;MAChBM,MAAM,EAAER,QAAQ;MAChB+B,QAAQ;MACRR,KAAK;MACLM;IACF,CAAC,GAAGxC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAC7B,MAAM8C,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IAEvC,IAAI3B,QAAQ,EAAE;MAGZ4E,QAAQ,CAACgB,QAAQ,CAACpG,IAAI,EAAEiH,MAAM,CAACrG,KAAK,EAAEyE,YAAY,CAAC;MAEnD,IAAqC,CAACjG,UAAU,CAACC,IAAI,CAAC,EAAE;QAGtD,MAAMmI,UAAU,GACdlH,QAAQ,IAAI,CAACwC,gBAAgB,GACzB,6BAA6B,GAC7B,gCAAgC;QAEtC,OAAOzB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAACuE,UAAU,CAAC,EAAE,CAClD,IAAI,CAACD,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrB/E,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,CAChB,CAAC;MACJ;MAEA,MAAMoH,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACN,MAAM,CAAC;MACtC,MAAMQ,SAAS,GACbpG,WAAC,CAACM,YAAY,CAAC4F,QAAQ,CAAC,IAAIA,QAAQ,CAACvH,IAAI,KAAKoG,QAAQ,CAACpG,IAAI;MAE7D,IAAI,CAACM,QAAQ,EAAE;QACb,IAAImH,SAAS,EAAE;UACb,OAAOd,6BAA6B,CAClCtF,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf0G,iCACF,CAAC;QACH;QAEA,OAAOF,6BAA6B,CAClCtF,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrBmB,QAAQ,EACRlG,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,CAChB,CAAC,EACF0G,iCACF,CAAC;MACH;MAEA,IAAIhF,KAAK,EAAE;QACT,IAAI4F,SAAS,EAAE;UACb,OAAOpG,WAAC,CAAC2B,cAAc,CAAC3B,WAAC,CAACS,SAAS,CAACD,KAAK,CAAC,EAAE,CAAC0F,QAAQ,CAAC,CAAC;QACzD;QACA,OAAOlG,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrBmB,QAAQ,EACRlG,WAAC,CAACS,SAAS,CAACD,KAAK,CAAC,CACnB,CAAC;MACJ;MAEA,IAAIM,KAAK,EAAE;QACT,MAAMuF,GAAG,GAAGrG,WAAC,CAACqF,kBAAkB,CAAC,CAAC;QAClC,IAAIe,SAAS,EAAE,OAAOC,GAAG;QACzB,OAAOrG,WAAC,CAACsG,kBAAkB,CAAC,CAC1BtG,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrBmB,QAAQ,CACT,CAAC,EACFG,GAAG,CACJ,CAAC;MACJ;MAEA,IAAID,SAAS,EAAE,OAAOpG,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC;MACrC,OAAOkB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAC1D5B,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrBmB,QAAQ,EACRlG,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,CAChB,CAAC;IACJ;IAEA,IAAIG,QAAQ,EAAE;MACZ,IAAIwC,gBAAgB,EAAE;QACpB,IAAI,CAACjB,KAAK,EAAE;UACV,OAAOR,WAAC,CAACsG,kBAAkB,CAAC,CAC1B,IAAI,CAACJ,QAAQ,CAACN,MAAM,CAAC,EACrBV,cAAc,CAAClH,IAAI,EAAEW,IAAI,CAAC,CAC3B,CAAC;QACJ;QACA,IAAqC,CAACZ,UAAU,CAACC,IAAI,CAAC,EAAE;UACtD,OAAOgC,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACsE,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,CAChB,CAAC;QACJ;QACA,OAAOkB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf,IAAI,CAACoH,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAACD,KAAK,CAAC,CACnB,CAAC;MACJ;MACA,IAAqC,CAACzC,UAAU,CAACC,IAAI,CAAC,EAAE;QACtD,OAAOgC,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAC/D,IAAI,CAACsE,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACfkB,WAAC,CAACS,SAAS,CAACO,QAAQ,CAAC,CACtB,CAAC;MACJ;MACA,OAAOhB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAC1D5B,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf,IAAI,CAACoH,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAACO,QAAQ,CAAC,CACtB,CAAC;IACJ;IACA,IAAoCjD,UAAU,CAACC,IAAI,CAAC,EAAE;MACpD,OAAOgC,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAC/D5B,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf,IAAI,CAACoH,QAAQ,CAACN,MAAM,CAAC,CACtB,CAAC;IACJ;IAEA,OAAO5F,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACsE,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,CAChB,CAAC;EACJ,CAAC;EAEDyH,QAAQA,CAACX,MAAM,EAAE;IACf,IAAI,CAACD,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO5F,WAAC,CAAC2B,cAAc,CACrB3B,WAAC,CAACyF,gBAAgB,CAAC,IAAI,CAACzG,GAAG,CAAC4G,MAAM,CAAC,EAAE5F,WAAC,CAAC8B,UAAU,CAAC,MAAM,CAAC,CAAC,EAC1D,CAAC,IAAI,CAACoE,QAAQ,CAACN,MAAM,CAAC,CACxB,CAAC;EACH,CAAC;EAEDlG,GAAGA,CAACkG,MAAM,EAAEpE,KAAK,EAAE;IACjB,MAAM;MACJuD,QAAQ;MACRzG,eAAe;MACfN,IAAI;MACJwH;IACF,CAAC,GAAG,IAAI;IACR,MAAM;MAAE7G;IAAK,CAAC,GAAIiH,MAAM,CAAChH,IAAI,CAAC+E,QAAQ,CAAmB7E,EAAE;IAC3D,MAAM;MACJA,EAAE;MACFM,MAAM,EAAED,QAAQ;MAChBM,MAAM,EAAER,QAAQ;MAChB6B,KAAK;MACLN;IACF,CAAC,GAAGlC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAC7B,MAAM8C,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IAEvC,IAAI3B,QAAQ,EAAE;MACZ,IAAqC,CAACpB,UAAU,CAACC,IAAI,CAAC,EAAE;QACtD,MAAMmI,UAAU,GACdlH,QAAQ,IAAI,CAACwC,gBAAgB,GACzB,6BAA6B,GAC7B,gCAAgC;QAEtC,OAAOzB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAACuE,UAAU,CAAC,EAAE,CAClD,IAAI,CAACD,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrB/E,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf0C,KAAK,CACN,CAAC;MACJ;MAEA,MAAM0E,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACN,MAAM,CAAC;MACtC,MAAMQ,SAAS,GACbpG,WAAC,CAACM,YAAY,CAAC4F,QAAQ,CAAC,IAAIA,QAAQ,CAACvH,IAAI,KAAKoG,QAAQ,CAACpG,IAAI;MAE7D,IAAIM,QAAQ,IAAI,CAAC6B,KAAK,EAAE;QACtB,MAAMuF,GAAG,GAAGpB,aAAa,CAACjH,IAAI,EAAEW,IAAI,CAAC;QACrC,IAAIyH,SAAS,EAAE,OAAOpG,WAAC,CAACsG,kBAAkB,CAAC,CAAC9E,KAAK,EAAE6E,GAAG,CAAC,CAAC;QACxD,OAAOrG,WAAC,CAACsG,kBAAkB,CAAC,CAC1B9E,KAAK,EACLxB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrBmB,QAAQ,CACT,CAAC,EACFjB,aAAa,CAACjH,IAAI,EAAEW,IAAI,CAAC,CAC1B,CAAC;MACJ;MAEA,IAAImC,KAAK,EAAE;QACT,IAAIsF,SAAS,EAAE;UACb,OAAOpG,WAAC,CAAC2B,cAAc,CAAC3B,WAAC,CAACS,SAAS,CAACK,KAAK,CAAC,EAAE,CAACoF,QAAQ,EAAE1E,KAAK,CAAC,CAAC;QAChE;QACA,OAAOxB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrB/E,WAAC,CAACS,SAAS,CAACK,KAAK,CAAC,EAClBoF,QAAQ,EACR1E,KAAK,CACN,CAAC;MACJ;MACA,OAAOxB,WAAC,CAACwG,oBAAoB,CAC3B,GAAG,EACHlB,6BAA6B,CAC3BtF,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf0G,iCACF,CAAC,EACDY,SAAS,GACL5E,KAAK,GACLxB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrBmB,QAAQ,EACR1E,KAAK,CACN,CACP,CAAC;IACH;IACA,IAAIvC,QAAQ,EAAE;MACZ,IAAI6B,KAAK,EAAE;QACT,IAAqC,CAAC/C,UAAU,CAACC,IAAI,CAAC,EAAE;UACtD,OAAOgC,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACsE,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf0C,KAAK,CACN,CAAC;QACJ;QACA,OAAOxB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACfkB,WAAC,CAACS,SAAS,CAACK,KAAK,CAAC,EAClB,IAAI,CAACoF,QAAQ,CAACN,MAAM,CAAC,EACrBpE,KAAK,CACN,CAAC;MACJ;MACA,OAAOxB,WAAC,CAACsG,kBAAkB,CAAC,CAC1B,IAAI,CAACJ,QAAQ,CAACN,MAAM,CAAC,EACrBpE,KAAK,EACLyD,aAAa,CAACjH,IAAI,EAAEW,IAAI,CAAC,CAC1B,CAAC;IACJ;IAEA,IAAoCZ,UAAU,CAACC,IAAI,CAAC,EAAE;MACpD,OAAOgC,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAC/D5B,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf,IAAI,CAACoH,QAAQ,CAACN,MAAM,CAAC,EACrBpE,KAAK,CACN,CAAC;IACJ;IAEA,OAAOxB,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACsE,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACf0C,KAAK,CACN,CAAC;EACJ,CAAC;EAEDiF,cAAcA,CAACb,MAAM,EAAE;IACrB,MAAM;MACJb,QAAQ;MACRzG,eAAe;MACfN,IAAI;MACJwH;IACF,CAAC,GAAG,IAAI;IACR,MAAM;MAAE7G;IAAK,CAAC,GAAIiH,MAAM,CAAChH,IAAI,CAAC+E,QAAQ,CAAmB7E,EAAE;IAC3D,MAAM;MACJA,EAAE;MACFM,MAAM,EAAED,QAAQ;MAChBM,MAAM,EAAER,QAAQ;MAChB6B;IACF,CAAC,GAAGxC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAE7B,IAAqC,CAACZ,UAAU,CAACC,IAAI,CAAC,EAAE;MACtD,IAAImB,QAAQ,EAAE;QACZ,IAAI;UAGF,IAAIuH,MAAM,GAAG1I,IAAI,CAAC4D,SAAS,CACzB,uCACF,CAAC;QACH,CAAC,CAAC,OAAA+E,OAAA,EAAM;UACN,MAAM,IAAIC,KAAK,CACb,0EAA0E,GACxE,qDACJ,CAAC;QACH;QACA,OAAO5G,WAAC,CAACyF,gBAAgB,CACvBzF,WAAC,CAAC2B,cAAc,CAAC+E,MAAM,EAAE,CACvB,IAAI,CAACR,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAACsE,QAAQ,CAAC,EACrB/E,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,CAChB,CAAC,EACFkB,WAAC,CAAC8B,UAAU,CAAC,OAAO,CACtB,CAAC;MACH;MAEA,OAAO9B,WAAC,CAACyF,gBAAgB,CACvBzF,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,iCAAiC,CAAC,EAAE,CAClE,IAAI,CAACsE,QAAQ,CAACN,MAAM,CAAC,EACrB5F,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,CAChB,CAAC,EACFkB,WAAC,CAAC8B,UAAU,CAAC,OAAO,CACtB,CAAC;IACH;IAEA,IAAI7C,QAAQ,IAAI,CAAC6B,KAAK,EAAE;MACtB,OAAOd,WAAC,CAACyF,gBAAgB,CACvBzF,WAAC,CAACsG,kBAAkB,CAAC,CAEnBV,MAAM,CAAChH,IAAI,CAACkH,MAAM,EAClBb,aAAa,CAACjH,IAAI,EAAEW,IAAI,CAAC,CAC1B,CAAC,EACFqB,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAClB,CAAC;IACH;IAEA,IAAI3C,QAAQ,IAAI,CAACF,QAAQ,EAAE;MACzB,MAAM4H,OAAO,GAAG,IAAI,CAAC7H,GAAG,CAAC4G,MAAM,CAAC;MAChC,IACE,CAACJ,iCAAiC,IAClC,CAACxF,WAAC,CAACE,gBAAgB,CAAC2G,OAAO,CAAC,EAC5B;QACA,OAAOA,OAAO;MAChB;MACA,MAAMC,GAAG,GAAGD,OAAO,CAACzG,SAAS,CAAC2G,GAAG,CAAC,CAAC;MACnCF,OAAO,CAACzG,SAAS,CAAC+B,IAAI,CAACC,cAAQ,CAACvB,UAAU,CAACyB,GAAI,UAASwE,GAAI,MAAK,CAAC;MAClE,OAAO9G,WAAC,CAACyF,gBAAgB,CACvBzF,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,UAAU,CAAC,EAAE,CAACiF,OAAO,CAAC,CAAC,EACvD7G,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAClB,CAAC;IACH;IAEA,MAAMkF,OAAO,GAAG,IAAI,CAACtH,GAAG,CAACkG,MAAM,EAAE5F,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAAC,CAAC;IACnD,IACE,CAAC9B,WAAC,CAACE,gBAAgB,CAAC8G,OAAO,CAAC,IAC5B,CAAChH,WAAC,CAACM,YAAY,CAAC0G,OAAO,CAAC5G,SAAS,CAAC4G,OAAO,CAAC5G,SAAS,CAACL,MAAM,GAAG,CAAC,CAAC,EAAE;MAC/DpB,IAAI,EAAE;IACR,CAAC,CAAC,EACF;MACA,MAAMiH,MAAM,CAACqB,mBAAmB,CAC9B,uEAAuE,GACrE,4DACJ,CAAC;IACH;IAIA,IAAIC,IAAoB;IACxB,IACElH,WAAC,CAAC0D,kBAAkB,CAACsD,OAAO,CAACzG,MAAM,EAAE;MAAE4G,QAAQ,EAAE;IAAM,CAAC,CAAC,IACzDnH,WAAC,CAACM,YAAY,CAAC0G,OAAO,CAACzG,MAAM,CAACoD,QAAQ,CAAC,IACvCqD,OAAO,CAACzG,MAAM,CAACoD,QAAQ,CAAChF,IAAI,KAAK,MAAM,EACvC;MACAuI,IAAI,GAAG,CAELF,OAAO,CAACzG,MAAM,CAACuF,MAAM,EACrB9F,WAAC,CAACoH,eAAe,CAEdJ,OAAO,CAAC5G,SAAS,CAAoBiH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACnD,CAAC,EACDL,OAAO,CAAC5G,SAAS,CAAC,CAAC,CAAC,CACrB;IACH,CAAC,MAAM;MACL8G,IAAI,GAAG,CACLF,OAAO,CAACzG,MAAM,EACdP,WAAC,CAACoH,eAAe,CAEdJ,OAAO,CAAC5G,SAAS,CAAoBiH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACnD,CAAC,CACF;IACH;IAEA,OAAOrH,WAAC,CAACyF,gBAAgB,CACvBzF,WAAC,CAAC2B,cAAc,CAAC3D,IAAI,CAAC4D,SAAS,CAAC,UAAU,CAAC,EAAEsF,IAAI,CAAC,EAClDlH,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAClB,CAAC;EACH,CAAC;EAEDwF,IAAIA,CAAC1B,MAAM,EAAEsB,IAAwC,EAAE;IAErD,IAAI,CAACvB,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO,IAAA2B,qCAAY,EAAC,IAAI,CAACvI,GAAG,CAAC4G,MAAM,CAAC,EAAE,IAAI,CAACM,QAAQ,CAACN,MAAM,CAAC,EAAEsB,IAAI,EAAE,KAAK,CAAC;EAC3E,CAAC;EAEDM,YAAYA,CAAC5B,MAAM,EAAEsB,IAAwC,EAAE;IAC7D,IAAI,CAACvB,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO,IAAA2B,qCAAY,EAAC,IAAI,CAACvI,GAAG,CAAC4G,MAAM,CAAC,EAAE,IAAI,CAACM,QAAQ,CAACN,MAAM,CAAC,EAAEsB,IAAI,EAAE,IAAI,CAAC;EAC1E,CAAC;EAED7D,MAAMA,CAAA,EAAG;IACP,MAAM,IAAIuD,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAEH,MAAMa,uBAAkD,GAAG;EACzDzI,GAAGA,CAAC4G,MAAM,EAAE;IACV,MAAM;MAAEtH,eAAe;MAAEN;IAAK,CAAC,GAAG,IAAI;IACtC,MAAM;MAAE8H;IAAO,CAAC,GAAGF,MAAM,CAAChH,IAAI;IAC9B,MAAM;MAAED;IAAK,CAAC,GAAIiH,MAAM,CAAChH,IAAI,CAAC+E,QAAQ,CAAmB7E,EAAE;IAE3D,OAAOsD,cAAQ,CAACvB,UAAW,uBAAsB,CAAC;MAChD6G,IAAI,EAAE1J,IAAI,CAAC4D,SAAS,CAAC,4BAA4B,CAAC;MAClD+F,GAAG,EAAE3H,WAAC,CAACS,SAAS,CAACqF,MAAM,CAAC;MACxB8B,IAAI,EAAE5H,WAAC,CAACS,SAAS,CAACnC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC,CAACG,EAAE;IAChD,CAAC,CAAC;EACJ,CAAC;EAEDY,GAAGA,CAAA,EAAG;IAEJ,MAAM,IAAIkH,KAAK,CAAC,yDAAyD,CAAC;EAC5E,CAAC;EAEDL,QAAQA,CAACX,MAAM,EAAE;IACf,OAAO5F,WAAC,CAAC2B,cAAc,CACrB3B,WAAC,CAACyF,gBAAgB,CAAC,IAAI,CAACzG,GAAG,CAAC4G,MAAM,CAAC,EAAE5F,WAAC,CAAC8B,UAAU,CAAC,MAAM,CAAC,CAAC,EAE1D,CAAC9B,WAAC,CAACS,SAAS,CAACmF,MAAM,CAAChH,IAAI,CAACkH,MAAsB,CAAC,CAClD,CAAC;EACH,CAAC;EAED+B,SAASA,CAACjC,MAAM,EAAE;IAChB,OAAO,IAAI,CAAC5G,GAAG,CAAC4G,MAAM,CAAC;EACzB,CAAC;EAEDa,cAAcA,CAACb,MAAM,EAAE;IACrB,OAAO,IAAI,CAAC5G,GAAG,CAAC4G,MAAM,CAAC;EACzB,CAAC;EAED0B,IAAIA,CAAC1B,MAAM,EAAEsB,IAAI,EAAE;IACjB,OAAOlH,WAAC,CAAC2B,cAAc,CAAC,IAAI,CAAC3C,GAAG,CAAC4G,MAAM,CAAC,EAAEsB,IAAI,CAAC;EACjD,CAAC;EAEDM,YAAYA,CAAC5B,MAAM,EAAEsB,IAAI,EAAE;IACzB,OAAOlH,WAAC,CAAC8H,sBAAsB,CAAC,IAAI,CAAC9I,GAAG,CAAC4G,MAAM,CAAC,EAAEsB,IAAI,EAAE,IAAI,CAAC;EAC/D,CAAC;EAED7D,MAAMA,CAAA,EAAG;IACP,MAAM,IAAIuD,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAEM,SAASmB,0BAA0BA,CACxCjB,GAAiB,EACjB5D,IAAuB,EACvB5E,eAAgC,EAChC;EACE4C,yBAAyB;EACzBsE,iCAAiC;EACjChC,aAAa;EACbQ;AAMF,CAAC,EACD5C,KAAW,EACX;EACA,IAAI,CAAC9C,eAAe,CAAC0J,IAAI,EAAE;EAE3B,MAAMnI,IAAI,GAAGqD,IAAI,CAAClE,GAAG,CAAC,MAAM,CAAC;EAC7B,MAAMiJ,OAAO,GAAG/G,yBAAyB,GACrCuG,uBAAuB,GACvB/B,sBAAsB;EAE1B,IAAAwC,0CAA2B,EAAmBrI,IAAI,EAAEmD,kBAAkB,EAAAH,MAAA,CAAAC,MAAA;IACpExE,eAAe;IACfyG,QAAQ,EAAE+B,GAAG;IACb9I,IAAI,EAAEoD;EAAK,GACR6G,OAAO;IACVzE,aAAa;IACbgC,iCAAiC;IACjCxB;EAAY,EACb,CAAC;EACFnE,IAAI,CAAC6C,QAAQ,CAAC+B,gBAAgB,EAAE;IAC9BnG,eAAe;IACfyG,QAAQ,EAAE+B,GAAG;IACb9I,IAAI,EAAEoD,KAAK;IACXF,yBAAyB;IACzB8C;EACF,CAAC,CAAC;AACJ;AAEA,SAASmE,0BAA0BA,CACjCrB,GAAiB,EACjBrI,IAAsC,EACtCH,eAAgC,EAChC;EACA,MAAM;IAAEQ;EAAG,CAAC,GAAGR,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EACzD,MAAM6C,KAAK,GAAG/C,IAAI,CAACG,IAAI,CAAC4C,KAAK,IAAI/C,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAC;EAEhE,OAAO+C,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC3B,8BAA8BwE,GAAI,KAAI9G,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE;AACtD;AACA;AACA;AACA,iBAAiB0C,KAAM;AACvB;AACA,KAAK,EACD/C,IACF,CAAC;AACH;AAEA,SAAS4J,iCAAiCA,CACxCvB,GAAiB,EACjBrI,IAAsC,EACtCH,eAAgC,EAChC8C,KAAW,EACX;EACA,MAAM;IAAEtC;EAAG,CAAC,GAAGR,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EACzD,MAAM6C,KAAK,GAAG/C,IAAI,CAACG,IAAI,CAAC4C,KAAK,IAAI/C,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAC;EAE7B;IACjC,IAAI,CAACjE,KAAK,CAACnD,eAAe,CAAC,2BAA2B,CAAC,EAAE;MACvD,OAAOmK,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEtC,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE,QAAOgI,GAAI;AAC5D;AACA;AACA;AACA,mBAAmBtF,KAAM;AACzB,WAAW,EACH/C,IACF,CAAC;IACH;EACF;EAEA,MAAMiI,MAAM,GAAGtF,KAAK,CAACQ,SAAS,CAAC,2BAA2B,CAAC;EAC3D,OAAOwG,mBAAmB,CACxBpI,WAAC,CAACsI,mBAAmB,CACnBtI,WAAC,CAAC2B,cAAc,CAAC+E,MAAM,EAAE,CACvB1G,WAAC,CAACuI,cAAc,CAAC,CAAC,EAClBvI,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAC,EACiBf,UAAU,CAACqD,KAAK,CAAC,GAC7CI,KAAK,GACLY,cAAQ,CAACvB,UAAU,CAACyB,GAAI,4BAA2Bd,KAAM,IAAG,CACjE,CACH,CAAC,EACD/C,IACF,CAAC;AACH;AAEA,SAAS+J,+BAA+BA,CACtC/J,IAAsC,EACtCH,eAAgC,EAChCkH,iCAA0C,EAC1C;EACA,MAAMiD,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAE9D,MAAM6C,KAAK,GAAGgE,iCAAiC,GAC3C/G,IAAI,CAACG,IAAI,CAAC4C,KAAK,GACfY,cAAQ,CAACvB,UAAU,CAACyB,GAAI;AAC9B,aAAa7D,IAAI,CAACG,IAAI,CAAC4C,KAAK,IAAIxB,WAAC,CAACqF,kBAAkB,CAAC,CAAE;AACvD,QAAQ;EAEN,OAAO+C,mBAAmB,CACxBpI,WAAC,CAAC0I,mBAAmB,CAAC,KAAK,EAAE,CAC3B1I,WAAC,CAAC2I,kBAAkB,CAAC3I,WAAC,CAACS,SAAS,CAACgI,WAAW,CAAC3J,EAAE,CAAC,EAAE0C,KAAK,CAAC,CACzD,CAAC,EACF/C,IACF,CAAC;AACH;AAEmC;EAEjC,IAAImK,kCAAkC,GAAG,SAAAA,CACvCnK,IAAsC,EACtCH,eAAgC,EAChC;IACA,MAAMmK,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;IAC9D,MAAM;MAAEG,EAAE;MAAE0B,KAAK;MAAEM,KAAK;MAAEzB;IAAU,CAAC,GAAGoJ,WAAW;IACnD,MAAMhH,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IAEvC,IAAI,CAACrC,IAAI,CAACS,UAAU,CAAC,CAAC,KAAKG,SAAS,IAAI,CAACoC,gBAAgB,CAAC,EAAE;IAE5D,IAAIA,gBAAgB,EAAE;MACpBnD,eAAe,CAACoB,GAAG,CAACjB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAkE,MAAA,CAAAC,MAAA,KACpC2F,WAAW;QACdpJ,SAAS,EAAE;MAAI,EAChB,CAAC;MAEF,OAAO+I,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B,gBAAgBtC,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE;AAChC;AACA;AACA;AACA,mBAAmB0B,KAAK,GAAGA,KAAK,CAAC7B,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACxE,mBAAmBvE,KAAK,GAAGA,KAAK,CAACnC,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACxE;AACA,SAAS,EACD5G,IACF,CAAC;IACH;IAEA,MAAM+C,KAAK,GAAG/C,IAAI,CAACG,IAAI,CAAC4C,KAAK,IAAI/C,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAC;IAChE,OAAO+C,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,cAActC,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE;AAC9B;AACA;AACA;AACA,mBAAmB0C,KAAM;AACzB;AACA,OAAO,EACD/C,IACF,CAAC;EACH,CAAC;AACH;AAEA,SAASoK,2BAA2BA,CAClC/B,GAAiB,EACjBrI,IAAoC,EACpCH,eAAgC,EAChC;EACA,MAAMmK,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEqC,QAAQ;IAAElC,EAAE;IAAE0B,KAAK;IAAEM,KAAK;IAAEzB;EAAU,CAAC,GAAGoJ,WAAW;EAC7D,IAAIpJ,SAAS,EAAE;EAEf,IAAI2B,QAAQ,EAAE;IACZ,OAAOoH,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,gCAAgCwE,GAAI,KAAIhI,EAAG;AAC3C;AACA;AACA;AACA,mBAAmBkC,QAAQ,CAACrC,IAAK;AACjC;AACA,OAAO,EACDF,IACF,CAAC;EACH;EACA,MAAMgD,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;EACvC,IAAIW,gBAAgB,EAAE;IACpBnD,eAAe,CAACoB,GAAG,CAACjB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAkE,MAAA,CAAAC,MAAA,KACpC2F,WAAW;MACdpJ,SAAS,EAAE;IAAI,EAChB,CAAC;IAEF,OAAO+I,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,gCAAgCwE,GAAI,KAAIhI,EAAG;AAC3C;AACA;AACA;AACA,iBAAiB0B,KAAK,GAAGA,KAAK,CAAC7B,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACtE,iBAAiBvE,KAAK,GAAGA,KAAK,CAACnC,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACtE;AACA,OAAO,EACD5G,IACF,CAAC;EACH;AACF;AAEA,SAASqK,kCAAkCA,CACzChC,GAAiB,EACjBrI,IAAoC,EACpCH,eAAgC,EAChC8C,KAAW,EACX;EACA,MAAMqH,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAE9D,IAAI8J,WAAW,CAACpJ,SAAS,EAAE;EAE3B,IAAqC,CAACtB,UAAU,CAACqD,KAAK,CAAC,EAAE;IACvD,MAAMK,gBAAgB,GAAGgH,WAAW,CAACjI,KAAK,IAAIiI,WAAW,CAAC3H,KAAK;IAC/D,IAAIW,gBAAgB,EAAE;MACpB,OAAOsH,kCAAkC,CACvCjC,GAAG,EACHrI,IAAI,EACJH,eAAe,EACf8C,KACF,CAAC;IACH;EACF;EAEA,OAAO4H,wCAAwC,CAC7ClC,GAAG,EACHrI,IAAI,EACJH,eAAe,EACf8C,KACF,CAAC;AACH;AAEA,SAAS2H,kCAAkCA,CACzCjC,GAAiB,EACjBrI,IAAoC,EACpCH,eAAgC,EAChC8C,KAAW,EACX;EACA,MAAMqH,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG,EAAE;IAAE0B,KAAK;IAAEM;EAAM,CAAC,GAAG2H,WAAW;EAExCnK,eAAe,CAACoB,GAAG,CAACjB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAkE,MAAA,CAAAC,MAAA,KACpC2F,WAAW;IACdpJ,SAAS,EAAE;EAAI,EAChB,CAAC;EAEiC;IACjC,IAAI,CAAC+B,KAAK,CAACnD,eAAe,CAAC,2BAA2B,CAAC,EAAE;MACvD,OAAOmK,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B,YAAYxD,EAAG,QAAOgI,GAAI;AAC1B,mBAAmBtG,KAAK,GAAGA,KAAK,CAAC7B,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACxE,mBAAmBvE,KAAK,GAAGA,KAAK,CAACnC,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACxE;AACA,SAAS,EACD5G,IACF,CAAC;IACH;EACF;EAEA,MAAMiI,MAAM,GAAGtF,KAAK,CAACQ,SAAS,CAAC,2BAA2B,CAAC;EAC3D,OAAOwG,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEoE,MAAO;AACpC,QAAQ1G,WAAC,CAACuI,cAAc,CAAC,CAAE;AAC3B,QAAQvI,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE;AACxB;AACA,eAAe0B,KAAK,GAAGA,KAAK,CAAC7B,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACpE,eAAevE,KAAK,GAAGA,KAAK,CAACnC,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACpE;AACA,MAAM,EACF5G,IACF,CAAC;AACH;AAEA,SAASuK,wCAAwCA,CAC/ClC,GAAiB,EACjBrI,IAAoC,EACpCH,eAAgC,EAChC8C,KAAW,EACX;EACA,MAAMqH,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG;EAAG,CAAC,GAAG2J,WAAW;EAES;IACjC,IAAI,CAACrH,KAAK,CAACnD,eAAe,CAAC,4BAA4B,CAAC,EAAE;MACxD,OAAOmK,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAExD,EAAG,QAAOgI,GAAI,GAAE,EACzCrI,IACF,CAAC;IACH;EACF;EAEA,MAAMiI,MAAM,GAAGtF,KAAK,CAACQ,SAAS,CAAC,4BAA4B,CAAC;EAC5D,OAAOwG,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEoE,MAAO;AACpC,QAAQ1G,WAAC,CAACuI,cAAc,CAAC,CAAE;AAC3B,QAAQvI,WAAC,CAACS,SAAS,CAAC3B,EAAE,CAAE;AACxB,MAAM,EACFL,IACF,CAAC;AACH;AAEA,SAASwK,yBAAyBA,CAChCnC,GAAiB,EACjBrI,IAA+B,EAC/B;EACA,MAAM;IAAEI,GAAG;IAAEsI;EAAS,CAAC,GAAG1I,IAAI,CAACG,IAAI;EACnC,MAAM4C,KAAK,GAAG/C,IAAI,CAACG,IAAI,CAAC4C,KAAK,IAAI/C,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAC;EAEhE,OAAO+C,mBAAmB,CACxBpI,WAAC,CAACsI,mBAAmB,CACnBtI,WAAC,CAACwG,oBAAoB,CACpB,GAAG,EACHxG,WAAC,CAACyF,gBAAgB,CAACqB,GAAG,EAAEjI,GAAG,EAAEsI,QAAQ,IAAInH,WAAC,CAACkJ,SAAS,CAACrK,GAAG,CAAC,CAAC,EAC1D2C,KACF,CACF,CAAC,EACD/C,IACF,CAAC;AACH;AAEA,SAAS0K,wBAAwBA,CAC/BrC,GAAiB,EACjBrI,IAA+B,EAC/B2C,KAAW,EACX;EACA,MAAM;IAAEvC,GAAG;IAAEsI;EAAS,CAAC,GAAG1I,IAAI,CAACG,IAAI;EACnC,MAAM4C,KAAK,GAAG/C,IAAI,CAACG,IAAI,CAAC4C,KAAK,IAAI/C,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAC;EAEhE,OAAO+C,mBAAmB,CACxBpI,WAAC,CAACsI,mBAAmB,CACnBtI,WAAC,CAAC2B,cAAc,CAACP,KAAK,CAACQ,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAClDkF,GAAG,EACHK,QAAQ,IAAInH,WAAC,CAACkJ,SAAS,CAACrK,GAAG,CAAC,GACxBA,GAAG,GACHmB,WAAC,CAAC6B,aAAa,CAAEhD,GAAG,CAAkBF,IAAI,CAAC,EAC/C6C,KAAK,CACN,CACH,CAAC,EACD/C,IACF,CAAC;AACH;AAEA,SAAS2K,iCAAiCA,CACxCtC,GAAiB,EACjBrI,IAAoC,EACpC2C,KAAW,EACX9C,eAAgC,EAChC;EACA,MAAMmK,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG,EAAE;IAAEkC,QAAQ;IAAER,KAAK;IAAEM,KAAK;IAAEzB;EAAU,CAAC,GAAGoJ,WAAW;EAE7D,IAAIpJ,SAAS,EAAE;EAEf,MAAMoC,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;EACvC,IAAIW,gBAAgB,EAAE;IACpBnD,eAAe,CAACoB,GAAG,CAACjB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAkE,MAAA,CAAAC,MAAA,KACpC2F,WAAW;MACdpJ,SAAS,EAAE;IAAI,EAChB,CAAC;IAEF,OAAO+I,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,gCAAgCwE,GAAI,KAAIhI,EAAG;AAC3C;AACA;AACA;AACA,iBAAiB0B,KAAK,GAAGA,KAAK,CAAC7B,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACtE,iBAAiBvE,KAAK,GAAGA,KAAK,CAACnC,IAAI,GAAGF,IAAI,CAACc,KAAK,CAAC8F,kBAAkB,CAAC,CAAE;AACtE;AACA,OAAO,EACD5G,IACF,CAAC;EACH;EAEA,OAAO2J,mBAAmB,CACxBhG,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC3B,8BAA8BwE,GAAI,KAAIhI,EAAG;AACzC;AACA;AACA;AACA,iBAAiBkC,QAAQ,CAACrC,IAAK;AAC/B;AACA,KAAK,EACDF,IACF,CAAC;AACH;AAEA,SAAS4K,6BAA6BA,CACpCrL,IAAU,EACVS,IAAoC,EACpCH,eAAgC,EAChCF,kCAAkC,GAAG,KAAK,EAC1C;EACA,MAAMqK,WAAW,GAAGnK,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IACJG,EAAE;IACFkC,QAAQ;IACRR,KAAK;IACLM,KAAK;IACLJ,cAAc;IACdK,cAAc;IACd3B,MAAM,EAAED;EACV,CAAC,GAAGsJ,WAAW;EACf,MAAM;IAAE9H,MAAM;IAAEd,IAAI;IAAEyJ,SAAS;IAAEC;EAAM,CAAC,GAAG9K,IAAI,CAACG,IAAI;EACpD,MAAM4K,QAAQ,GAAGhJ,KAAK,IAAIG,MAAM,CAACZ,MAAM,KAAK,CAAC;EAC7C,MAAM0J,QAAQ,GAAG3I,KAAK,IAAIH,MAAM,CAACZ,MAAM,GAAG,CAAC;EAE3C,IAAKyJ,QAAQ,IAAI9I,cAAc,IAAM+I,QAAQ,IAAI1I,cAAe,EAAE;IAChEzC,eAAe,CAACoB,GAAG,CAACjB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAkE,MAAA,CAAAC,MAAA,KACpC2F,WAAW;MACdpJ,SAAS,EAAE;IAAI,EAChB,CAAC;IACF,OAAO,IAAI;EACb;EAEA,IACmCtB,UAAU,CAACC,IAAI,CAAC,KAChDwL,QAAQ,IAAIC,QAAQ,KACrB,CAACrL,kCAAkC,EACnC;IACA,MAAMmB,KAAK,GAAGd,IAAI,CAACO,GAAG,CAAC,MAAM,CAAC,CAACO,KAAK;IACpC,MAAMmK,OAAO,GAAGnK,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;IACnD,MAAM4B,KAAuB,GAAG;MAC9BuI,OAAO,EAAED,OAAO;MAChBE,aAAa,EAAE;IACjB,CAAC;IAEDnL,IAAI,CAACiE,QAAQ,CAACmH,kBAAkB,EAAEzI,KAAK,CAAC;IACxC,IAAIA,KAAK,CAACwI,aAAa,CAAC7J,MAAM,EAAE;MAC9B,MAAM+J,WAAW,GAAGvK,KAAK,CAACC,qBAAqB,CAAC,WAAW,CAAC;MAC5DD,KAAK,CAAC4C,IAAI,CAAC;QACTrD,EAAE,EAAEgL,WAAW;QACfpI,IAAI,EAAEU,cAAQ,CAACvB,UAAU,CAACyB,GAAI;MAChC,CAAC,CAAC;MACF,KAAK,MAAMY,IAAI,IAAI9B,KAAK,CAACwI,aAAa,EAAE;QACtC1G,IAAI,CAAC8B,WAAW,CAAChF,WAAC,CAACS,SAAS,CAACqJ,WAAW,CAAC,CAAC;MAC5C;IACF;IAEAnJ,MAAM,CAACoJ,OAAO,CAAC/J,WAAC,CAACS,SAAS,CAACiJ,OAAO,CAAC,CAAC;EACtC;EAEA,IAAIM,MAAM,GAAGhJ,QAAQ;EAErB,IAAIwI,QAAQ,EAAE;IACZlL,eAAe,CAACoB,GAAG,CAACjB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAkE,MAAA,CAAAC,MAAA,KACpC2F,WAAW;MACd/H,cAAc,EAAE,IAAI;MACpBrB,SAAS,EAAE;IAAI,EAChB,CAAC;IACF2K,MAAM,GAAGxJ,KAAK;EAChB,CAAC,MAAM,IAAIiJ,QAAQ,EAAE;IACnBnL,eAAe,CAACoB,GAAG,CAACjB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAkE,MAAA,CAAAC,MAAA,KACpC2F,WAAW;MACd1H,cAAc,EAAE,IAAI;MACpB1B,SAAS,EAAE;IAAI,EAChB,CAAC;IACF2K,MAAM,GAAGlJ,KAAK;EAChB,CAAC,MAAM,IAAI3B,QAAQ,IAAI,CAACf,kCAAkC,EAAE;IAC1D4L,MAAM,GAAGlL,EAAE;EACb;EAEA,OAAOsJ,mBAAmB,CACxBpI,WAAC,CAACiK,mBAAmB,CACnBjK,WAAC,CAACS,SAAS,CAACuJ,MAAM,CAAC,EAEnBrJ,MAAM,EACNd,IAAI,EACJyJ,SAAS,EACTC,KACF,CAAC,EACD9K,IACF,CAAC;AACH;AAWA,MAAMoL,kBAAkB,GAAGnH,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAmB,CACnE;EACEsH,UAAUA,CAAChH,IAAI,EAAE9B,KAAK,EAAE;IACtB,IAAIA,KAAK,CAACwI,aAAa,IAAI1G,IAAI,CAACtE,IAAI,CAACD,IAAI,KAAK,WAAW,EAAE;MACzDyC,KAAK,CAACwI,aAAa,CAACzH,IAAI,CAACe,IAAI,CAAC;IAChC;EACF,CAAC;EACDiH,eAAeA,CAACjH,IAAI,EAAE;IAEpB,MAAM;MAAEtE;IAAK,CAAC,GAAGsE,IAAI;IACrB,IAAItE,IAAI,CAAC+F,QAAQ,KAAK,QAAQ,EAAE;MAC9B,MAAMxE,QAAQ,GAAG,IAAAiK,wEAA+B,EAACxL,IAAI,CAACuB,QAAQ,CAAC;MAC/D,IAAIH,WAAC,CAACK,gBAAgB,CAACF,QAAQ,CAAC,EAAE;QAChC+C,IAAI,CAAC8B,WAAW,CAAChF,WAAC,CAACqK,cAAc,CAAC,IAAI,CAAC,CAAC;MAC1C;IACF;EACF,CAAC;EACDC,cAAcA,CAACpH,IAAI,EAAE9B,KAAK,EAAE;IAC1BA,KAAK,CAACmJ,aAAa,GAAG,IAAI;IAC1BrH,IAAI,CAAC8B,WAAW,CAAChF,WAAC,CAACS,SAAS,CAACW,KAAK,CAACuI,OAAO,CAAC,CAAC;EAC9C,CAAC;EACDa,YAAYA,CAACtH,IAAI,EAAE;IACjB,MAAM;MAAEtE,IAAI;MAAEW;IAAM,CAAC,GAAG2D,IAAI;IAG5B,IAAItE,IAAI,CAAC6L,IAAI,CAAC9L,IAAI,KAAK,KAAK,IAAIC,IAAI,CAAC+E,QAAQ,CAAChF,IAAI,KAAK,QAAQ,EAAE;MAC/DuE,IAAI,CAAC8B,WAAW,CAACzF,KAAK,CAAC8F,kBAAkB,CAAC,CAAC,CAAC;IAC9C;EACF;AACF,CAAC,EACDtC,iCAAkB,CACnB,CAAC;AAEF,MAAM2H,sBAAkE,GAAG;EACzEC,oBAAoBA,CAACzH,IAAI,EAAE9B,KAAK,EAAE;IAChC,IACE8B,IAAI,CAAC3D,KAAK,CAAC4E,uBAAuB,CAACjB,IAAI,CAACtE,IAAI,CAACD,IAAI,EAAEyC,KAAK,CAAC4C,YAAY,CAAC,EACtE;MACA5C,KAAK,CAACmJ,aAAa,GAAG,IAAI;MAC1BrH,IAAI,CAACtE,IAAI,CAACD,IAAI,GAAGyC,KAAK,CAACuI,OAAO,CAAChL,IAAI;IACrC;EACF;AACF,CAAC;AAED,SAASiM,kBAAkBA,CACzB1H,IAAc,EACd4D,GAAiB,EACjB+D,eAAoC,EACpC;EAAA,IAAAC,cAAA;EACA,MAAM1J,KAAuB,GAAG;IAC9BuI,OAAO,EAAE7C,GAAG;IACZyD,aAAa,EAAE,KAAK;IACpBvG,YAAY,EAAE6G;EAChB,CAAC;EACD,IAAI,CAAC3H,IAAI,CAACjE,QAAQ,CAAC,CAAC,EAAE;IAEpBiE,IAAI,CAACR,QAAQ,CAACmH,kBAAkB,EAAEzI,KAAK,CAAC;EAC1C;EAGA,IACEyJ,eAAe,IAAI,IAAI,KAAAC,cAAA,GACvB1J,KAAK,CAACuI,OAAO,aAAbmB,cAAA,CAAenM,IAAI,IACnByC,KAAK,CAACuI,OAAO,CAAChL,IAAI,KAAKkM,eAAe,CAAClM,IAAI,EAC3C;IACAuE,IAAI,CAACR,QAAQ,CAACgI,sBAAsB,EAAEtJ,KAAK,CAAC;EAC9C;EAEA,OAAOA,KAAK,CAACmJ,aAAa;AAC5B;AASA,SAASQ,cAAcA,CAAC;EAAElM,GAAG;EAAEsI;AAA0B,CAAC,EAAE;EAC1D,IAAItI,GAAG,CAACmM,IAAI,KAAK,YAAY,EAAE;IAC7B,OAAO,CAAC7D,QAAQ,KAAKtI,GAAG,CAACF,IAAI,KAAK,MAAM,IAAIE,GAAG,CAACF,IAAI,KAAK,QAAQ,CAAC;EACpE;EACA,IAAIE,GAAG,CAACmM,IAAI,KAAK,eAAe,EAAE;IAChC,OAAOnM,GAAG,CAAC2C,KAAK,KAAK,MAAM,IAAI3C,GAAG,CAAC2C,KAAK,KAAK,QAAQ;EACvD;EACA,OAAO,KAAK;AACd;AAaA,SAAS4G,mBAAmBA,CAAmBxJ,IAAO,EAAEH,IAAc,EAAE;EACtEuB,WAAC,CAACiL,sBAAsB,CAACrM,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAC;EACzCoB,WAAC,CAACkL,oBAAoB,CAACtM,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAC;EACvC,OAAOA,IAAI;AACb;AAyBO,SAASuM,oBAAoBA,CAClCrE,GAAwB,EACxBsE,QAAkC,EAClC/M,KAAiB,EACjBC,eAAgC,EAChCN,IAAU,EACVqN,oBAA6B,EAC7BjN,kCAA2C,EAC3CoH,iCAA0C,EAC1C8F,aAAsB,EACtBT,eAAoC,EACpC;EAAA,IAAAU,IAAA,EAAAC,KAAA;EACA,IAAIC,aAAa,IAAoB;EACrC,IAAIC,cAA4B;EAChC,MAAMC,WAA0B,GAAG,EAAE;EACrC,MAAMC,aAAsC,GAAG,EAAE;EACjD,IAAIC,2BAA2B,GAAG,KAAK;EAEvC,MAAMC,eAAwC,GAAG,EAAE;EACnD,IAAIC,gBAA8C,GAAG,IAAI;EAEzD,MAAMC,WAAW,GAAGhM,WAAC,CAACM,YAAY,CAAC8K,QAAQ,CAAC,GACxC,MAAMA,QAAQ,GACd,MAAM;IAAA,IAAAa,eAAA;IACJ,CAAAA,eAAA,GAAAP,cAAc,YAAAO,eAAA,GAAdP,cAAc,GACZrN,KAAK,CAAC,CAAC,CAAC,CAACkB,KAAK,CAAC2M,gCAAgC,CAACd,QAAQ,CAAC;IAC3D,OAAOM,cAAc;EACvB,CAAC;EAEL,MAAMS,uBAAuB,IAAAZ,IAAA,GAC3BzE,GAAG,YAAAyE,IAAA,GACHlN,KAAK,CAAC,CAAC,CAAC,CAACkB,KAAK,CAACC,qBAAqB,CAAC,CAAAqL,eAAe,oBAAfA,eAAe,CAAElM,IAAI,KAAI,OAAO,CAAC;EACxE,CAAA6M,KAAA,GAAA1E,GAAG,YAAA0E,KAAA,GAAH1E,GAAG,GAAK9G,WAAC,CAACS,SAAS,CAACoK,eAAe,CAAC;EAEpC,KAAK,MAAMpM,IAAI,IAAIJ,KAAK,EAAE;IACxBI,IAAI,CAAC2N,eAAe,CAAC,CAAC,IAAItO,EAAE,CAACuO,sBAAsB,CAAC5N,IAAI,CAAC;IAGzD,MAAMU,QAAQ,GAAG,EAACa,WAAC,CAACsM,aAAa,YAAftM,WAAC,CAACsM,aAAa,CAAG7N,IAAI,CAACG,IAAI,CAAC,KAAIH,IAAI,CAACG,IAAI,CAACQ,MAAM;IAClE,MAAMmN,UAAU,GAAG,CAACpN,QAAQ;IAC5B,MAAMT,SAAS,GAAGD,IAAI,CAACC,SAAS,CAAC,CAAC;IAClC,MAAM8N,QAAQ,GAAG,CAAC9N,SAAS;IAC3B,MAAM+N,OAAO,GAAGhO,IAAI,CAACS,UAAU,CAAC,CAAC;IACjC,MAAMD,QAAQ,GAAG,CAACwN,OAAO;IACzB,MAAMH,aAAa,GAAG7N,IAAI,CAAC6N,aAAa,oBAAlB7N,IAAI,CAAC6N,aAAa,CAAG,CAAC;IAE5C,IAAInN,QAAQ,EAAEsM,aAAa,KAA0B;IAErD,IAAItM,QAAQ,IAAKF,QAAQ,IAAIP,SAAU,IAAI4N,aAAa,EAAE;MACxD,IAAII,4BAAa,CAAC;QAChBC,UAAU,EAAElO,IAAI;QAChB6M,aAAa;QACbtN,IAAI,EAAEA,IAAI;QACV4O,aAAa,EAAE/B,eAAe;QAC9BmB,WAAW;QACXa,YAAYA,CAAA,EAAG;UACbpB,aAAa,KAAgC;UAC7C,IAAItM,QAAQ,IAAImN,aAAa,EAAE;YAC7B,OAAOH,uBAAuB;UAChC,CAAC,MAAM;YACL,OAAOnM,WAAC,CAACyF,gBAAgB,CACvB0G,uBAAuB,EACvBnM,WAAC,CAAC8B,UAAU,CAAC,WAAW,CAC1B,CAAC;UACH;QACF;MACF,CAAC,CAAC,CAACgL,OAAO,CAAC,CAAC;MAEZ,MAAMC,QAAQ,GAAGnC,kBAAkB,CACjCnM,IAAI,EACJ0N,uBAAuB,EACvBtB,eACF,CAAC;MACD,IAAIkC,QAAQ,EAAE;QACZtB,aAAa,KAAgC;MAC/C;IACF;IAEAI,2BAA2B,GAAG,KAAK;IAOnC,QAAQ,IAAI;MACV,KAAKS,aAAa;QAAE;UAClB,MAAMU,SAAS,GAAIvO,IAAI,CAACG,IAAI,CAAmBiB,IAAI;UAGnD,IAAImN,SAAS,CAACjN,MAAM,KAAK,CAAC,IAAIC,WAAC,CAACY,qBAAqB,CAACoM,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YACnErB,WAAW,CAACxJ,IAAI,CAACiG,mBAAmB,CAAC4E,SAAS,CAAC,CAAC,CAAC,EAAEvO,IAAI,CAAC,CAAC;UAC3D,CAAC,MAAM;YACLkN,WAAW,CAACxJ,IAAI,CACdnC,WAAC,CAACiN,gBAAgB,CAChB7K,cAAQ,CAACC,SAAS,CAACC,GAAI,YAAW0K,SAAU,OAAM,EAClDvO,IAAI,CAACG,IACP,CACF,CAAC;UACH;UACA;QACF;MACA,KAAKO,QAAQ,IACXT,SAAS,IACT+N,OAAO,IACPrO,kCAAkC;QAClCuN,WAAW,CAACxJ,IAAI,CACdgG,0BAA0B,CAACnI,WAAC,CAACS,SAAS,CAACqG,GAAG,CAAC,EAAErI,IAAI,EAAEH,eAAe,CACpE,CAAC;QACD;MACF,KAAKa,QAAQ,IACXT,SAAS,IACT+N,OAAO,IACP,CAACrO,kCAAkC;QACnC,IAAqC,CAACL,UAAU,CAACC,IAAI,CAAC,EAAE;UACtD2N,WAAW,CAACxJ,IAAI,CACdyG,kCAAkC,CAACnK,IAAI,EAAEH,eAAe,CAC1D,CAAC;QACH,CAAC,MAAM;UACLqN,WAAW,CAACxJ,IAAI,CACdqG,+BAA+B,CAC7B/J,IAAI,EACJH,eAAe,EACfkH,iCACF,CACF,CAAC;QACH;QACA;MACF,KAAKrG,QAAQ,IAAIqN,QAAQ,IAAIC,OAAO,IAAIpB,oBAAoB;QAO1D,IAAI,CAACN,cAAc,CAACtM,IAAI,CAACG,IAAI,CAAC,EAAE;UAE9B+M,WAAW,CAACxJ,IAAI,CAAC8G,yBAAyB,CAACjJ,WAAC,CAACS,SAAS,CAACqG,GAAG,CAAC,EAAErI,IAAI,CAAC,CAAC;UACnE;QACF;MAEF,KAAKU,QAAQ,IAAIqN,QAAQ,IAAIC,OAAO,IAAI,CAACpB,oBAAoB;QAC3DM,WAAW,CAACxJ,IAAI,CAEdgH,wBAAwB,CAACnJ,WAAC,CAACS,SAAS,CAACqG,GAAG,CAAC,EAAErI,IAAI,EAAET,IAAI,CACvD,CAAC;QACD;MACF,KAAKuO,UAAU,IACb7N,SAAS,IACT+N,OAAO,IACPrO,kCAAkC;QAClCwN,aAAa,CAACzJ,IAAI,CAChBgG,0BAA0B,CAACnI,WAAC,CAACuI,cAAc,CAAC,CAAC,EAAE9J,IAAI,EAAEH,eAAe,CACtE,CAAC;QACD;MACF,KAAKiO,UAAU,IACb7N,SAAS,IACT+N,OAAO,IACP,CAACrO,kCAAkC;QACnCwN,aAAa,CAACzJ,IAAI,CAChBkG,iCAAiC,CAC/BrI,WAAC,CAACuI,cAAc,CAAC,CAAC,EAClB9J,IAAI,EACJH,eAAe,EACfN,IACF,CACF,CAAC;QACD;MACF,KAAKuO,UAAU,IACb7N,SAAS,IACTO,QAAQ,IACRb,kCAAkC;QAClCwN,aAAa,CAAC7B,OAAO,CACnBlB,2BAA2B,CACzB7I,WAAC,CAACuI,cAAc,CAAC,CAAC,EAElB9J,IAAI,EACJH,eACF,CACF,CAAC;QACDwN,eAAe,CAAC3J,IAAI,CAClBkH,6BAA6B,CAC3BrL,IAAI,EAEJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKmO,UAAU,IACb7N,SAAS,IACTO,QAAQ,IACR,CAACb,kCAAkC;QACnCwN,aAAa,CAAC7B,OAAO,CACnBjB,kCAAkC,CAChC9I,WAAC,CAACuI,cAAc,CAAC,CAAC,EAElB9J,IAAI,EACJH,eAAe,EACfN,IACF,CACF,CAAC;QACD8N,eAAe,CAAC3J,IAAI,CAClBkH,6BAA6B,CAC3BrL,IAAI,EAEJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKe,QAAQ,IACXT,SAAS,IACTO,QAAQ,IACR,CAACb,kCAAkC;QACnC,IAAqC,CAACL,UAAU,CAACC,IAAI,CAAC,EAAE;UACtD2N,WAAW,CAAC5B,OAAO,CAEjBnB,kCAAkC,CAACnK,IAAI,EAAEH,eAAe,CAC1D,CAAC;QACH;QACAwN,eAAe,CAAC3J,IAAI,CAClBkH,6BAA6B,CAC3BrL,IAAI,EAEJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKe,QAAQ,IACXT,SAAS,IACTO,QAAQ,IACRb,kCAAkC;QAClCuN,WAAW,CAAC5B,OAAO,CACjBX,iCAAiC,CAC/BpJ,WAAC,CAACS,SAAS,CAACqG,GAAG,CAAC,EAEhBrI,IAAI,EACJT,IAAI,EACJM,eACF,CACF,CAAC;QACDwN,eAAe,CAAC3J,IAAI,CAClBkH,6BAA6B,CAC3BrL,IAAI,EAEJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKmO,UAAU,IAAIC,QAAQ,IAAIC,OAAO,IAAIpB,oBAAoB;QAE5DO,aAAa,CAACzJ,IAAI,CAAC8G,yBAAyB,CAACjJ,WAAC,CAACuI,cAAc,CAAC,CAAC,EAAE9J,IAAI,CAAC,CAAC;QACvE;MACF,KAAK8N,UAAU,IAAIC,QAAQ,IAAIC,OAAO,IAAI,CAACpB,oBAAoB;QAC7DQ,2BAA2B,GAAG,IAAI;QAClCD,aAAa,CAACzJ,IAAI,CAEhBgH,wBAAwB,CAACnJ,WAAC,CAACuI,cAAc,CAAC,CAAC,EAAE9J,IAAI,EAAET,IAAI,CACzD,CAAC;QACD;MACF;QACE,MAAM,IAAI4I,KAAK,CAAC,cAAc,CAAC;IACnC;EACF;EAEA,IAAI6E,aAAa,IAA+B,IAAIZ,eAAe,IAAI,IAAI,EAAE;IAC3EkB,gBAAgB,GAAG/L,WAAC,CAACsI,mBAAmB,CACtCtI,WAAC,CAACwG,oBAAoB,CACpB,GAAG,EACHxG,WAAC,CAACS,SAAS,CAAC0L,uBAAuB,CAAC,EACpCnM,WAAC,CAACS,SAAS,CAACoK,eAAe,CAC7B,CACF,CAAC;EACH;EAEA,OAAO;IACLc,WAAW,EAAEA,WAAW,CAACuB,MAAM,CAACC,OAAO,CAAC;IACxCvB,aAAa,EAAEA,aAAa,CAACsB,MAAM,CAACC,OAAO,CAAC;IAC5CtB,2BAA2B;IAC3BC,eAAe,EAAEA,eAAe,CAACoB,MAAM,CAACC,OAAO,CAAC;IAChDpB,gBAAgB;IAChBqB,SAASA,CAAClK,IAAuB,EAAE;MACjC,KAAK,MAAMzE,IAAI,IAAIJ,KAAK,EAAE;QAMxBI,IAAI,CAACG,IAAI,CAACyO,eAAe,GAAG,IAAI;QAChC5O,IAAI,CAAC6O,MAAM,CAAC,CAAC;MACf;MAEA,IAAI5B,cAAc,EAAE;QAClBxI,IAAI,CAAC3D,KAAK,CAAC4C,IAAI,CAAC;UAAErD,EAAE,EAAEkB,WAAC,CAACS,SAAS,CAACiL,cAAc;QAAE,CAAC,CAAC;QACpDxI,IAAI,CAACxD,GAAG,CACN,YAAY,EACZM,WAAC,CAACwG,oBAAoB,CAAC,GAAG,EAAEkF,cAAc,EAAExI,IAAI,CAACtE,IAAI,CAAC2O,UAAU,CAClE,CAAC;MACH;MAEA,IAAI9B,aAAa,MAAsB,EAAE;QACvC,IAAIvI,IAAI,CAACsK,iBAAiB,CAAC,CAAC,EAAE;UAC5BtK,IAAI,CAAC3D,KAAK,CAAC4C,IAAI,CAAC;YAAErD,EAAE,EAAEgI;UAAI,CAAC,CAAC;UAC5B5D,IAAI,CAAC8B,WAAW,CACdhF,WAAC,CAACwG,oBAAoB,CAAC,GAAG,EAAExG,WAAC,CAACS,SAAS,CAACqG,GAAG,CAAC,EAAE5D,IAAI,CAACtE,IAAI,CACzD,CAAC;QACH,CAAC,MAAM;UACL,IAAIiM,eAAe,IAAI,IAAI,EAAE;YAE3B3H,IAAI,CAACtE,IAAI,CAACE,EAAE,GAAGgI,GAAG;UACpB;UACA,IAAIiF,gBAAgB,IAAI,IAAI,EAAE;YAC5B7I,IAAI,CAAC3D,KAAK,CAAC4C,IAAI,CAAC;cAAErD,EAAE,EAAEqN;YAAwB,CAAC,CAAC;UAClD;QACF;MACF;MAEA,OAAOjJ,IAAI;IACb;EACF,CAAC;AACH"}