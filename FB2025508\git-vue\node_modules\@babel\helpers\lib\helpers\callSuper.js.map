{"version": 3, "names": ["_getPrototypeOf", "require", "_isNativeReflectConstruct", "_possibleConstructorReturn", "_callSuper", "_this", "derived", "args", "getPrototypeOf", "possibleConstructorReturn", "isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply"], "sources": ["../../src/helpers/callSuper.ts"], "sourcesContent": ["/* @minVersion 7.23.8 */\n\n// This is duplicated to packages/babel-plugin-transform-classes/src/inline-callSuper-helpers.ts\n\n// @ts-expect-error helper\nimport getPrototypeOf from \"getPrototypeOf\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.ts\";\n// @ts-expect-error helper\nimport possibleConstructorReturn from \"possibleConstructorReturn\";\n\nexport default function _callSuper(\n  _this: Function,\n  derived: Function,\n  args: any[],\n) {\n  // Super\n  derived = getPrototypeOf(derived);\n  return possibleConstructorReturn(\n    _this,\n    isNativeReflectConstruct()\n      ? // NOTE: This doesn't work if this.__proto__.constructor has been modified.\n        Reflect.construct(\n          derived,\n          args || [],\n          getPrototypeOf(_this).constructor,\n        )\n      : derived.apply(_this, args),\n  );\n}\n"], "mappings": ";;;;;;AAKA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AAEA,IAAAE,0BAAA,GAAAF,OAAA;AAEe,SAASG,UAAUA,CAChCC,KAAe,EACfC,OAAiB,EACjBC,IAAW,EACX;EAEAD,OAAO,GAAGE,eAAc,CAACF,OAAO,CAAC;EACjC,OAAOG,0BAAyB,CAC9BJ,KAAK,EACL,IAAAK,iCAAwB,EAAC,CAAC,GAEtBC,OAAO,CAACC,SAAS,CACfN,OAAO,EACPC,IAAI,IAAI,EAAE,EACVC,eAAc,CAACH,KAAK,CAAC,CAACQ,WACxB,CAAC,GACDP,OAAO,CAACQ,KAAK,CAACT,KAAK,EAAEE,IAAI,CAC/B,CAAC;AACH"}