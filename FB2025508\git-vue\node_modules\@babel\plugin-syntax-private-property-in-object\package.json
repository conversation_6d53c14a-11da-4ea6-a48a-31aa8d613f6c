{"_from": "@babel/plugin-syntax-private-property-in-object@^7.14.5", "_id": "@babel/plugin-syntax-private-property-in-object@7.14.5", "_inBundle": false, "_integrity": "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==", "_location": "/@babel/plugin-syntax-private-property-in-object", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-private-property-in-object@^7.14.5", "name": "@babel/plugin-syntax-private-property-in-object", "escapedName": "@babel%2fplugin-syntax-private-property-in-object", "scope": "@babel", "rawSpec": "^7.14.5", "saveSpec": null, "fetchSpec": "^7.14.5"}, "_requiredBy": ["/@babel/plugin-transform-private-property-in-object", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "_shasum": "0dc6671ec0ea22b6e94a1114f857970cd39de1ad", "_spec": "@babel/plugin-syntax-private-property-in-object@^7.14.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "deprecated": false, "description": "Allow parsing of '#foo in obj' brand checks", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-private-property-in-object", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-private-property-in-object", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-private-property-in-object"}, "version": "7.14.5"}