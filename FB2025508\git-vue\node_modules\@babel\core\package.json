{"_from": "@babel/core@^7.9.6", "_id": "@babel/core@7.24.0", "_inBundle": false, "_integrity": "sha512-fQfkg0Gjkza3nf0c7/w6Xf34BW4YvzNfACRLmmb7XRLa6XHdR+K9AlJlxneFfWYf6uhOzuzZVTjF/8KfndZANw==", "_location": "/@babel/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/core@^7.9.6", "name": "@babel/core", "escapedName": "@babel%2fcore", "scope": "@babel", "rawSpec": "^7.9.6", "saveSpec": null, "fetchSpec": "^7.9.6"}, "_requiredBy": ["/@vue/babel-preset-app", "/@vue/cli-plugin-babel"], "_resolved": "https://registry.npmjs.org/@babel/core/-/core-7.24.0.tgz", "_shasum": "56cbda6b185ae9d9bed369816a8f4423c5f2ff1b", "_spec": "@babel/core@^7.9.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@vue\\cli-plugin-babel", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "browser": {"./lib/config/files/index.js": "./lib/config/files/index-browser.js", "./lib/config/resolve-targets.js": "./lib/config/resolve-targets-browser.js", "./lib/transform-file.js": "./lib/transform-file-browser.js", "./src/config/files/index.ts": "./src/config/files/index-browser.ts", "./src/config/resolve-targets.ts": "./src/config/resolve-targets-browser.ts", "./src/transform-file.ts": "./src/transform-file-browser.ts"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20core%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.23.5", "@babel/generator": "^7.23.6", "@babel/helper-compilation-targets": "^7.23.6", "@babel/helper-module-transforms": "^7.23.3", "@babel/helpers": "^7.24.0", "@babel/parser": "^7.24.0", "@babel/template": "^7.24.0", "@babel/traverse": "^7.24.0", "@babel/types": "^7.24.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "deprecated": false, "description": "Babel compiler core.", "devDependencies": {"@babel/helper-transform-fixture-test-runner": "^7.24.0", "@babel/plugin-syntax-flow": "^7.23.3", "@babel/plugin-transform-flow-strip-types": "^7.23.3", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/preset-env": "^7.24.0", "@babel/preset-typescript": "^7.23.3", "@jridgewell/trace-mapping": "^0.3.17", "@types/convert-source-map": "^2.0.0", "@types/debug": "^4.1.0", "@types/gensync": "^1.0.0", "@types/resolve": "^1.3.2", "@types/semver": "^5.4.0", "rimraf": "^3.0.0", "ts-node": "^10.9.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}, "homepage": "https://babel.dev/docs/en/next/babel-core", "keywords": ["6to5", "babel", "classes", "const", "es6", "harmony", "let", "modules", "transpile", "transpiler", "var", "babel-core", "compiler"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/core", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-core"}, "type": "commonjs", "version": "7.24.0"}