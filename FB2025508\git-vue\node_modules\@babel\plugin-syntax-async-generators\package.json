{"_from": "@babel/plugin-syntax-async-generators@^7.8.4", "_id": "@babel/plugin-syntax-async-generators@7.8.4", "_inBundle": false, "_integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==", "_location": "/@babel/plugin-syntax-async-generators", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-async-generators@^7.8.4", "name": "@babel/plugin-syntax-async-generators", "escapedName": "@babel%2fplugin-syntax-async-generators", "scope": "@babel", "rawSpec": "^7.8.4", "saveSpec": null, "fetchSpec": "^7.8.4"}, "_requiredBy": ["/@babel/plugin-transform-async-generator-functions", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "_shasum": "a983fb1aeb2ec3f6ed042a210f640e90e786fe0d", "_spec": "@babel/plugin-syntax-async-generators@^7.8.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "deprecated": false, "description": "Allow parsing of async generator functions", "devDependencies": {"@babel/core": "^7.8.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-async-generators", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators"}, "version": "7.8.4"}