{"_from": "@babel/plugin-transform-unicode-sets-regex@^7.23.3", "_id": "@babel/plugin-transform-unicode-sets-regex@7.23.3", "_inBundle": false, "_integrity": "sha512-W7lliA/v9bNR83Qc3q1ip9CQMZ09CcHDbHfbLRDNuAhn1Mvkr1ZNF7hPmztMQvtTGVLJ9m8IZqWsTkXOml8dbw==", "_location": "/@babel/plugin-transform-unicode-sets-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-unicode-sets-regex@^7.23.3", "name": "@babel/plugin-transform-unicode-sets-regex", "escapedName": "@babel%2fplugin-transform-unicode-sets-regex", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.23.3.tgz", "_shasum": "4fb6f0a719c2c5859d11f6b55a050cc987f3799e", "_spec": "@babel/plugin-transform-unicode-sets-regex@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Compile regular expressions' unicodeSets (v) flag.", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-sets-regex", "keywords": ["babel-plugin", "regex", "regexp", "unicode", "sets", "properties", "property", "string", "strings", "regular expressions"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-unicode-sets-regex", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-unicode-sets-regex"}, "type": "commonjs", "version": "7.23.3"}