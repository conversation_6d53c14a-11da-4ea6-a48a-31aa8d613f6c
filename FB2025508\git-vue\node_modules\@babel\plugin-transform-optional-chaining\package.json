{"_from": "@babel/plugin-transform-optional-chaining@^7.23.4", "_id": "@babel/plugin-transform-optional-chaining@7.23.4", "_inBundle": false, "_integrity": "sha512-ZU8y5zWOfjM5vZ+asjgAPwDaBjJzgufjES89Rs4Lpq63O300R/kOz30WCLo6BxxX6QVEilwSlpClnG5cZaikTA==", "_location": "/@babel/plugin-transform-optional-chaining", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-optional-chaining@^7.23.4", "name": "@babel/plugin-transform-optional-chaining", "escapedName": "@babel%2fplugin-transform-optional-chaining", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.23.4.tgz", "_shasum": "6acf61203bdfc4de9d4e52e64490aeb3e52bd017", "_spec": "@babel/plugin-transform-optional-chaining@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}, "deprecated": false, "description": "Transform optional chaining operators into a series of nil checks", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.23.4", "@babel/traverse": "^7.23.4"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-optional-chaining", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-optional-chaining", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-optional-chaining"}, "type": "commonjs", "version": "7.23.4"}