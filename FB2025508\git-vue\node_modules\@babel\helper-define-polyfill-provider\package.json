{"_from": "@babel/helper-define-polyfill-provider@^0.6.0", "_id": "@babel/helper-define-polyfill-provider@0.6.0", "_inBundle": false, "_integrity": "sha512-efwOM90nCG6YeT8o3PCyBVSxRfmILxCNL+TNI8CGQl7a62M0Wd9VkV+XHwIlkOz1r4b+lxu6gBjdWiOMdUCrCQ==", "_location": "/@babel/helper-define-polyfill-provider", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-define-polyfill-provider@^0.6.0", "name": "@babel/helper-define-polyfill-provider", "escapedName": "@babel%2fhelper-define-polyfill-provider", "scope": "@babel", "rawSpec": "^0.6.0", "saveSpec": null, "fetchSpec": "^0.6.0"}, "_requiredBy": ["/babel-plugin-polyfill-corejs2"], "_resolved": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.0.tgz", "_shasum": "4d1a8b898c8299a2fcf295d7d356d2648471ab31", "_spec": "@babel/helper-define-polyfill-provider@^0.6.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\babel-plugin-polyfill-corejs2", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "deprecated": false, "description": "Babel helper to create your own polyfill provider", "devDependencies": {"@babel/cli": "^7.22.6", "@babel/core": "^7.22.6", "@babel/generator": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/traverse": "^7.22.6", "babel-loader": "^8.1.0", "rollup": "^2.3.2", "rollup-plugin-babel": "^4.4.0", "strip-ansi": "^6.0.0", "webpack": "^4.42.1", "webpack-cli": "^3.3.11"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "58703f07c9cff9f27d145215265042094739a175", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/helper-define-polyfill-provider", "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-helper-define-polyfill-provider"}, "version": "0.6.0"}