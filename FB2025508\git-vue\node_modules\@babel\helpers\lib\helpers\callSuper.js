"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _callSuper;
var _getPrototypeOf = require("getPrototypeOf");
var _isNativeReflectConstruct = require("./isNativeReflectConstruct.js");
var _possibleConstructorReturn = require("possibleConstructorReturn");
function _callSuper(_this, derived, args) {
  derived = _getPrototypeOf(derived);
  return _possibleConstructorReturn(_this, (0, _isNativeReflectConstruct.default)() ? Reflect.construct(derived, args || [], _getPrototypeOf(_this).constructor) : derived.apply(_this, args));
}

//# sourceMappingURL=callSuper.js.map
