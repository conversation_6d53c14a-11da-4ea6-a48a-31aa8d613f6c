{"_from": "@babel/plugin-syntax-top-level-await@^7.14.5", "_id": "@babel/plugin-syntax-top-level-await@7.14.5", "_inBundle": false, "_integrity": "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==", "_location": "/@babel/plugin-syntax-top-level-await", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-top-level-await@^7.14.5", "name": "@babel/plugin-syntax-top-level-await", "escapedName": "@babel%2fplugin-syntax-top-level-await", "scope": "@babel", "rawSpec": "^7.14.5", "saveSpec": null, "fetchSpec": "^7.14.5"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "_shasum": "c1cfdadc35a646240001f06138247b741c34d94c", "_spec": "@babel/plugin-syntax-top-level-await@^7.14.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "deprecated": false, "description": "Allow parsing of top-level await in modules", "devDependencies": {"@babel/core": "7.14.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-top-level-await", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-top-level-await", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-top-level-await"}, "version": "7.14.5"}