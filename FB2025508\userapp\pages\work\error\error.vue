<template>
	<view class="error-container">
		<view class="error-box">
			<uni-icons type="error" size="80" color="#FF5A5F"></uni-icons>
			<text class="error-title">访问受限</text>
			<text class="error-message">{{ errorMessage }}</text>
			<button class="back-button" @click="goBack">返回登录</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				errorMessage: '您没有权限访问此页面'
			}
		},
		onLoad(options) {
			// 接收传递的错误信息
			if (options.message) {
				this.errorMessage = options.message;
			}
		},
		methods: {
			goBack() {
				// 返回登录页面
				uni.reLaunch({
					url: '/pages/login'
				});
			}
		}
	}
</script>

<style>
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	background-color: #f8f8f8;
	padding: 0 30rpx;
}

.error-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 60rpx 40rpx;
	width: 80%;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.error-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333333;
	margin-top: 40rpx;
	margin-bottom: 20rpx;
}

.error-message {
	font-size: 32rpx;
	color: #666666;
	text-align: center;
	line-height: 1.6;
	margin-bottom: 50rpx;
}

.back-button {
	background-color: #007AFF;
	color: #FFFFFF;
	border-radius: 50rpx;
	padding: 20rpx 60rpx;
	font-size: 32rpx;
	border: none;
}
</style>
