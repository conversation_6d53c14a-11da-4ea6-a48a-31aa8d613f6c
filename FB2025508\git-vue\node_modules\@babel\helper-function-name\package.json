{"_from": "@babel/helper-function-name@^7.23.0", "_id": "@babel/helper-function-name@7.23.0", "_inBundle": false, "_integrity": "sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==", "_location": "/@babel/helper-function-name", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-function-name@^7.23.0", "name": "@babel/helper-function-name", "escapedName": "@babel%2fhelper-function-name", "scope": "@babel", "rawSpec": "^7.23.0", "saveSpec": null, "fetchSpec": "^7.23.0"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-wrap-function", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-function-name", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.23.0.tgz", "_shasum": "1f9a3cdbd5b2698a670c30d2735f9af95ed52759", "_spec": "@babel/helper-function-name@^7.23.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\traverse", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/template": "^7.22.15", "@babel/types": "^7.23.0"}, "deprecated": false, "description": "Helper function to change the property 'name' of every function", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-function-name", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-function-name", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-function-name"}, "type": "commonjs", "version": "7.23.0"}