<template>
	<view class="container">
		<!-- 顶部统计信息 -->
		<view class="stats-header">
			<view class="title">管理</view>
			<view class="stats-container">
				<view class="stat-item">
					<view class="stat-value">{{ orderStats.today || 0 }}</view>
					<view class="stat-label">门店订单总数 今日</view>
				</view>
				<view class="stat-item">
					<view class="stat-value">{{ orderStats.week || 0 }}</view>
					<view class="stat-label">门店订单总数 本周</view>
				</view>
				<view class="stat-item">
					<view class="stat-value">{{ orderStats.month || 0 }}</view>
					<view class="stat-label">门店订单总数 本月</view>
				</view>
			</view>
		</view>
		
		<!-- 员工列表 -->
		<view class="staff-list-container">
			<view class="list-header">员工列表（总计{{ staffList.length }}人）</view>
			<view class="staff-list">
				<view v-for="(staff, index) in staffList" :key="index" class="staff-item">
					<view class="staff-avatar">
						<image src="/static/images/avatar-placeholder.png" mode="aspectFill"></image>
					</view>
					<view class="staff-info">
						<view class="staff-name">
							{{ staff.staff_name }}
							<text class="gender-tag" :class="{'male': staff.gender === '1', 'female': staff.gender === '0'}">
								{{ staff.gender === '1' ? '男' : staff.gender === '0' ? '女' : '未知' }}
							</text>
						</view>
						<view class="staff-phone" @click="makePhoneCall(staff.phone)">{{ staff.phone }}</view>
					</view>
					<view class="staff-codes">
						<view class="code-tag">{{ staff.staff_code }}</view>
						<view class="code-tag">{{ staff.group_code }}</view>
						<view class="code-tag">{{ staff.subgroup_code }}</view>
					</view>
					<view class="staff-arrow">
						<text class="arrow-icon">></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getStaffList, getOrderStats } from '@/api/work/centerorder.js';
	
	export default {
		data() {
			return {
				orderStats: {
					today: 0,
					week: 0,
					month: 0
				},
				staffList: [],
				currentStaffId: null
			}
		},
		onLoad() {
			this.loadData();
		},
		methods: {
			loadData() {
				// 获取订单统计数据
				getOrderStats().then(res => {
					if (res.code === 200) {
						this.orderStats = res.data;
						this.currentStaffId = res.data.staff_id;
					} else {
						uni.showToast({
							title: '获取订单统计失败',
							icon: 'none'
						});
					}
				});
				
				// 获取员工列表数据
				getStaffList().then(res => {
					if (res.code === 200) {
						this.staffList = res.data;
					} else {
						uni.showToast({
							title: '获取员工列表失败',
							icon: 'none'
						});
					}
				});
			},
			makePhoneCall(phoneNumber) {
				if (!phoneNumber) {
					uni.showToast({
						title: '电话号码不存在',
						icon: 'none'
					});
					return;
				}
				
				uni.makePhoneCall({
					phoneNumber: phoneNumber,
					fail: (err) => {
						console.log('拨打电话失败', err);
					}
				});
			}
		}
	}
</script>

<style>
	.container {
		padding-bottom: 20px;
	}
	
	.stats-header {
		background-color: #2196f3;
		color: #fff;
		padding: 20px 15px;
	}
	
	.title {
		font-size: 18px;
		font-weight: bold;
		margin-bottom: 15px;
	}
	
	.stats-container {
		display: flex;
		justify-content: space-between;
	}
	
	.stat-item {
		flex: 1;
		text-align: center;
	}
	
	.stat-value {
		font-size: 24px;
		font-weight: bold;
	}
	
	.stat-label {
		font-size: 12px;
		margin-top: 5px;
		white-space: nowrap;
	}
	
	.staff-list-container {
		background-color: #f5f5f5;
		padding: 10px 15px;
	}
	
	.list-header {
		font-size: 14px;
		color: #666;
		margin: 10px 0;
	}
	
	.staff-list {
		background-color: #fff;
		border-radius: 8px;
	}
	
	.staff-item {
		display: flex;
		align-items: center;
		padding: 15px;
		border-bottom: 1px solid #eee;
	}
	
	.staff-avatar {
		width: 40px;
		height: 40px;
		margin-right: 15px;
	}
	
	.staff-avatar image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}
	
	.staff-info {
		flex: 1;
	}
	
	.staff-name {
		font-size: 16px;
		color: #333;
	}
	
	.gender-tag {
		display: inline-block;
		font-size: 12px;
		padding: 1px 6px;
		border-radius: 3px;
		margin-left: 5px;
	}
	
	.male {
		background-color: #e6f7ff;
		color: #1890ff;
	}
	
	.female {
		background-color: #fff0f6;
		color: #eb2f96;
	}
	
	.staff-phone {
		font-size: 14px;
		color: #0066cc;
		margin-top: 5px;
	}
	
	.staff-codes {
		display: flex;
		margin-right: 10px;
	}
	
	.code-tag {
		background-color: #f0f0f0;
		color: #666;
		font-size: 12px;
		padding: 2px 8px;
		margin-right: 5px;
		border-radius: 4px;
	}
	
	.staff-arrow {
		color: #cccccc;
		font-size: 16px;
	}
</style>
