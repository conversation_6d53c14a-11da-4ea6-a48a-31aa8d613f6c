{"_from": "@babel/plugin-syntax-class-static-block@^7.14.5", "_id": "@babel/plugin-syntax-class-static-block@7.14.5", "_inBundle": false, "_integrity": "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==", "_location": "/@babel/plugin-syntax-class-static-block", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-class-static-block@^7.14.5", "name": "@babel/plugin-syntax-class-static-block", "escapedName": "@babel%2fplugin-syntax-class-static-block", "scope": "@babel", "rawSpec": "^7.14.5", "saveSpec": null, "fetchSpec": "^7.14.5"}, "_requiredBy": ["/@babel/plugin-transform-class-static-block", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "_shasum": "195df89b146b4b78b3bf897fd7a257c84659d406", "_spec": "@babel/plugin-syntax-class-static-block@^7.14.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "deprecated": false, "description": "Allow parsing of class static blocks", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-static-block", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-class-static-block", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-static-block"}, "version": "7.14.5"}