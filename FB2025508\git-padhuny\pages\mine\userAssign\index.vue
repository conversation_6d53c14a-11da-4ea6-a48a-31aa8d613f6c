<template>
  <view class="container">
    <!-- 头部标题 -->
    <view class="header">
      <view class="back-icon" @click="goBack">
        <text class="iconfont icon-left"></text>
      </view>
      <view class="title">用户分配</view>
    </view>

    <!-- 总计用户 -->
    <view class="total-info">
      <text>总计人数: {{ selectedUsers.length }}/{{ userList.length }}</text>
      <view class="action-buttons">
        <view class="import-button" @click="importStaffUsers">
          <text>导入店员用户</text>
        </view>
        <view class="assign-button" @click="assignToStaff">
          <text>分配</text>
        </view>
      </view>
    </view>

    <!-- 用户列表 -->
    <scroll-view scroll-y="true" class="user-list">
      <view v-for="(user, index) in userList" :key="index" 
            :class="['user-item', {'user-item-selected': user.selected}]" 
            @click="toggleSelectUser(index)">
        <view class="checkbox">
          <image v-if="user.selected" src="/static/images/checkbox_selected.png" mode="aspectFit" class="checkbox-image"></image>
          <image v-else src="/static/images/checkbox_normal.png" mode="aspectFit" class="checkbox-image"></image>
        </view>
        <view class="user-info">
          <view class="user-name">{{ user.user_name || user.name }}</view>
          <view class="user-details">
            <text class="gender">{{ user.gender === '0' ? '男' : '女' }}</text>
            <text class="age">{{ user.age }}岁</text>
            <text class="phone">{{ user.phone }}</text>
          </view>
        </view>
      </view>
      
      <!-- 空数据提示 -->
      <view v-if="userList.length === 0" class="empty-data">
        <text>暂无待分配用户</text>
      </view>
    </scroll-view>

    <!-- 底部说明 -->
    <view class="footer-note">
      <text></text>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-button">
      <button class="finish-button" @click="finishAssignment">分配结束</button>
    </view>
  </view>
</template>

<script>
import { getUnassignedUsers, assignUserToStaff } from '@/api/work/retrun_part.js'

export default {
  data() {
    return {
      userList: [], // 用户列表
      shopId: '', // 当前店铺ID
      regionId: '' // 当前区域ID
    }
  },
  computed: {
    // 已选择的用户
    selectedUsers() {
      return this.userList.filter(user => user.selected)
    }
  },
  onLoad(options) {
    // 获取传入的店铺ID
    if (options.shopId) {
      this.shopId = options.shopId
    }
    
    // 获取传入的区域ID
    if (options.regionId) {
      this.regionId = options.regionId
    }
    
    // 加载用户列表
    this.loadUserList()
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 导入店员用户
    importStaffUsers() {
      uni.navigateTo({
        url: './shopassigondev'
      })
    },
    
    // 加载未分配的用户列表
    loadUserList() {
      uni.showLoading({
        title: '加载中...'
      })
      
      getUnassignedUsers({
        shopId: this.shopId,
        regionId: this.regionId
      }).then(res => {
        uni.hideLoading()
        
        if (res.code === 200 && res.data) {
          // 转换数据并按性别排序（先男后女）
          let users = res.data.map(item => {
            return {
              ...item,
              selected: false, // 添加选中状态
              // 确保gender是字符串
              gender: typeof item.gender === 'string' ? item.gender : item.gender.toString(),
              // 计算年龄（使用birth_date字段）
              age: item.birth_date ? this.calculateAge(item.birth_date) : '--'
            }
          })
          
          // 按性别排序（先男后女）
          users.sort((a, b) => {
            return a.gender.localeCompare(b.gender)
          })
          
          this.userList = users
        } else {
          uni.showToast({
            title: res.msg || '获取用户列表失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        uni.hideLoading()
        console.error('获取用户列表异常:', err)
        uni.showToast({
          title: '获取用户列表异常',
          icon: 'none'
        })
      })
    },
    
    // 计算年龄
    calculateAge(birthDateString) {
      try {
        const birthDate = new Date(birthDateString)
        const today = new Date()
        let age = today.getFullYear() - birthDate.getFullYear()
        const monthDiff = today.getMonth() - birthDate.getMonth()
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--
        }
        
        return age
      } catch (e) {
        return '--'
      }
    },
    
    // 切换用户选中状态
    toggleSelectUser(index) {
      this.userList[index].selected = !this.userList[index].selected
    },
    
    // 分配给店员
    assignToStaff() {
      if (this.selectedUsers.length === 0) {
        uni.showToast({
          title: '请选择要分配的用户',
          icon: 'none'
        })
        return
      }
      
      // 跳转到店员选择页面
      uni.navigateTo({
        url: './shopassign?shopId=' + this.shopId + '&userIds=' + JSON.stringify(this.selectedUsers.map(user => user.user_id))
      })
    },
    
    // 完成分配流程
    finishAssignment() {
      uni.navigateBack({
        delta: 1 // 返回上一页
      })
    },
    
    // 检查是否有导入的数据
    checkImportedData() {
      try {
        const importedData = uni.getStorageSync('imported_staff_users')
        if (importedData && importedData.users && importedData.users.length > 0) {
          // 将导入的用户添加到当前列表中并标记为选中
          const importedUsers = importedData.users.map(user => {
            return {
              ...user,
              selected: true, // 标记为已选中
              gender: typeof user.gender === 'string' ? user.gender : user.gender.toString(),
              age: user.birth_date ? this.calculateAge(user.birth_date) : '--'
            }
          })
          
          // 清空现有的选中状态
          this.userList.forEach(user => {
            user.selected = false
          })
          
          // 将导入的用户合并到列表中，避免重复
          importedUsers.forEach(importedUser => {
            const existIndex = this.userList.findIndex(u => u.user_id === importedUser.user_id)
            if (existIndex !== -1) {
              // 如果用户已存在，则更新选中状态
              this.userList[existIndex].selected = true
            } else {
              // 如果用户不存在，则添加到列表
              this.userList.push(importedUser)
            }
          })
          
          // 清除存储的数据，避免重复导入
          uni.removeStorageSync('imported_staff_users')
          
          // 显示导入成功提示
          uni.showToast({
            title: `已导入${importedUsers.length}个用户`,
            icon: 'success'
          })
        }
      } catch (e) {
        console.error('处理导入数据失败:', e)
      }
    }
  },
  // 页面显示时刷新数据
  onShow() {
    // 每次显示页面时重新加载列表，以反映最新分配状态
    this.loadUserList()
    
    // 检查是否有从导入页面返回的数据
    this.checkImportedData()
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.back-icon {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.total-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-top: 20rpx;
  font-size: 28rpx;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
}

.import-button, .assign-button {
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #ffffff;
}

.import-button {
  background-color: #52c41a;
}

.assign-button {
  background-color: #1890ff;
}

.user-list {
  flex: 1;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eeeeee;
  background-color: #ffffff;
  transition: all 0.3s ease;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.user-item-selected {
  background-color: #e6f7ff;
  border: 1rpx solid #91d5ff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.checkbox {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-image {
  width: 40rpx;
  height: 40rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-details {
  display: flex;
  font-size: 28rpx;
  color: #666666;
  gap: 20rpx;
}

.user-details .gender {
  background-color: #f0f9ff;
  color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.user-details .age {
  background-color: #f6ffed;
  color: #52c41a;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.user-details .phone {
  color: #666666;
  font-family: monospace;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999999;
  font-size: 28rpx;
}

.footer-note {
  padding: 30rpx;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
}

.footer-button {
  padding: 30rpx;
}

.finish-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4b8ff0;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 40rpx;
  text-align: center;
}
</style>
