{"_from": "@babel/plugin-syntax-object-rest-spread@^7.8.3", "_id": "@babel/plugin-syntax-object-rest-spread@7.8.3", "_inBundle": false, "_integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==", "_location": "/@babel/plugin-syntax-object-rest-spread", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-object-rest-spread@^7.8.3", "name": "@babel/plugin-syntax-object-rest-spread", "escapedName": "@babel%2fplugin-syntax-object-rest-spread", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@babel/plugin-transform-object-rest-spread", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "_shasum": "60e225edcbd98a640332a2e72dd3e66f1af55871", "_spec": "@babel/plugin-syntax-object-rest-spread@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "deprecated": false, "description": "Allow parsing of object rest/spread", "devDependencies": {"@babel/core": "^7.8.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-object-rest-spread", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread"}, "version": "7.8.3"}