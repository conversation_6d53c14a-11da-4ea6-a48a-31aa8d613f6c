{"_from": "@babel/plugin-syntax-jsx@^7.8.3", "_id": "@babel/plugin-syntax-jsx@7.23.3", "_inBundle": false, "_integrity": "sha512-EB2MELswq55OHUoRZLGg/zC7QWUKfNLpE57m/S2yr1uEneIgsTgrSzXP3NXEsMkVn76OlaVVnzN+ugObuYGwhg==", "_location": "/@babel/plugin-syntax-jsx", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-jsx@^7.8.3", "name": "@babel/plugin-syntax-jsx", "escapedName": "@babel%2fplugin-syntax-jsx", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@vue/babel-plugin-jsx", "/@vue/babel-plugin-transform-vue-jsx", "/@vue/babel-preset-app", "/@vue/babel-sugar-composition-api-inject-h", "/@vue/babel-sugar-composition-api-render-instance", "/@vue/babel-sugar-functional-vue", "/@vue/babel-sugar-inject-h", "/@vue/babel-sugar-v-model", "/@vue/babel-sugar-v-on"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.23.3.tgz", "_shasum": "8f2e4f8a9b5f9aa16067e142c1ac9cd9f810f473", "_spec": "@babel/plugin-syntax-jsx@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@vue\\babel-preset-app", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Allow parsing of jsx", "devDependencies": {"@babel/core": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-jsx", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-jsx"}, "type": "commonjs", "version": "7.23.3"}