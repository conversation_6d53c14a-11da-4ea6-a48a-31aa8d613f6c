<template>
  <view class="mine-container" :style="{height: `${windowHeight}px`}">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <!-- <view v-if="!avatar" class="cu-avatar xl round bg-white">
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix">
          </image> -->
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            点击登录
          </view>
          <view v-if="name" @click="handleToInfo" class="user-info">
            <view class="u_title">
              用户名：{{ name }}
            </view>
            <view class="u_detail" v-if="userDetail">
              <view class="detail-item">店铺名称：{{ userDetail.shop_name }}</view>
              <view class="detail-item">员工编号：{{ userDetail.staff_code }}</view>
              <view class="detail-item">昵称：{{ userDetail.nickname }}</view>
              <view class="detail-item">电话号码：{{ userDetail.phone }}</view>
              <view class="detail-item">所属城市：{{ userDetail.region_name }}</view>
            </view>
          </view>
        </view>
        <!-- <view @click="handleToInfo" class="flex align-center">
          <text>个人信息</text>
          <view class="iconfont icon-right"></view>
        </view> -->
      </view>
    </view>

    <view class="content-section">
<!--      <view class="mine-actions grid col-4 text-center">
        <view class="action-item" @click="handleJiaoLiuQun">
          <view class="iconfont icon-friendfill text-pink icon"></view>
          <text class="text">交流群</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-service text-blue icon"></view>
          <text class="text">在线客服</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-community text-mauve icon"></view>
          <text class="text">反馈社区</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-dianzan text-green icon"></view>
          <text class="text">点赞我们</text>
        </view>
      </view> -->

      <view class="menu-list">
        <view class="list-cell list-cell-arrow" @click="handleToEditInfo">
          <view class="menu-item-box">
            <view class="iconfont icon-user menu-icon"></view>
            <view>订单数据统计</view>
          </view>
        </view>
       <view class="list-cell list-cell-arrow" @click="handleHelp">
          <view class="menu-item-box">
            <view class="iconfont icon-help menu-icon"></view>
            <view>服务项目统计</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleAbout">
          <view class="menu-item-box">
            <view class="iconfont icon-aixin menu-icon"></view>
            <view>向用户自我介绍</view>
          </view>
        </view>
		<view class="list-cell list-cell-arrow" @click="handleuserApplication">
		  <view class="menu-item-box">
		    <view class="iconfont icon-aixin menu-icon"></view>
		    <view>个人资料</view>
		  </view>
		</view>
		<view class="list-cell list-cell-arrow" @click="handleuserAssign">
		  <view class="menu-item-box">
		    <view class="iconfont icon-aixin menu-icon"></view>
		    <view>我要加盟</view>
		  </view>
		</view>
    <view class="list-cell list-cell-arrow" @click="AddUser">
		  <view class="menu-item-box">
		    <view class="iconfont icon-aixin menu-icon"></view>
		    <view>我要报名</view>
		  </view>
		</view>
		<view class="list-cell list-cell-arrow" @click="ShopOpinion">
		  <view class="menu-item-box">
		    <view class="iconfont icon-aixin menu-icon"></view>
		    <view>意见反馈</view>
		  </view>
		</view>
		<view class="list-cell">
		  <view class="menu-item-box-between">
		    <view class="menu-item-left">
              <view class="iconfont icon-aixin menu-icon"></view>
              <view>是否参与新建订单</view>
            </view>
            <view class="menu-item-right">
              <view class="switch-label">允许</view>
              <switch :checked="allowStaffLogin" @change="toggleStaffLogin" color="#3c96f3" />
            </view>
		  </view>
		</view>
        <view class="list-cell list-cell-arrow" @click="handleToSetting">
          <view class="menu-item-box">
            <view class="iconfont icon-setting menu-icon"></view>
            <view>应用设置</view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
  import storage from '@/utils/storage'
  import { updateStaffLoginStatus, updateStaffLoginStatusJoin, getUserDetailInfo } from '@/api/work/retrun_part.js'
  
  export default {
    data() {
      return {
        name: this.$store.state.user.name,
        version: getApp().globalData.config.appInfo.version,
        allowStaffLogin: true,
        loading: false,
        userDetail: null // 用户详细信息
      }
    },
    computed: {
      avatar() {
        return this.$store.state.user.avatar
      },
      windowHeight() {
        return uni.getSystemInfoSync().windowHeight - 50
      }
    },
    onShow() {
      this.loadStaffLoginSetting();
      this.loadUserDetailInfo();
    },
    methods: {
      // 加载用户详细信息
      loadUserDetailInfo() {
        // 显示加载中
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        
        // 调用API获取用户详细信息
        getUserDetailInfo().then(res => {
          if (res && res.code === 200 && res.data) {
            this.userDetail = res.data;
          }
          uni.hideLoading();
        }).catch(err => {
          console.error('获取用户详细信息失败', err);
          uni.hideLoading();
        });
      },
      
      loadStaffLoginSetting() {
        // 显示加载中
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        
        // 调用后端接口获取当前状态
        this.getShopInfo().then(res => {
          if (res && res.code === 200 && res.data) {
            // 获取店铺下任意一个店员的登录状态，判断当前设置
            const loginStatus = res.data.staffLoginStatus;
            this.allowStaffLogin = loginStatus === '1';
            
            // 同步到本地存储
            uni.setStorageSync('allowStaffLogin', this.allowStaffLogin);
          }
          uni.hideLoading();
        }).catch(() => {
          // 从本地存储获取
          const setting = uni.getStorageSync('allowStaffLogin');
          this.allowStaffLogin = setting !== '' ? setting : true;
          uni.hideLoading();
        });
      },
      
      // 获取店铺信息和店员登录状态
      getShopInfo() {
        return new Promise((resolve, reject) => {
          updateStaffLoginStatusJoin({ action: 'query' })
            .then(res => {
              resolve(res);
            })
            .catch(err => {
              console.error('获取店员登录状态失败', err);
              reject(err);
            });
        });
      },
      toggleStaffLogin(e) {
        if (this.loading) return;
        this.loading = true;
        
        const value = e.detail.value;
        const statusValue = value ? '1' : '0';
        
        // 显示加载中
        uni.showLoading({
          title: '更新中...',
          mask: true
        });
        
        // 调用后端接口保存设置
        updateStaffLoginStatusJoin({
          action: 'update',
          loginStatus: statusValue
        })
        .then(res => {
          uni.hideLoading();
          
          if (res.code === 200) {
            // 更新成功，保存到本地存储
            this.allowStaffLogin = value;
            uni.setStorageSync('allowStaffLogin', value);
            
            // 提示成功信息
            this.$modal.showToast(value ? '已允许参与新建订单' : '已禁止参与新建订单');
          } else {
            // 更新失败，恢复原来的设置
            this.allowStaffLogin = !value;
            this.$modal.showToast(res.msg || '更新失败，请稍后重试');
          }
          this.loading = false;
        })
        .catch(err => {
          uni.hideLoading();
          console.error('更新店员参与状态失败', err);
          this.$modal.showToast('更新失败，请稍后重试');
          // 恢复原来的设置
          this.allowStaffLogin = !value;
          this.loading = false;
        });
      },
      handleToInfo() {
        this.$tab.navigateTo('/pages/mine/info/index')
      },
      handleToEditInfo() {
        this.$tab.navigateTo('/pages/work/assistantMag/assistantMag')
      },
      handleToSetting() {
        this.$tab.navigateTo('/pages/mine/setting/index')
      },
      handleToLogin() {
        this.$tab.reLaunch('/pages/login')
      },
      handleToAvatar() {
        this.$tab.navigateTo('/pages/mine/avatar/index')
      },
      handleLogout() {
        this.$modal.confirm('确定注销并退出系统吗？').then(() => {
          this.$store.dispatch('LogOut').then(() => {
            this.$tab.reLaunch('/pages/index')
          })
        })
      },
      handleHelp() {
        this.$tab.navigateTo('/pages/mine/UserSerTotal/UserSerTotal')
      },
      handleAbout() {
        this.$tab.navigateTo('/pages/mine/Introduce/Introduce')
      },
	  handleuserAssign() {
	    this.$tab.navigateTo('/pages/mine/ShopApplication/ShopApplication')
	  },
	  ShopOpinion() {
	    this.$tab.navigateTo('/pages/mine/ShopOpinion/ShopOpinion')
	  },
    AddUser() {
	    this.$tab.navigateTo('/pages/mine/AddUser/AddUser')
	  },
	  handleuserApplication() {
	    this.$tab.navigateTo('/pages/mine/PersonalData/PersonalData')
	  },
      handleJiaoLiuQun() {
        this.$modal.showToast('QQ群：①133713780、②146013835')
      },
      handleBuilding() {
        this.$modal.showToast('模块建设中~')
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #f5f6f7;
  }

  .mine-container {
    width: 100%;
    height: 100%;
    padding-bottom: 20px; // 添加底部内边距，确保内容不被遮挡


    .header-section {
      padding: 15px 15px 45px 15px;
      background-color: #3c96f3;
      color: white;

      .login-tip {
        font-size: 18px;
        margin-left: 10px;
      }

      .cu-avatar {
        border: 2px solid #eaeaea;

        .icon {
          font-size: 40px;
        }
      }

      .user-info {
        margin-left: 15px;
        width: 100%;

        .u_title {
          font-size: 18px;
          line-height: 30px;
          font-weight: bold;
        }
        
        .u_detail {
          margin-top: 5px;
          
          .detail-item {
            font-size: 14px;
            line-height: 22px;
            color: rgba(255, 255, 255, 0.9);
          }
        }
      }
    }

    .content-section {
      position: relative;
      top: -30px; // 减少顶部负边距，从-50px改为-30px
      padding-bottom: 30px; // 添加底部内边距

      .mine-actions {
        margin: 15px 15px;
        padding: 20px 0px;
        border-radius: 8px;
        background-color: white;

        .action-item {
          .icon {
            font-size: 24px;
          }

          .text {
            display: block;
            font-size: 13px;
            margin: 8px 0px;
          }
        }
      }
      
      // 添加菜单列表的样式
      .menu-list {
        margin: 15px;
        margin-bottom: 60px; // 增加底部外边距
      }
      
      // 调整菜单项的样式
      .list-cell {
        margin-bottom: 8px; // 增加菜单项之间的间距
      }
    }
  }
  
  .menu-item-box-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
  }
  
  .menu-item-left {
    display: flex;
    align-items: center;
  }
  
  .menu-item-right {
    display: flex;
    align-items: center;
  }
  
  .switch-label {
    font-size: 14px;
    margin-right: 10px;
    color: #666;
  }
</style>
