<template>
  <view class="container">
    <!-- 头部标题 -->
    <view class="header">
      <view class="back-icon" @click="goBack">
        <text class="iconfont icon-left"></text>
      </view>
      <view class="title">选择目标店员</view>
    </view>

    <!-- 店员列表 -->
    <scroll-view scroll-y="true" class="staff-list">
      <view class="shop-title">
        <text>{{ shopInfo.shopName || '当前店铺' }}</text>
      </view>
      
      <view v-for="(staff, index) in staffList" :key="index" 
            :class="['staff-item', {'staff-item-selected': selectedStaffId === staff.staff_id}]" 
            @click="selectStaff(staff)">
        <view class="staff-info">
          <text class="staff-code">{{ staff.staff_code }}</text>
          <text class="staff-name">{{ staff.staff_name }}</text>
          <text class="staff-count">({{ staff.assigned_user_count || 0 }})</text>
        </view>
      </view>
      
      <!-- 空数据提示 -->
      <view v-if="staffList.length === 0" class="empty-data">
        <text>暂无可用店员</text>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="cancel-button" @click="goBack">取消</button>
      <button class="confirm-button" @click="confirmAssignment" :disabled="!selectedStaffId">确定</button>
    </view>
  </view>
</template>

<script>
import { getShopStaffList, assignUsersToStaff } from '@/api/work/retrun_part.js'

export default {
  data() {
    return {
      staffList: [], // 店员列表
      selectedStaffId: null, // 选中的店员ID
      shopId: '', // 当前店铺ID
      userIds: [], // 待分配的用户ID列表
      shopInfo: {}, // 店铺信息
      excludeStaffId: null, // 要排除的店员ID
    }
  },
  onLoad(options) {
    console.log('shopassigndevex加载参数：', options);
    
    // 获取传入的店铺ID
    if (options.shopId) {
      this.shopId = options.shopId
    }
    
    // 获取传入的用户ID列表
    if (options.userIds) {
      try {
        this.userIds = JSON.parse(options.userIds)
      } catch (e) {
        console.error('解析用户ID列表失败:', e)
        this.userIds = []
      }
    }
    
    // 获取要排除的店员ID
    if (options.excludeStaffId) {
      this.excludeStaffId = options.excludeStaffId
    }
    
    // 加载店员列表
    this.loadStaffList()
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 加载店员列表
    loadStaffList() {
      uni.showLoading({
        title: '加载中...'
      })
      
      getShopStaffList({
        shopId: this.shopId
      }).then(res => {
        uni.hideLoading()
        
        if (res.code === 200 && res.data) {
          // 获取店员列表并排除指定的店员
          let staffList = res.data.staffList || []
          
          // 确保源店员ID与比较时格式一致
          const sourceStaffId = String(this.excludeStaffId);
          
          console.log('排除店员ID:', sourceStaffId);
          
          // 严格比较并排除源店员
          if (sourceStaffId) {
            staffList = staffList.filter(staff => String(staff.staff_id) !== sourceStaffId)
            console.log('过滤后店员数量:', staffList.length);
          }
          
          this.staffList = staffList
          this.shopInfo = res.data.shopInfo || {}
          
          // 按照用户分配数量升序排序
          this.staffList.sort((a, b) => {
            return (a.assigned_user_count || 0) - (b.assigned_user_count || 0)
          })
        } else {
          uni.showToast({
            title: res.msg || '获取店员列表失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        uni.hideLoading()
        console.error('获取店员列表异常:', err)
        uni.showToast({
          title: '获取店员列表异常',
          icon: 'none'
        })
      })
    },
    
    // 选择店员
    selectStaff(staff) {
      this.selectedStaffId = staff.staff_id
    },
    
    // 确认分配
    confirmAssignment() {
      if (!this.selectedStaffId) {
        uni.showToast({
          title: '请选择目标店员',
          icon: 'none'
        })
        return
      }
      
      if (this.userIds.length === 0) {
        uni.showToast({
          title: '没有选择待分配的用户',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({
        title: '分配中...'
      })
      
      // 调用分配接口
      assignUsersToStaff({
        staffId: this.selectedStaffId,
        shopId: this.shopId,
        userIds: this.userIds
      }).then(res => {
        uni.hideLoading()
        
        if (res.code === 200) {
          uni.showToast({
            title: '分配成功',
            icon: 'success'
          })
          
          // 设置分配成功标记和详细信息，方便其他页面判断是否需要刷新
          uni.setStorageSync('user_assigned', {
            assigned: true,
            sourceStaffId: this.excludeStaffId,
            targetStaffId: this.selectedStaffId,
            timestamp: Date.now()
          });
          
          // 分配成功后返回一级（回到用户列表页）
          setTimeout(() => {
            uni.navigateBack({
              delta: 1
            })
          }, 1500)
        } else {
          uni.showToast({
            title: res.msg || '分配失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        uni.hideLoading()
        console.error('分配用户异常:', err)
        uni.showToast({
          title: '分配用户异常',
          icon: 'none'
        })
      })
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.back-icon {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.staff-list {
  flex: 1;
  padding: 0 30rpx;
  background-color: #ffffff;
  margin-top: 20rpx;
}

.shop-title {
  padding: 20rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1rpx solid #eeeeee;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eeeeee;
  border-radius: 8rpx;
  margin-bottom: 6rpx;
  transition: all 0.3s ease;
  position: relative;
}

.staff-item-selected {
  background-color: #e6f7ff;
  border-left: 8rpx solid #1890ff;
}

.staff-item-selected .staff-code,
.staff-item-selected .staff-name,
.staff-item-selected .staff-count {
  color: #1890ff;
  font-weight: 500;
}

.staff-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.staff-code {
  font-size: 28rpx;
  color: #333333;
  margin-right: 10rpx;
}

.staff-name {
  font-size: 28rpx;
  color: #666666;
  margin-right: 10rpx;
}

.staff-count {
  font-size: 28rpx;
  color: #999999;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999999;
  font-size: 28rpx;
}

.footer-buttons {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
}

.cancel-button, .confirm-button {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 40rpx;
  text-align: center;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #dddddd;
}

.confirm-button {
  background-color: #4b8ff0;
  color: #ffffff;
}

.confirm-button[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}
</style>
