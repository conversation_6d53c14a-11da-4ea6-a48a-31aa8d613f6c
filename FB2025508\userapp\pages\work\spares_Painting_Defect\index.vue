<!-- 备品缺陷 -->
<template>
	<view class="part-container">

			<uni-forms ref="form" :modelValue="formData">
                <uni-forms-item name="barCode">
                   <uni-section title="条码" subTitle="" type="line" padding>
					<!-- <uni-easyinput ref="barCodeInput"  trim="all" v-model="formData.barCode" focus placeholder="请输入内容" @change="barCodeChange"></uni-easyinput> -->
					<uni-view class="bar-code-css-view1 uni-input-wrapper">
						<uni-easyinput 
							id="barCodeInputClick" 
							class="uni-input bar-code-css1" 
							focus 
							placeholder="请进行扫码" 
							trim="all"
							v-model="formData.barCode" 
							@focus="barCodeFocus" 
							@blur="barCodeBlur" 
							@confirm="barCodeChange" />
					</uni-view>
				</uni-section>
				</uni-forms-item>
				<uni-forms-item>
					<uni-col :span="20">
						<uni-section class="mb-10" title="产品:" ><template v-slot:right>{{formData.proname}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
                <uni-forms-item>
					<uni-col :span="20">
						<uni-section class="mb-10" title="Erp号:"><template v-slot:right>{{formData.erpnum}}</template></uni-section>
					</uni-col>
				</uni-forms-item>
 <!-- 下拉框按钮 -->
 <view class="dropdown" @click="toggleDropdown">
      <text :class="{ 'placeholder': selectedItems.length === 0 }">{{ dropdownButtonText }}</text>
    </view>

    <!-- 下拉框选项列表 -->
    <view v-if="showDropdown" class="dropdown-options">
      <view v-for="(item, index) in options" :key="item.id" class="option-item" @click="toggleSelection(item.id)">
        <text :class="{ selected: selectedItems.includes(item.id) }">{{ item.name }}</text>
      </view>
    </view>

    <!-- 显示已选择的项 -->
    <view v-if="selectedItems.length > 0" class="selected-items">
      <text>已选项：</text>
      <view v-for="(item, index) in selectedItems" :key="item" class="selected-item">
        {{ getOptionName(item) }}
      </view>
    </view>
			</uni-forms>
            <view class="button-container">
    <button class="button-item green-button"  @click="commit">提交</button>
    <button class="button-item red-button" @click="reset">重置</button>
  </view>
            <!-- <button type="warn" @click="reset">提交</button>
			<button type="warn" @click="reset">重置</button> -->
			<uni-table ref="table" :loading="loading" border stripe emptyText="暂无更多数据" @selection-change="selectionChange">
			<uni-tr :style="{ background: '#000000' }">
				<uni-th align="center">条码号</uni-th>
				<uni-th align="center">产品名称</uni-th>
			</uni-tr>
			<uni-tr v-for="(item, index) in sonList" :key="index" :data-id="item.ID" 
				:style="{ background: item.toivCount == 0 ? '#ffffff' : '#33f533' }">
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.barcode}}</uni-td>
				<uni-td align="center" :style="{ color: item.toivCount == 0 ? '#606266' : '#ffffff' }">{{ item.productname}}</uni-td>
			</uni-tr>
		</uni-table>
		
		<!-- 遮罩 -->
		<uni-popup ref="loading" type="center" :animation="false" :mask-click="false">遮罩</uni-popup>
		<!-- 消息 -->
		<uni-popup ref="popup"><view class="popup-content"><text class="text" :duration="500">操作成功</text></view></uni-popup>
		<uni-popup ref="popup1"><view class="popup1-content"><text class="text" :duration="500">{{errorMsg}}</text></view></uni-popup>
		
		<!-- <view>
			<input v-model="text" placeholder="输入一段文字" />
			<button @click="speak">播放文本</button>
		</view> -->
	</view>
</template>

<script>
  import { findSpare,findSparebyid,scanBarcode,scanNineBarcode,findSpareListByPlanNum,findqx} from '@/api/work/spares_Painting_Defect.js'
  
import { reactive } from "vue"
	export default {
	  data() {
	    return {
            options: [
        { id: 1, name: '选项一' },
        { id: 2, name: '选项二' },
        { id: 3, name: '选项三' },
      ],
      selectedItems: [], // 保存已选择的选项ID
      selectedItemNames: [], // 保存已选择的选项名称
      showDropdown: false,  // 控制下拉框显示,
			// text: '',
			errorMsg: '',
			formData: {
				id: '',
				proname: '',
				erpnum: '',
				erpremark: '',
				planquantity: '',
				realquanity: '',
				barCode: '',
			},
			formTem1: { barCode: '', erpNum: '', remark: '', num: '', },
			formTem2: {deviceCode: '', assemblyList: [{ barCode: '', erpNum: '', remark: '', num: '', }] },
			mainList: [],
			sonList: [],
			rules: {
				deviceCode: {
					rules:[
						{
							required: true,
							errorMessage: '请扫描器具码',
						}
					],
					validateTrigger:'submit'
				},
				erpNum: {
					rules:[
						{
							required: true,
							errorMessage: '请填写ERP号或者进行扫描条码',
						}
					],
					validateTrigger:'submit'
				},
				actualLoadingQuantity: {
					rules:[
						{
							required: true,
							errorMessage: '请填写数量',
						}
					],
					validateTrigger:'submit'
				},
			}
	    }
	  },
	  created(){
		this.qxList();
	  	this.tableList();
	  },
      computed: {
    // 按钮文本内容
    dropdownButtonText() {
      return this.selectedItems.length === 0 ? '缺陷' : '缺陷';
    },
  },
  watch: {
    // 监听 selectedItems 数组的变化，更新 selectedItemNames
    selectedItems: {
      handler(newVal) {
        this.selectedItemNames = newVal.map(id => {
          const option = this.options.find(opt => opt.id === id);
          return option ? option.name : '';
        });
      },
      immediate: true,
      deep: true
    }
  },
	  methods: {
 // 切换下拉框的显示状态
 toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    // 切换选项的选择和反选状态
    toggleSelection(id) {
      const index = this.selectedItems.indexOf(id);
      if (index > -1) {
        // 如果选项已选中，则取消选择
        this.selectedItems.splice(index, 1);
      } else {
        // 如果选项未选中，则添加选择
        this.selectedItems.push(id);
      }
    },
    // 获取选项名称
    getOptionName(id) {
      const option = this.options.find(opt => opt.id === id);
      return option ? option.name : '';
    },
		  //列表获取
		  tableList(){
			  findSpare().then(r=>{
			  	this.sonList= r.data
			  })
		  },
		  qxList(){
			findqx().then(r=>{
			    this.options =r.data;
				//console(r.data);
			  })
		  },
		  // 条码事件
		     barCodeChange(){
			  this.$refs.loading.open()
			  if(this.formData.barCode.length==0)
			  {
				  	this.errorMsg= '请扫描条码';
					this.speak(this.errorMsg);
					this.msg('popup1');
					return;
			  }
             if(this.formData.barCode.length==9)
			  {
			  scanNineBarcode({'barCode':this.formData.barCode,'PlanErp':this.formData.erpnum,'id':this.formData.id,'defect':this.selectedItemNames}).then(r=>{
				  if(r.data.code == '200'){
                    if(r.data.id=="")
                 {
                  	this.formData.proname = r.data.productname;
                    this.formData.erpnum = r.data.erp;
					// this.formData.realquanity = r.data.realnum;
					// var param = {erp_num: this.formData.erpnum,barcode: this.formData.barCode};
					// this.sonList.push(param);
					this.speak("绑定成功")
					this.msg('popup')
					//this.formData.barCode = "";
					//this.barCodeClick();
                }else
                {

                 this.reset();
                this.barCodeClick();
                this.speak("绑定成功");
			    this.msg('popup');
				this.tableList();

                }

				  } else {
					this.errorMsg= r.data.msg;
					this.speak('绑定失败')
					this.msg('popup1')
                      this.reset();
                    this.barCodeClick();
				  }
			  }).catch(e=>{
				  this.errorMsg= e
				  this.speak(this.errorMsg)
				  this.msg('popup1')
                      this.reset();
                    this.barCodeClick();
			  })
			  }else
			  {
			  scanBarcode({'barCode':this.formData.barCode,'PlanErp':this.formData.erpnum,'id':this.formData.id,'defect':this.selectedItemNames}).then(r=>{
				  if(r.data.code == '200'){
                    if(r.data.id=="")
                 {
                  	this.formData.proname = r.data.productname;
                    this.formData.erpnum = r.data.erp;
					// this.formData.realquanity = r.data.realnum;
					// var param = {erp_num: this.formData.erpnum,barcode: this.formData.barCode};
					// this.sonList.push(param);
					this.speak("绑定成功")
					this.msg('popup')
					//this.formData.barCode = "";
					//this.barCodeClick();
                }else
                {
                      this.reset();
                    this.barCodeClick();
               		 this.speak("绑定成功");
					this.msg('popup');
					this.tableList();

                }

				  } else {
					this.errorMsg= r.data.msg;
					this.speak('绑定失败')
					this.msg('popup1')
                      this.reset();
                    this.barCodeClick();
				  }
			  }).catch(e=>{
				  this.errorMsg= e
				  this.speak(this.errorMsg)
				  this.msg('popup1')
                      this.reset();
                    this.barCodeClick();
			  })
			  }
		  },
		  //条码触发焦点回调事件
		  barCodeFocus(){
			  //border-color: rgb(41, 121, 255);
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = 'rgb(41, 121, 255)';
			  }
		  },
		  //条码离开焦点回调事件
		  barCodeBlur(){
			  //去除边框样式
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = '';
			  }
		  },
		  //重置
		  reset(){
                     this.formData.barCode = "";
	                this.formData.proname = "";
                    this.formData.erpnum = "";
                    this.formData.id="";
				    //this.sonList = [];
                    this.resetDropdown();
		  },
              resetDropdown() {
      this.selectedItems = [];
      this.selectedItemNames = [];
      this.showDropdown = false;
    },
          commit(){
          if (this.formData.barCode === null || this.formData.barCode === undefined || this.formData.barCode === '') {
   	                this.errorMsg= "请扫描产品条码";
					this.speak('请扫描产品条码')
					this.msg('popup1')
                    return;
             }
                if(this.selectedItems.length==0)
                {
                	this.errorMsg= "必须选择缺陷";
					this.speak('必须选择缺陷')
					this.msg('popup1')
                    return;
                }
                    this.formData.id = '1';
	             this.barCodeChange();
		  },
		  //消息提示
		  msg(type){
			this.$refs[type].open('center')
			setTimeout(() => {
				this.$refs[type].close()
			}, 3000);
			this.$refs.loading.close()
		  },
		  //语音提示
		  speak(text) {
			  var music = null;
			  music = uni.createInnerAudioContext(); //创建播放器对象
			  music.src = `../../../static/video/msg/${text.includes("成功") ? 'czcg.wav' : 'czsb.mp3'}`;
			  music.volume = 1;
			  music.play(); //执行播放
			  music.onEnded(() => {
				  //播放结束
				  music = null;
			  });
		  },
		   barCodeClick(){
			document.getElementById("barCodeInputClick").getElementsByTagName('input')[0].focus();
		  },
		  //数组对比
		  arraysAreDifferent(arr11, arr22) {
			const arr1 = [ ...new Set(arr11) ]
			const arr2 = [ ...new Set(arr22) ]
		    return !arr2.every(item => arr1.includes(item))
		  },
	  },
	}
</script>

<style scoped lang="scss">
.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-item {
  flex: 1;
  margin: 0 5px;
  border: none;
  padding: 10px;
  text-align: center;
}

.green-button {
  background-color: green;
  color: white;
}

.red-button {
  background-color: red;
  color: white;
}
.part-container{
	background-color: #ffffff;
}
.bar-code-css{
	height: 35px;
	text-indent: 10px;
	display: flex;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	color: #000;
	font-size: 14px;
}
.bar-code-css .uni-input-placeholder.input-placeholder{
	color: #999;
}
.bar-code-css-view{
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	/* border-color: rgb(41, 121, 255); */
	background-color: rgb(255, 255, 255);
}
.popup-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #09bb07;
	background-color: #e1f3d8;
}
.popup1-content{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #f56c6c;
	background-color: #fde2e2;
}

.view-border{
	margin: 10px;
	padding: 10px;
	border: 1px solid #ccc;
	border-radius: 6px;
}
	
::v-deep .view-border .section-css uni-view.uni-section-header__slot-right{
	width: 70%!important;
}

.container {
  padding: 20px;
}

.dropdown {
  overflow: hidden;
  padding: 10px;
  background-color: #ffffff;
  border: 1px solid #007aff;
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 20px;
}

.dropdown text {
  width: 180px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 10px;
  margin-right: 10px;
}
.dropdown:hover {
  box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.15);
}

.placeholder {
  color: #b0b0b0;
  font-style: italic;
}

.dropdown-options {
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 5px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.option-item {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item text {
  font-size: 16px;
}

.option-item text.selected {
  color: #007aff;
  font-weight: bold;
}


.selected-items {
  margin-top: 10px;
  padding: 10px;
  border: 1px dashed #ddd;
  background-color: #fafafa;
  border-radius: 4px;
}

.selected-item {
  padding: 5px 0;
  font-size: 16px;
  color: #333;
}
</style>