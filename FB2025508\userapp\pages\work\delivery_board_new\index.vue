<!-- 看板发货新 -->
<template>
	<view class="part-container">
		<!-- 搜素 -->
		<uni-card :is-shadow="false" is-full  v-show="listShow" class="zymtest">
			<view style="border: 1px solid #ccc;border-radius: 6px;padding: 10px;">
				<uni-forms ref="form" :modelValue="findFormData" label-width="250">
					<uni-forms-item label="计划发货单号" name="orderNo">
						<uni-easyinput type="text" v-model="findFormData.orderNo" />
					</uni-forms-item>
					<uni-forms-item label="客户订单号" name="customerOrderNo">
						<uni-easyinput type="text" v-model="findFormData.customerOrderNo"  />
					</uni-forms-item>
				</uni-forms>
				<button @click="tableList">搜素</button>
			</view>
		</uni-card>
		<!-- 列表 -->
		<uni-card :is-shadow="false" is-full v-for="item in mainList" v-show="listShow" :key='item.orderNum' class="zymtest">
			<view style="border: 1px solid #ccc;border-radius: 6px;padding: 10px;">
				
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="计划发货单号"><template v-slot:right>{{item.orderNo}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="客户订单号"><template v-slot:right>{{item.customerOrderNo}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="24">
						<uni-section class="mb-10" title="计划发货时间"><template v-slot:right>{{item.planDate}}</template></uni-section>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row">
					<uni-col :span="12">
						<uni-section class="mb-10" title="客户代码"><template v-slot:right>{{item.customerCode}}</template></uni-section>
					</uni-col>
					<uni-col :span="12">
						<uni-section class="mb-10" title="客户名称"><template v-slot:right>{{item.customerName}}</template></uni-section>
					</uni-col>
				</uni-row>
				<button  type="primary" @click="()=>operationClick(item.id)"  
				       :style="{ backgroundColor: item.orderStatus!='已完成' ? '#007aff' : 'limegreen' }">发货</button>
			</view>
		</uni-card>
		

		<!-- 操作信息 -->
		<view v-show="!listShow">
			<!-- 扫码 -->
			<uni-card :is-shadow="false" is-full class="zymtest">
				<view style="border: 1px solid #ccc;border-radius: 6px;padding: 10px;">
					<uni-row class="demo-uni-row">
						<uni-col :span="14">
							<uni-section class="mb-10" title="计划发货单号"><template v-slot:right>{{mainListCheck.orderNo}}</template></uni-section>
						</uni-col>
						<uni-col :span="10">
							<uni-section class="mb-10" title="客户代码"><template v-slot:right>{{mainListCheck.customerCode}}</template></uni-section>
						</uni-col>
					</uni-row>
					<uni-row class="demo-uni-row">
						<uni-col :span="14">
							<uni-section class="mb-10" title="客户订单号"><template v-slot:right>{{mainListCheck.customerOrderNo}}</template></uni-section>
						</uni-col>
						<uni-col :span="10">
							<uni-section class="mb-10" title="客户名称"><template v-slot:right>{{mainListCheck.customerName}}</template></uni-section>
						</uni-col>
					</uni-row>
					<uni-row class="demo-uni-row">
						<uni-col :span="24">
							<uni-section class="mb-10" title="计划发货时间"><template v-slot:right>{{mainListCheck.planDate}}</template></uni-section>
						</uni-col>
					</uni-row>
					<uni-row class="demo-uni-row" v-if="mainListCheck.orderStatus!='已完成'">
						<uni-col :span="24">
							<uni-section class="mb-10" title="条码/ERP号">
								<uni-easyinput
									ref="inputFocus1" 
									class="uni-input bar-code-css1" 
									focus 
									trim="all"
									v-model="barCodeOrErpNum" 
									@focus="barCodeFocus" 
									@blur="barCodeBlur" 
									@confirm="deviceCodeChange('inputFocus1')" />
							</uni-section>
						</uni-col>
					</uni-row>
				</view>
			</uni-card>
			
			<uni-card :is-shadow="false" is-full v-for="item in subList" :key='item._XID' :class="'zymsub zymsub-' + (2 + item.i*1)">
				<view style="border: 1px solid #ccc;border-radius: 6px;padding: 10px;">
					
					<uni-row class="demo-uni-row">
						<uni-col :span="24">
							<uni-section class="mb-10" title="ERP号"><template v-slot:right>{{item.erpNo}}</template></uni-section>
						</uni-col>
					</uni-row>
					<uni-row class="demo-uni-row">
						<uni-col :span="24">
							<uni-section class="mb-10" title="客户零件号"><template v-slot:right>{{item.lu3}}</template></uni-section>
						</uni-col>
					</uni-row>
					<uni-row class="demo-uni-row">
						<uni-col :span="24">
							<uni-section class="mb-10" title="产品描述"><template v-slot:right>{{item.remark}}</template></uni-section>
						</uni-col>
					</uni-row>
					<uni-row class="demo-uni-row">
						<uni-col :span="24">
							<uni-section class="mb-10" title="计划数量"><template v-slot:right>{{item.planNum}}</template></uni-section>
						</uni-col>
					</uni-row>
					<uni-row class="demo-uni-row">
						<uni-col :span="24">
							<uni-section class="mb-10" title="发货数量"><template v-slot:right>
									<uni-easyinput 
										:disabled="item.disabled"
										:ref="'inputFocus' + (2 + item.i*1)" 
										class="uni-input bar-code-css1" 
										focus 
										trim="all"
										v-model="item.actualNum" 
										@focus="barCodeFocus" 
										@blur="barCodeBlur(item.i*1)" /></template>
							</uni-section>
						</uni-col>
					</uni-row>
				</view>
			</uni-card>
			<view>
				<uni-row class="demo-uni-row" style="position: fixed;bottom: 0;left: 0;width: 100%;" v-if="mainListCheck.orderStatus!='已完成'">
					<uni-col :span="8">
						<button @click="ret">返回</button>
					</uni-col>
					<uni-col :span="8">
						<button type="primary" @click="submit">提交</button>
					</uni-col>
					<uni-col :span="8">
						<button type="warn" @click="actualNumSave">暂存</button>
					</uni-col>
				</uni-row>
				<uni-row class="demo-uni-row" style="position: fixed;bottom: 0;left: 0;width: 100%;" v-if="mainListCheck.orderStatus=='已完成'">
					<uni-col :span="24">
						<button @click="ret">返回</button>
					</uni-col>
				</uni-row>
			</view>
		</view>
		
		<!-- 遮罩 -->
		<uni-popup ref="loading" type="center" :animation="false" :mask-click="false">遮罩</uni-popup>
		<!-- 消息 -->
		<uni-popup ref="popup"><view class="popup-content"><text class="text" :duration="1000">上报成功</text></view></uni-popup>
		<uni-popup ref="popup1"><view class="popup1-content"><text class="text" :duration="1000">{{errorMsg}}</text></view></uni-popup>
		<uni-popup ref="popup2"><view class="popup-content"><text class="text" :duration="1000">暂存成功</text></view></uni-popup>
		
		<!-- <view>
			<input v-model="text" placeholder="输入一段文字" />
			<button @click="speak">播放文本</button>
		</view> -->
	</view>
</template>

<script>
  import { findErpNumByBarCode } from '@/api/work/retrun_part.js'
  import { getPlan, planFindAll, saveDeliveryPlan, savePdaSub, planList, getPlannew, findPlannewErpNum, updatePlannew, send } from '@/api/work/delivery_board.js'
  
import { reactive } from "vue"
	export default {
	  data() {
	    return {
			// 条码或者ERP号
			barCodeOrErpNum: "",
			//列表的显示
			listShow: true,
			// text: '',
			errorMsg: '',
			formData: {
				orderNum: '',
				deviceList: [],
			},
			findFormData: {
				orderNo:'',
				customerOrderNo:''
			},
			formTem1: { barCode: '', erpNum: '', remark: '', num: '', },
			formTem2: {deviceCode: '', assemblyList: [{ barCode: '', erpNum: '', remark: '', num: '', }] },
			mainList: [],
			mainListCheck: {},
			subList: [],
	    }
	  },
	  created(){
	  	this.tableList()
	  },
	  methods: {
		  //返回
		  ret(){
			  this.listShow= true
			  this.mainListCheck= {}
			  this.subList= []
		  },
		  //实际发货数量保存
		  actualNumSave(){
			  var arr = this.subList
			  arr.sort((a, b) => a.i - b.i)
			  arr = arr.map(m=>({...m, disabled: true}))
			  var that = this
			  const updateDate = {...this.mainListCheck, sub: JSON.stringify(arr), orderStatus: '进行中'}
			  const mainArr = JSON.parse(JSON.stringify(this.mainList))
			  this.mainList = mainArr.map(m=>{
				  if(m.id = updateDate.id){
					  return updateDate
				  }
				  return m
			  })
			  updatePlannew(updateDate).then(d=>{				  
				that.speak("暂存成功")
				that.msg('popup2')
				//修改背景色
				const elements = document.querySelectorAll(`.zymsub .uni-section-header`);
				if(elements){
					elements.forEach(element => {
						element.style.backgroundColor = '#FFFFFF';
					})
				}
				//触发选中
				setTimeout(function () {
					that.deviceCodeClick()
				}, 200);
				
				that.subList = JSON.parse(JSON.stringify(arr))
				console.log(JSON.parse(JSON.stringify(arr)))
			  })
		  },
		  yyyymmdd(){
			  var date = new Date();
			  var year = date.getFullYear();  // 年份
			  var month = date.getMonth() + 1;  // 月份，返回值为0-11，所以需要加1
			  var day = date.getDate();  // 日期
			   
			  // 对月份和日期进行补零
			  month = month < 10 ? '0' + month : month.toString();
			  day = day < 10 ? '0' + day : day.toString();
			   
			  var currentDate = year +'-'+ month +'-'+ day;
			  console.log(currentDate);
			  return currentDate
		  },
		  //列表获取
		  tableList(){
			  planList({...this.findFormData, planDate: this.yyyymmdd()}).then(r=>{
			  	this.mainList= r.rows
			  })
		  },
		  
		  //操作的点击事件
		  operationClick(id){
			this.listShow= false
			getPlannew(id).then(r=>{
				this.mainListCheck= { ...r.data }
				if(r.data.sub){
					this.subList = JSON.parse(r.data.sub).map(m=>({...m, disabled: true}))
				}
			})
		  },
		  // 条码事件
		  deviceCodeChange(id){
			  this.barCodeClick(id);
		  },
		  //触发条码焦点事件
		  deviceCodeClick(){
			const e = document.getElementById("inputFocus1")
			if(e){
				e.getElementsByTagName('input')[0].focus()
			}
		  },
		  //触发条码焦点事件
		  barCodeClick(id){
			const thisbarCodeOrErpNum = this.barCodeOrErpNum
			if(!this.barCodeOrErpNum){
				this.errorMsg= '输入信息不能为空！'
				this.speak(this.errorMsg)
				this.msg('popup1')
				return
			}
			var that = this
			findPlannewErpNum({"customerCode":this.barCodeOrErpNum}).then(d=>{
				var arr = JSON.parse(JSON.stringify(this.subList))
				const index = arr.findIndex(el => el.erpNo == d);
				const item = arr.find(el => el.erpNo == d);
				if (index > -1) {
					arr.splice(index, 1); // 移除元素
					arr.unshift(item); // 将元素添加到数组开头
					arr[0].disabled = false
					//修改背景色
					const elements = document.querySelectorAll(`.zymsub-${item.i + 2} .uni-section-header`);
					if(elements){
						elements.forEach(element => {
							element.style.backgroundColor = '#74ff02';
						})
					}
					//触发选中
					setTimeout(function () {
						const e = document.getElementById(`inputFocus-${item.i + 2}`)
						if(e){
							console.log(e.getElementsByTagName('input')[0])
							e.getElementsByTagName('input')[0].focus()
						}
					}, 200);
				}else{
					//如果没有查询都爱数据，进行重新排序
					arr.sort((a, b) => a.i - b.i)
					arr = arr.map(m=>({...m, disabled: true}))
					//修改背景色
					const elements = document.querySelectorAll(`.zymsub .uni-section-header`);
					if(elements){
						elements.forEach(element => {
							element.style.backgroundColor = '#FFFFFF';
						})
					}
					//触发选中
					setTimeout(function () {
						that.errorMsg= `条码/ERP号【${thisbarCodeOrErpNum}】,未查询到数据！`
						that.speak(that.errorMsg)
						that.msg('popup1')
						that.deviceCodeClick()
					}, 200);
				}
				this.subList = JSON.parse(JSON.stringify(arr))
				console.log(JSON.parse(JSON.stringify(arr)))
				
				this.barCodeOrErpNum = ''
			})
			
		  },
		  //条码触发焦点回调事件
		  barCodeFocus(){
			  //border-color: rgb(41, 121, 255);
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = 'rgb(41, 121, 255)';
			  }
		  },
		  //条码离开焦点回调事件
		  barCodeBlur(i){
			  //去除边框样式
			  if(document.querySelector('.bar-code-css-view')){
				document.querySelector('.bar-code-css-view').style.borderColor = '';
			  }
			  const arr = this.subList
			  const item = arr.find(el => el.i == i);
			  if(item && item.actualNum*1 > item.planNum*1){
				  	this.errorMsg= `ERP号【${item.erpNo}】,发货数量不能大于计划数据量！`
				  	this.speak(this.errorMsg)
					item.actualNum= 0
				  	this.msg('popup1')
			  }else if(item.actualNum*1 < 0){
					this.errorMsg= `ERP号【${item.erpNo}】,发货数量不能小于0！`
					this.speak(this.errorMsg)
					this.msg('popup1')
					item.actualNum= 0
			  }
		  },
		  //上报事件
		  submit(){
			  var arr = this.subList
			  arr.sort((a, b) => a.i - b.i)
			  arr = arr.map(m=>({...m, disabled: true}))
			  const updateDate = {...this.mainListCheck, sub: JSON.stringify(arr)}
			  const mainArr = JSON.parse(JSON.stringify(this.mainList))
			  this.mainList = mainArr.map(m=>{
				  if(m.id == updateDate.id){
					  return updateDate
				  }
				  return m
			  })
			  
			  
			  // this.$refs.loading.open()
			  //判断发货数量是否为空
			  var sendNum = 0
			  for (var i = 0; i < arr.length; i++) {
				  const item = arr[i]
				  sendNum = sendNum + item.actualNum*1
				  console.log("item", item.actualNum*1 < 0)
				  if(item && item.actualNum*1 > item.planNum*1){
						this.errorMsg= `ERP号【${item.erpNo}】,发货数量不能大于计划数据量！`
						this.speak(this.errorMsg)
						this.msg('popup1')
						return
				  }else if(item.actualNum*1 < 0){
						this.errorMsg= `ERP号【${item.erpNo}】,发货数量不能小于0！`
						this.speak(this.errorMsg)
						this.msg('popup1')
						return
				  }
			  }
			  if(sendNum == 0){
				this.errorMsg= `发货单数量不能都是0！`
				this.speak(this.errorMsg)
				this.msg('popup1')
				return
			  }
			  
			  // //发货
			  send({...updateDate}).then(d=>{
				  //修改背景色
				  const elements = document.querySelectorAll(`.zymsub .uni-section-header`);
				  if(elements){
					elements.forEach(element => {
						element.style.backgroundColor = '#FFFFFF';
					})
				  }
				  //返回主列表
				  this.ret()
				  this.tableList()
			  })
			  
		  },
		  //重置
		  reset(){
			  this.formData.deviceList = []
			  this.formData= JSON.parse(JSON.stringify(this.formData))
			  this.tableList()
			  this.listShow= true
			  this.deviceCodeClick();
		  },
		  //消息提示
		  msg(type){
			this.$refs[type].open('center')
			setTimeout(() => {
				this.$refs[type].close()
			}, 3000);
			this.$refs.loading.close()
		  },
		  //语音提示
		  speak(text) {
			  if(!text){
				return
			  }
			  var music = null;
			  music = uni.createInnerAudioContext(); //创建播放器对象
			  music.src = `../../../static/video/msg/${text.includes("成功") ? 'czcg.wav' : 'czsb.mp3'}`;
			  music.volume = 1;
			  music.play(); //执行播放
			  music.onEnded(() => {
				  //播放结束
				  music = null;
			  });
		  },
		  //数组对比
		  arraysAreDifferent(arr11, arr22) {
			const arr1 = [ ...new Set(arr11) ]
			const arr2 = [ ...new Set(arr22) ]
		    return !arr2.every(item => arr1.includes(item))
		  },
		  //焦点事件
		  focusClick(id){
			const e = document.getElementById(id)
			console.log(1111, e)
			if(e){
				e.getElementsByTagName('input')[0].focus()
			}
		  },
		}
	}
</script>

<style scoped lang="scss">
.part-container{
	background-color: #ffffff;
}
.bar-code-css{
	height: 35px;
	text-indent: 10px;
	display: flex;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	color: #000;
	font-size: 14px;
}
.bar-code-css .uni-input-placeholder.input-placeholder{
	color: #999;
}
.bar-code-css-view{
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	/* border-color: rgb(41, 121, 255); */
	background-color: rgb(255, 255, 255);
}
.popup-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #09bb07;
	background-color: #e1f3d8;
}
.popup1-content{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 15px;
	height: 50px;
	color: #f56c6c;
	background-color: #fde2e2;
}

.view-border{
	margin: 10px;
	padding: 10px;
	border: 1px solid #ccc;
	border-radius: 6px;
}
	
::v-deep .view-border .section-css uni-view.uni-section-header__slot-right{
	width: 70%!important;
}

::v-deep .zymtest .uni-section .uni-section-header {
    padding: 2px 10px!important;
}
</style>