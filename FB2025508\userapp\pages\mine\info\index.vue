<template>
  <view class="container">
    <!-- Header with store information -->
    <view class="header">
      <view class="back-icon" @click="goBack">
        <text class="iconfont icon-left"></text>
      </view>
      <view class="title">个人资料</view>
    </view>

    <!-- Store information section with auto line wrapping -->
    <view class="shop-info" v-if="personalInfo.shopInfo">
      <text class="shop-name">{{ personalInfo.shopInfo.shopName }}</text>
    </view>

    <!-- Personal info form -->
    <view class="form-container">
      <!-- 修改昵称为可编辑状态 -->
      <view class="form-item editable">
        <text class="label">
          <text class="required">*</text>昵称
        </text>
        <input 
          class="input-value" 
          type="text" 
          v-model="personalInfo.nickname" 
          placeholder="请输入昵称" 
        />
      </view>

      <!-- 修改手机号为可编辑状态 -->
      <view class="form-item editable">
        <text class="label">
          <text class="required">*</text>手机号
        </text>
        <input 
          class="input-value" 
          type="number" 
          v-model="personalInfo.phone" 
          placeholder="请输入手机号" 
          maxlength="11"
          @blur="validatePhoneNumber"
        />
      </view>

      <view class="separator">以下为非必填项</view>

      <!-- Direct input for name -->
      <view class="form-item editable">
        <text class="label">
          <text class="required">*</text>姓名
        </text>
        <input 
          class="input-value" 
          type="text" 
          v-model="personalInfo.staff_name" 
          placeholder="请输入姓名" 
        />
      </view>
      
      <!-- Gender as selectable buttons -->
      <view class="form-item">
        <text class="label">性别</text>
        <view class="gender-options">
          <view 
            class="gender-option" 
            :class="{ active: personalInfo.gender === '0' }" 
            @click="setGender('0')"
          >
            男
          </view>
          <view 
            class="gender-option" 
            :class="{ active: personalInfo.gender === '1' }" 
            @click="setGender('1')"
          >
            女
          </view>
        </view>
      </view>

      <!-- Birth date with native date picker -->
      <view class="form-item">
        <text class="label">
          <text class="required">*</text>出生年月
        </text>
        <picker 
          mode="date" 
          :value="personalInfo.birthDate" 
          start="1900-01-01" 
          :end="currentDate" 
          @change="onDatePickerChange"
        >
          <view class="picker-value">
            {{ personalInfo.birthDate || '点击选择出生年月' }}
          </view>
        </picker>
        <text class="arrow iconfont icon-right"></text>
      </view>

      <!-- Direct input for ID Card -->
      <view class="form-item editable">
        <text class="label">
          <text class="required">*</text>身份证号
        </text>
        <input 
          class="input-value" 
          type="idcard" 
          v-model="personalInfo.id_card" 
          placeholder="请输入身份证号" 
          @blur="validateIdCardInput"
        />
      </view>

      <!-- Province-City-District Cascading Selection -->
      <view class="form-item" @click="showRegionPicker">
        <text class="label">
          <text class="required">*</text>所在区域
        </text>
        <text class="value">{{ displayRegion }}</text>
        <text class="arrow iconfont icon-right"></text>
      </view>

      <!-- For users, show user code field -->
      <view class="form-item" v-if="isNormalUser">
        <text class="label">用户码</text>
        <text class="value">{{ personalInfo.userCode || '' }}</text>
        <text class="arrow iconfont icon-right"></text>
      </view>
    </view>

    <!-- Save button -->
    <view class="save-btn-container">
      <button class="save-btn" @click="savePersonalInfo">保存</button>
    </view>

    <!-- Three-level Region Picker -->
    <uni-popup ref="regionPop" type="bottom">
      <view class="picker-container">
        <view class="picker-header">
          <text class="cancel" @click="closeRegionPicker">取消</text>
          <text class="confirm" @click="confirmRegion">确定</text>
        </view>
        <picker-view :indicator-style="indicatorStyle" style="height: 240px;" @change="onRegionChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in provinceList" :key="index">
              {{ item.regioninfo_name }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in cityList" :key="index">
              {{ item.regioninfo_name }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in districtList" :key="index">
              {{ item.regioninfo_name }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getStaffPersonalInfo, updateStaffPersonalInfo, getRegionInfoList } from '@/api/work/retrun_part.js'
import { validateIdCard } from '@/utils/validate.js'

export default {
  data() {
    return {
      personalInfo: {
        staff_name: '',
        gender: '0',
        id_card: '',
        birthDate: '',
        phone: '',
        nickname: '',
        region_id: null,
      },
      isNormalUser: false,
      currentDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
      
      // 区域选择相关
      allRegionData: {
        provinces: [],
        cities: [],
        districts: []
      },
      provinceList: [],
      cityList: [],
      districtList: [],
      selectedIndices: [0, 0, 0],  // [省索引, 市索引, 区索引]
      selectedRegion: {
        province: null,
        city: null,
        district: null
      },
      
      indicatorStyle: 'height: 40px;'
    }
  },
  computed: {
    displayRegion() {
      // 如果有已选择的区域
      if (this.selectedRegion.district) {
        return this.selectedRegion.province.regioninfo_name + ' ' + 
               this.selectedRegion.city.regioninfo_name + ' ' + 
               this.selectedRegion.district.regioninfo_name;
      }
      
      // 从personalInfo中获取区域数据
      if (this.personalInfo.province_name && this.personalInfo.city_name && this.personalInfo.district_name) {
        return this.personalInfo.province_name + ' ' + 
               this.personalInfo.city_name + ' ' + 
               this.personalInfo.district_name;
      }
      
      return '点击选择省市区';
    }
  },
  onLoad() {
    this.loadRegionData();
    this.loadPersonalInfo();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    // 加载区域数据
    loadRegionData() {
      uni.showLoading({
        title: '加载中...'
      });
      
      getRegionInfoList().then(res => {
        uni.hideLoading();
        
        if (res.code === 200 && res.data) {
          this.allRegionData = res.data;
          
          // 初始化省列表
          this.provinceList = this.allRegionData.provinces || [];
          
          // 初始化市列表
          this.updateCityList(0);
          
          // 初始化区县列表
          this.updateDistrictList(0);
        } else {
          uni.showToast({
            title: '获取区域数据失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.hideLoading();
        console.error('获取区域数据异常:', err);
        uni.showToast({
          title: '获取区域数据失败',
          icon: 'none'
        });
      });
    },
    
    // 根据选中的省更新市列表
    updateCityList(provinceIndex) {
      if (!this.provinceList || this.provinceList.length === 0) {
        this.cityList = [];
        this.districtList = [];
        return;
      }
      
      const selectedProvince = this.provinceList[provinceIndex];
      if (!selectedProvince) {
        this.cityList = [];
        this.districtList = [];
        return;
      }
      
      // 根据省的ID筛选获取其下的市列表
      this.cityList = (this.allRegionData.cities || []).filter(city => 
        city.parentinfo_id === selectedProvince.regioninfo_id
      );
      
      // 更新区县列表
      this.updateDistrictList(0);
    },
    
    // 根据选中的市更新区县列表
    updateDistrictList(cityIndex) {
      if (!this.cityList || this.cityList.length === 0) {
        this.districtList = [];
        return;
      }
      
      const selectedCity = this.cityList[cityIndex];
      if (!selectedCity) {
        this.districtList = [];
        return;
      }
      
      // 根据市的ID筛选获取其下的区县列表
      this.districtList = (this.allRegionData.districts || []).filter(district => 
        district.parentinfo_id === selectedCity.regioninfo_id
      );
    },
    
    // 设置初始区域选择
    setInitialRegionSelection() {
      if (!this.personalInfo.province_id || !this.personalInfo.city_id || !this.personalInfo.district_id) {
        return;
      }
      
      // 查找省份索引
      const provinceIndex = this.provinceList.findIndex(
        p => p.regioninfo_id === this.personalInfo.province_id
      );
      
      if (provinceIndex !== -1) {
        this.selectedIndices[0] = provinceIndex;
        this.updateCityList(provinceIndex);
        
        // 查找城市索引
        const cityIndex = this.cityList.findIndex(
          c => c.regioninfo_id === this.personalInfo.city_id
        );
        
        if (cityIndex !== -1) {
          this.selectedIndices[1] = cityIndex;
          this.updateDistrictList(cityIndex);
          
          // 查找区县索引
          const districtIndex = this.districtList.findIndex(
            d => d.regioninfo_id === this.personalInfo.district_id
          );
          
          if (districtIndex !== -1) {
            this.selectedIndices[2] = districtIndex;
            
            // 设置已选择的区域对象
            this.selectedRegion = {
              province: this.provinceList[provinceIndex],
              city: this.cityList[cityIndex],
              district: this.districtList[districtIndex]
            };
          }
        }
      }
    },
    
    // Direct gender selection
    setGender(gender) {
      this.personalInfo.gender = gender;
    },
    
    // Handle date picker change
    onDatePickerChange(e) {
      this.personalInfo.birthDate = e.detail.value;
    },
    
    // 验证手机号
    validatePhoneNumber() {
      if (this.personalInfo.phone && !/^1[3-9]\d{9}$/.test(this.personalInfo.phone)) {
        uni.showToast({
          title: '请输入有效的手机号',
          icon: 'none'
        });
      }
    },
    
    // Validate ID card on blur
    validateIdCardInput() {
      if (this.personalInfo.id_card && !validateIdCard(this.personalInfo.id_card)) {
        uni.showToast({
          title: '请输入有效的身份证号',
          icon: 'none'
        });
      }
    },
    
    // 加载个人信息
    loadPersonalInfo() {
      getStaffPersonalInfo().then(res => {
        if (res.code === 200 && res.data) {
          // Debug log to see what data is returned
          console.log('Received personal info:', res.data);
          
          // Properly assign data to personalInfo object
          this.personalInfo = {
            ...this.personalInfo, // Keep default values
            ...res.data, // Override with data from API
          };
          
          // Ensure birthDate is properly formatted
          if (res.data.birth_time && !this.personalInfo.birthDate) {
            // Convert date object or string to YYYY-MM-DD format
            const birthTime = new Date(res.data.birth_time);
            if (!isNaN(birthTime.getTime())) { // Check if valid date
              this.personalInfo.birthDate = birthTime.toISOString().split('T')[0];
            }
          }
          
          // Make sure region_id, province_id, city_id, and district_id are properly set
          if (res.data.district_id) {
            this.personalInfo.region_id = res.data.district_id;
            this.personalInfo.district_id = res.data.district_id;
            this.personalInfo.district_name = res.data.district_name;
          }
          
          if (res.data.city_id) {
            this.personalInfo.city_id = res.data.city_id;
            this.personalInfo.city_name = res.data.city_name;
          }
          
          if (res.data.province_id) {
            this.personalInfo.province_id = res.data.province_id;
            this.personalInfo.province_name = res.data.province_name;
          }
          
          // 设置区域选择器初始值
          if (this.provinceList.length > 0) {
            this.setInitialRegionSelection();
          }
          
          console.log('Updated personalInfo:', this.personalInfo);
        } else {
          uni.showToast({
            title: '获取个人信息失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('获取个人信息异常:', err);
        uni.showToast({
          title: '获取个人信息异常',
          icon: 'none'
        });
      });
    },
    
    // Region picker methods
    showRegionPicker() {
      this.$refs.regionPop.open();
    },
    
    onRegionChange(e) {
      const values = e.detail.value;
      const [provinceIndex, cityIndex, districtIndex] = values;
      
      // 如果省份变更，更新市和区县
      if (provinceIndex !== this.selectedIndices[0]) {
        this.updateCityList(provinceIndex);
        values[1] = 0; // 重置市索引
        values[2] = 0; // 重置区县索引
      } 
      // 如果市变更，更新区县
      else if (cityIndex !== this.selectedIndices[1]) {
        this.updateDistrictList(cityIndex);
        values[2] = 0; // 重置区县索引
      }
      
      this.selectedIndices = values;
    },
    
    confirmRegion() {
      const [provinceIndex, cityIndex, districtIndex] = this.selectedIndices;
      
      const selectedProvince = this.provinceList[provinceIndex];
      const selectedCity = this.cityList[cityIndex];
      const selectedDistrict = this.districtList[districtIndex];
      
      if (!selectedProvince || !selectedCity || !selectedDistrict) {
        uni.showToast({
          title: '请选择完整的省市区',
          icon: 'none'
        });
        return;
      }
      
      // 更新选中的区域
      this.selectedRegion = {
        province: selectedProvince,
        city: selectedCity,
        district: selectedDistrict
      };
      
      // 更新personalInfo中的区域数据
      this.personalInfo.province_id = selectedProvince.regioninfo_id;
      this.personalInfo.province_name = selectedProvince.regioninfo_name;
      this.personalInfo.city_id = selectedCity.regioninfo_id;
      this.personalInfo.city_name = selectedCity.regioninfo_name;
      this.personalInfo.district_id = selectedDistrict.regioninfo_id;
      this.personalInfo.district_name = selectedDistrict.regioninfo_name;
      this.personalInfo.region_id = selectedDistrict.regioninfo_id;  // 区域ID存储区县ID
      
      this.$refs.regionPop.close();
    },
    
    closeRegionPicker() {
      this.$refs.regionPop.close();
    },
    
    // 保存个人信息
    savePersonalInfo() {
      // 验证必填项
      if (!this.personalInfo.nickname) {
        uni.showToast({
          title: '请填写昵称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.personalInfo.phone) {
        uni.showToast({
          title: '请填写手机号',
          icon: 'none'
        });
        return;
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.personalInfo.phone)) {
        uni.showToast({
          title: '请输入有效的手机号',
          icon: 'none'
        });
        return;
      }
      
      if (!this.personalInfo.staff_name) {
        uni.showToast({
          title: '请填写姓名',
          icon: 'none'
        });
        return;
      }
      
      if (!this.personalInfo.birthDate) {
        uni.showToast({
          title: '请选择出生年月',
          icon: 'none'
        });
        return;
      }
      
      if (!this.personalInfo.id_card) {
        uni.showToast({
          title: '请填写身份证号',
          icon: 'none'
        });
        return;
      }
      
      if (!validateIdCard(this.personalInfo.id_card)) {
        uni.showToast({
          title: '请输入有效的身份证号',
          icon: 'none'
        });
        return;
      }
      
      if (!this.personalInfo.region_id) {
        uni.showToast({
          title: '请选择所在区域',
          icon: 'none'
        });
        return;
      }
      
      // 构建保存数据
      const saveData = {
        staffId: this.personalInfo.staff_id,
        staffName: this.personalInfo.staff_name,
        gender: this.personalInfo.gender,
        idCard: this.personalInfo.id_card,
        birthDate: this.personalInfo.birthDate,
        regionId: this.personalInfo.region_id,  // 使用区县ID作为区域ID
        phone: this.personalInfo.phone,         // 添加手机号
        nickname: this.personalInfo.nickname    // 添加昵称
      };
      
      // 调用API保存数据
      uni.showLoading({
        title: '保存中...'
      });
      
      updateStaffPersonalInfo(saveData).then(res => {
        uni.hideLoading();
        if (res.code === 200) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: res.msg || '保存失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.hideLoading();
        console.error('保存个人信息异常:', err);
        uni.showToast({
          title: '保存个人信息异常',
          icon: 'none'
        });
      });
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.back-icon {
  position: absolute;
  left: 30rpx;
  font-size: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.shop-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.shop-name {
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  max-width: 90%;
  word-wrap: break-word;
  line-height: 1.4;
}

.form-container {
  background-color: #ffffff;
  padding: 0 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eeeeee;
}

.form-item.editable {
  align-items: center;
}

.label {
  width: 180rpx;
  font-size: 32rpx;
  color: #333333;
}

.required {
  color: #ff4d4f;
  margin-right: 5rpx;
}

.value {
  flex: 1;
  font-size: 32rpx;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
}

.input-value {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
  height: 40rpx;
  line-height: 40rpx;
}

.picker-value {
  flex: 1;
  font-size: 32rpx;
  color: #666666;
}

.gender-options {
  display: flex;
  flex: 1;
}

.gender-option {
  padding: 10rpx 40rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
  color: #666666;
}

.gender-option.active {
  background-color: #ff6b00;
  color: #ffffff;
}

.arrow {
  font-size: 32rpx;
  color: #999999;
  margin-left: 10rpx;
}

.separator {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #999999;
}

.save-btn-container {
  padding: 60rpx 30rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #ff6b00;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 44rpx;
  text-align: center;
}

/* 选择器样式 */
.picker-container {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.cancel {
  color: #999999;
  font-size: 32rpx;
}

.confirm {
  color: #ff6b00;
  font-size: 32rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 32rpx;
}
</style>

