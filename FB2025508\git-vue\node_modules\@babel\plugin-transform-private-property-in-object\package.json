{"_from": "@babel/plugin-transform-private-property-in-object@^7.23.4", "_id": "@babel/plugin-transform-private-property-in-object@7.23.4", "_inBundle": false, "_integrity": "sha512-9G3K1YqTq3F4Vt88Djx1UZ79PDyj+yKRnUy7cZGSMe+a7jkwD259uKKuUzQlPkGam7R+8RJwh5z4xO27fA1o2A==", "_location": "/@babel/plugin-transform-private-property-in-object", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-private-property-in-object@^7.23.4", "name": "@babel/plugin-transform-private-property-in-object", "escapedName": "@babel%2fplugin-transform-private-property-in-object", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.23.4.tgz", "_shasum": "3ec711d05d6608fd173d9b8de39872d8dbf68bf5", "_spec": "@babel/plugin-transform-private-property-in-object@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "deprecated": false, "description": "This plugin transforms checks for a private property in an object", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-private-property-in-object", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "type": "commonjs", "version": "7.23.4"}