## 1.1.2（2023-04-11）
- 修复 更改 modelValue 报错的 bug
- 修复 v-for 未使用 key 值控制台 warning
## 1.1.1（2023-02-21）
- 修复代码合并时引发 value 属性为空时不渲染数据的问题
## 1.1.0（2023-02-15）
- 修复 localdata 不支持动态更新的bug
## 1.0.9（2023-02-15）
- 修复 localdata 不支持动态更新的bug
## 1.0.8（2022-09-16）
- 可以使用 uni-scss 控制主题色
## 1.0.7（2022-07-06）
- 优化 pc端图标位置不正确的问题
## 1.0.6（2022-07-05）
- 优化 显示样式
## 1.0.5（2022-07-04）
- 修复 uni-data-picker 在 uni-forms-item 中宽度不正确的bug
## 1.0.4（2022-04-19）
- 修复 字节小程序 本地数据无法选择下一级的Bug
## 1.0.3（2022-02-25）
- 修复 nvue 不支持的 v-show 的 bug
## 1.0.2（2022-02-25）
- 修复 条件编译 nvue 不支持的 css 样式
## 1.0.1（2021-11-23）
- 修复 由上个版本引发的map、v-model等属性不生效的bug
## 1.0.0（2021-11-19）
- 优化 组件 UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-data-picker](https://uniapp.dcloud.io/component/uniui/uni-data-picker)
## 0.4.9（2021-10-28）
- 修复 VUE2 v-model 概率无效的 bug
## 0.4.8（2021-10-27）
- 修复 v-model 概率无效的 bug
## 0.4.7（2021-10-25）
- 新增 属性 spaceInfo 服务空间配置 HBuilderX 3.2.11+
- 修复 树型 uniCloud 数据类型为 int 时报错的 bug
## 0.4.6（2021-10-19）
- 修复 非 VUE3 v-model 为 0 时无法选中的 bug
## 0.4.5（2021-09-26）
- 新增 清除已选项的功能（通过 clearIcon 属性配置是否显示按钮），同时提供 clear 方法以供调用，二者等效
- 修复 readonly 为 true 时报错的 bug
## 0.4.4（2021-09-26）
- 修复 上一版本造成的 map 属性失效的 bug
- 新增 ellipsis 属性，支持配置 tab 选项长度过长时是否自动省略
## 0.4.3（2021-09-24）
- 修复 某些情况下级联未触发的 bug
## 0.4.2（2021-09-23）
- 新增 提供 show 和 hide 方法，开发者可以通过 ref 调用
- 新增 选项内容过长自动添加省略号
## 0.4.1（2021-09-15）
- 新增 map 属性 字段映射，将 text/value 映射到数据中的其他字段
## 0.4.0（2021-07-13）
- 组件兼容 vue3，如何创建 vue3 项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 0.3.5（2021-06-04）
- 修复 无法加载云端数据的问题
## 0.3.4（2021-05-28）
- 修复 v-model 无效问题
- 修复 loaddata 为空数据组时加载时间过长问题
- 修复 上个版本引出的本地数据无法选择带有 children 的 2 级节点
## 0.3.3（2021-05-12）
- 新增 组件示例地址
## 0.3.2（2021-04-22）
- 修复 非树形数据有 where 属性查询报错的问题
## 0.3.1（2021-04-15）
- 修复 本地数据概率无法回显时问题
## 0.3.0（2021-04-07）
- 新增 支持云端非树形表结构数据
- 修复 根节点 parent_field 字段等于 null 时选择界面错乱问题
## 0.2.0（2021-03-15）
- 修复 nodeclick、popupopened、popupclosed 事件无法触发的问题
## 0.1.9（2021-03-09）
- 修复 微信小程序某些情况下无法选择的问题
## 0.1.8（2021-02-05）
- 优化 部分样式在 nvue 上的兼容表现
## 0.1.7（2021-02-05）
- 调整为 uni_modules 目录规范
