{"_from": "@babel/compat-data@^7.23.5", "_id": "@babel/compat-data@7.23.5", "_inBundle": false, "_integrity": "sha512-uU27kfDRlhfKl+w1U6vp16IuvSLtjAxdArVXPa9BvLkrr7CYIsxH5adpHObeAGY/41+syctUWOZ140a2Rvkgjw==", "_location": "/@babel/compat-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/compat-data@^7.23.5", "name": "@babel/compat-data", "escapedName": "@babel%2fcompat-data", "scope": "@babel", "rawSpec": "^7.23.5", "saveSpec": null, "fetchSpec": "^7.23.5"}, "_requiredBy": ["/@babel/helper-compilation-targets", "/@babel/plugin-transform-object-rest-spread", "/@babel/preset-env", "/babel-plugin-polyfill-corejs2"], "_resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.23.5.tgz", "_shasum": "ffb878728bb6bdcb6f4510aa51b1be9afb8cfd98", "_spec": "@babel/compat-data@^7.23.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-compilation-targets", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": ">", "devDependencies": {"@mdn/browser-compat-data": "^5.3.0", "core-js-compat": "^3.31.0", "electron-to-chromium": "^1.4.441"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js", "./overlapping-plugins": "./overlapping-plugins.js", "./plugin-bugfixes": "./plugin-bugfixes.js"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "compat-table", "compat-data"], "license": "MIT", "name": "@babel/compat-data", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "type": "commonjs", "version": "7.23.5"}