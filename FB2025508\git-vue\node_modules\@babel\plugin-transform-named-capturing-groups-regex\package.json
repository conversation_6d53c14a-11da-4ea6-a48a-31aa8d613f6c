{"_from": "@babel/plugin-transform-named-capturing-groups-regex@^7.22.5", "_id": "@babel/plugin-transform-named-capturing-groups-regex@7.22.5", "_inBundle": false, "_integrity": "sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==", "_location": "/@babel/plugin-transform-named-capturing-groups-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-named-capturing-groups-regex@^7.22.5", "name": "@babel/plugin-transform-named-capturing-groups-regex", "escapedName": "@babel%2fplugin-transform-named-capturing-groups-regex", "scope": "@babel", "rawSpec": "^7.22.5", "saveSpec": null, "fetchSpec": "^7.22.5"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.5.tgz", "_shasum": "67fe18ee8ce02d57c855185e27e3dc959b2e991f", "_spec": "@babel/plugin-transform-named-capturing-groups-regex@^7.22.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Compile regular expressions using named groups to ES5.", "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "core-js": "^3.30.2"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-named-capturing-groups-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-named-capturing-groups-regex", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-named-capturing-groups-regex"}, "type": "commonjs", "version": "7.22.5"}