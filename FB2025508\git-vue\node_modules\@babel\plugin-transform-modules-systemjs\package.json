{"_from": "@babel/plugin-transform-modules-systemjs@^7.23.9", "_id": "@babel/plugin-transform-modules-systemjs@7.23.9", "_inBundle": false, "_integrity": "sha512-KDlPRM6sLo4o1FkiSlXoAa8edLXFsKKIda779fbLrvmeuc3itnjCtaO6RrtoaANsIJANj+Vk1zqbZIMhkCAHVw==", "_location": "/@babel/plugin-transform-modules-systemjs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-modules-systemjs@^7.23.9", "name": "@babel/plugin-transform-modules-systemjs", "escapedName": "@babel%2fplugin-transform-modules-systemjs", "scope": "@babel", "rawSpec": "^7.23.9", "saveSpec": null, "fetchSpec": "^7.23.9"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.23.9.tgz", "_shasum": "105d3ed46e4a21d257f83a2f9e2ee4203ceda6be", "_spec": "@babel/plugin-transform-modules-systemjs@^7.23.9", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.23.3", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.20"}, "deprecated": false, "description": "This plugin transforms ES2015 modules to SystemJS", "devDependencies": {"@babel/core": "^7.23.9", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/helper-transform-fixture-test-runner": "^7.23.9", "core-js": "^3.35.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-modules-systemjs", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "type": "commonjs", "version": "7.23.9"}