<template>
  <view class="normal-login-container">
    <view class="logo-content align-center justify-center flex">
      <image style="width: 100rpx;height: 100rpx;" :src="globalConfig.appInfo.logo" mode="widthFix">
      </image>
      <text class="title">完美婚介店员系统</text>
    </view>
    <view class="login-form-content">
      <view class="input-item flex align-center">
        <view class="iconfont icon-user icon"></view>
        <input v-model="loginForm.username" class="input" type="text" placeholder="请输入账号" maxlength="30" />
      </view>
      <view class="input-item flex align-center">
        <view class="iconfont icon-password icon"></view>
        <input v-model="loginForm.password" type="password" class="input" placeholder="请输入密码" maxlength="20" />
      </view>
      <!-- 记住密码选项 -->
      <view class="remember-box flex align-center">
        <view class="custom-checkbox" @click="toggleRemember">
          <view class="checkbox-icon" :class="{'checkbox-icon-checked': rememberPassword}">
            <view v-if="rememberPassword" class="checkbox-inner"></view>
          </view>
          <text class="remember-text">记住用户名和密码</text>
        </view>
      </view>
      <!-- <view class="input-item flex align-center" style="width: 60%;margin: 0px;" v-if="captchaEnabled">
        <view class="iconfont icon-code icon"></view>
        <input v-model="loginForm.code" type="number" class="input" placeholder="请输入验证码" maxlength="4" />
        <view class="login-code"> 
          <image :src="codeUrl" @click="getCode" class="login-code-img"></image>
        </view>
      </view> -->
      <view class="action-btn">
        <button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round">登录</button>
      </view>
      <!-- <view class="reg text-center" v-if="register">
        <text class="text-grey1">没有账号？</text>
        <text @click="handleUserRegister" class="text-blue">立即注册</text>
      </view> -->
      <!-- <view class="xieyi text-center">
        <text class="text-grey1">登录即代表同意</text>
        <text @click="handleUserAgrement" class="text-blue">《用户协议》</text>
        <text @click="handlePrivacy" class="text-blue">《隐私协议》</text>
      </view> -->
    </view>
     
  </view>
</template>

<script>
  import { getCodeImg } from '@/api/login'

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: true,
        // 用户注册开关
        register: false,
        // 记住密码选项
        rememberPassword: false,
        globalConfig: getApp().globalData.config,
        loginForm: {
          username: "",
          password: "",
          code: "",
          uuid: ''
        }
      }
    },
    created() {
      // 加载记住的用户名和密码
      this.loadSavedCredentials()
      // this.getCode()
    },
    methods: {
      // 切换记住密码状态
      toggleRemember() {
        this.rememberPassword = !this.rememberPassword
      },
      
      // 加载保存的用户名和密码
      loadSavedCredentials() {
        try {
          const savedUsername = uni.getStorageSync('rememberedUsername')
          const savedPassword = uni.getStorageSync('rememberedPassword')
          const rememberedStatus = uni.getStorageSync('rememberPassword')
          
          if (savedUsername && savedPassword && rememberedStatus === 'true') {
            this.loginForm.username = savedUsername
            this.loginForm.password = savedPassword
            this.rememberPassword = true
          }
        } catch (e) {
          console.error('读取存储的用户信息失败', e)
        }
      },
      
      // 保存用户名和密码
      saveCredentials() {
        if (this.rememberPassword) {
          try {
            uni.setStorageSync('rememberedUsername', this.loginForm.username)
            uni.setStorageSync('rememberedPassword', this.loginForm.password)
            uni.setStorageSync('rememberPassword', 'true')
          } catch (e) {
            console.error('保存用户信息失败', e)
          }
        } else {
          // 不记住密码，清除已保存的信息
          try {
            uni.removeStorageSync('rememberedUsername')
            uni.removeStorageSync('rememberedPassword')
            uni.setStorageSync('rememberPassword', 'false')
          } catch (e) {
            console.error('清除用户信息失败', e)
          }
        }
      },
      
      // 用户注册
      handleUserRegister() {
        this.$tab.redirectTo(`/pages/register`)
      },
      // 隐私协议
      handlePrivacy() {
        let site = this.globalConfig.appInfo.agreements[0]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 用户协议
      handleUserAgrement() {
        let site = this.globalConfig.appInfo.agreements[1]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 获取图形验证码
      getCode() {
        getCodeImg().then(res => {
		  debugger
          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        })
      },
      // 登录方法
      async handleLogin() {
        if (this.loginForm.username === "") {
          this.$modal.msgError("请输入您的账号")
        } else if (this.loginForm.password === "") {
          this.$modal.msgError("请输入您的密码")
        } 
		// else if (this.loginForm.code === "" && this.captchaEnabled) {
  //         this.$modal.msgError("请输入验证码")
  //       } 
		else {
          this.$modal.loading("登录中，请耐心等待...")
          this.pwdLogin()
        }
      },
      // 密码登录
      async pwdLogin() {
        this.$store.dispatch('Login', this.loginForm).then(() => {
          // 登录成功后保存凭据（如果选择了记住密码）
          this.saveCredentials()
          
          this.$modal.closeLoading()
          this.loginSuccess()
        }).catch(() => {
          if (this.captchaEnabled) {
            // this.getCode()
          }
        })
      },
      // 登录成功后，处理函数
      loginSuccess(result) {
        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          			uni.setStorage({
			  key: 'userData',
			  data: res.permissions,
			  success: function () {
			    console.log('数据存储成功');
			  }
              });
           //console.log("用户信息"+res.permissions);
          // this.$tab.reLaunch('/pages/index')
		  this.$tab.reLaunch('/pages/work/index')
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }

  .normal-login-container {
    width: 100%;

    .logo-content {
      width: 100%;
      font-size: 21px;
      text-align: center;
      padding-top: 15%;

      image {
        border-radius: 4px;
      }

      .title {
        margin-left: 10px;
      }
    }

    .login-form-content {
      text-align: center;
      margin: 20px auto;
      margin-top: 15%;
      width: 80%;

      .input-item {
        margin: 20px auto;
        background-color: #f5f6f7;
        height: 45px;
        border-radius: 20px;

        .icon {
          font-size: 38rpx;
          margin-left: 10px;
          color: #999;
        }

        .input {
          width: 100%;
          font-size: 14px;
          line-height: 20px;
          text-align: left;
          padding-left: 15px;
        }
      }
      
      .remember-box {
        width: 100%;
        text-align: left;
        padding-left: 10px;
        margin-top: 10px;
        
        .custom-checkbox {
          display: flex;
          align-items: center;
          
          .checkbox-icon {
            width: 18px;
            height: 18px;
            border: 1px solid #ccc;
            border-radius: 3px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &.checkbox-icon-checked {
              border-color: #3c96f3;
              background-color: #3c96f3;
            }
            
            .checkbox-inner {
              width: 6px;
              height: 10px;
              border: 2px solid #fff;
              border-left: 0;
              border-top: 0;
              transform: rotate(45deg);
              margin-top: -2px;
              margin-left: 1px;
            }
          }
          
          .remember-text {
            margin-left: 8px;
            font-size: 14px;
            color: #666;
          }
        }
      }

      .login-btn {
        margin-top: 30px;
        height: 45px;
      }
      
      .reg {
        margin-top: 15px;
      }
      
      .xieyi {
        color: #333;
        margin-top: 20px;
      }
      
      .login-code {
        height: 38px;
        float: right;
      
        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }
  }

</style>
