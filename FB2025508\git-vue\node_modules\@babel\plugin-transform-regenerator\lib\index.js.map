{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_regeneratorTransform", "_default", "exports", "default", "declare", "types", "t", "assertVersion", "name", "inherits", "regeneratorTransform", "visitor", "MemberExpression", "path", "_this$availableHelper", "availableHelper", "call", "obj", "get", "isIdentifier", "helper", "addHelper", "isArrowFunctionExpression", "replaceWith", "body", "callExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { types as t } from \"@babel/core\";\nimport regeneratorTransform from \"regenerator-transform\";\n\nexport default declare(({ types: t, assertVersion }) => {\n  assertVersion(\n    process.env.BABEL_8_BREAKING && process.env.IS_PUBLISH\n      ? PACKAGE_JSON.version\n      : 7,\n  );\n\n  return {\n    name: \"transform-regenerator\",\n\n    inherits: regeneratorTransform.default,\n\n    visitor: {\n      // We visit MemberExpression so that we always transform\n      // regeneratorRuntime before babel-plugin-polyfill-regenerator.\n      MemberExpression(path) {\n        if (!process.env.BABEL_8_BREAKING) {\n          if (!this.availableHelper?.(\"regeneratorRuntime\")) {\n            // When using an older @babel/helpers version, fallback\n            // to the old behavior.\n            // TODO: Remove this in Babel 8.\n            return;\n          }\n        }\n\n        const obj = path.get(\"object\");\n        if (obj.isIdentifier({ name: \"regeneratorRuntime\" })) {\n          const helper = this.addHelper(\"regeneratorRuntime\") as\n            | t.Identifier\n            | t.ArrowFunctionExpression;\n\n          if (!process.env.BABEL_8_BREAKING) {\n            if (\n              // TODO: Remove this in Babel 8, it's necessary to\n              // avoid the IIFE when using older Babel versions.\n              t.isArrowFunctionExpression(helper)\n            ) {\n              obj.replaceWith(helper.body);\n              return;\n            }\n          }\n\n          obj.replaceWith(t.callExpression(helper, []));\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEA,IAAAC,qBAAA,GAAAD,OAAA;AAAyD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1C,IAAAC,0BAAO,EAAC,CAAC;EAAEC,KAAK,EAAEC,CAAC;EAAEC;AAAc,CAAC,KAAK;EACtDA,aAAa,CAGP,CACN,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,uBAAuB;IAE7BC,QAAQ,EAAEC,qBAAoB,CAACP,OAAO;IAEtCQ,OAAO,EAAE;MAGPC,gBAAgBA,CAACC,IAAI,EAAE;QACc;UAAA,IAAAC,qBAAA;UACjC,IAAI,GAAAA,qBAAA,GAAC,IAAI,CAACC,eAAe,aAApBD,qBAAA,CAAAE,IAAA,KAAI,EAAmB,oBAAoB,CAAC,GAAE;YAIjD;UACF;QACF;QAEA,MAAMC,GAAG,GAAGJ,IAAI,CAACK,GAAG,CAAC,QAAQ,CAAC;QAC9B,IAAID,GAAG,CAACE,YAAY,CAAC;UAAEX,IAAI,EAAE;QAAqB,CAAC,CAAC,EAAE;UACpD,MAAMY,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,oBAAoB,CAErB;UAEM;YACjC,IAGEf,CAAC,CAACgB,yBAAyB,CAACF,MAAM,CAAC,EACnC;cACAH,GAAG,CAACM,WAAW,CAACH,MAAM,CAACI,IAAI,CAAC;cAC5B;YACF;UACF;UAEAP,GAAG,CAACM,WAAW,CAACjB,CAAC,CAACmB,cAAc,CAACL,MAAM,EAAE,EAAE,CAAC,CAAC;QAC/C;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC"}