{"name": "vconsole", "version": "3.15.1", "description": "A lightweight, extendable front-end developer tool for mobile web page.", "homepage": "https://github.com/Tencent/vConsole", "files": ["dist/*", "build/vendor.d.ts", "CHANGELOG*", "README*"], "main": "dist/vconsole.min.js", "typings": "dist/vconsole.min.d.ts", "scripts": {"build": "webpack --mode=production --progress --env target=web", "build:wx": "webpack --mode=production --progress --env target=wx", "build:typings": "node ./build/build.typings.js", "build:dev": "webpack --mode=development --progress", "watch": "webpack --mode=development --watch --progress", "serve": "webpack serve --config webpack.serve.config --progress", "test:pack": "npx npm-packlist"}, "sideEffects": ["lib/*", "element/*", "network/*", "storage/*"], "keywords": ["console", "debug", "mobile"], "repository": {"type": "git", "url": "git+https://github.com/Tencent/vConsole.git"}, "dependencies": {"@babel/runtime": "^7.17.2", "copy-text-to-clipboard": "^3.0.1", "core-js": "^3.11.0", "mutation-observer": "^1.0.3"}, "devDependencies": {"@babel/core": "^7.14.0", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-export-namespace-from": "^7.12.13", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-block-scoping": "^7.16.0", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.14.1", "@babel/preset-typescript": "^7.13.0", "babel-loader": "^8.2.4", "babel-plugin-add-module-exports": "^1.0.4", "css-loader": "^6.7.1", "less": "^4.1.2", "less-loader": "^10.2.0", "style-loader": "^3.3.1", "svelte": "^3.47.0", "svelte-loader": "^3.1.2", "svelte-preprocess": "^4.10.6", "typescript": "^4.6.3", "webpack": "^5.72.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.8.1", "webpack-merge": "^5.8.0"}, "author": "Tencent", "license": "MIT"}