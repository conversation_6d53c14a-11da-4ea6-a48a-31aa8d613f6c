{"_from": "@babel/helper-string-parser@^7.23.4", "_id": "@babel/helper-string-parser@7.23.4", "_inBundle": false, "_integrity": "sha512-803gmbQdqwdf4olxrX4AJyFBV/RTr3rSmOj0rKwesmzlfhYNDEs+/iOcznzpNWlJlIlTJC2QfPFcHB6DlzdVLQ==", "_location": "/@babel/helper-string-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-string-parser@^7.23.4", "name": "@babel/helper-string-parser", "escapedName": "@babel%2fhelper-string-parser", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/types"], "_resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.23.4.tgz", "_shasum": "9478c707febcbbe1ddb38a3d91a2e054ae622d83", "_spec": "@babel/helper-string-parser@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\types", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A utility package to parse strings", "devDependencies": {"charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-string-parser", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-string-parser"}, "type": "commonjs", "version": "7.23.4"}