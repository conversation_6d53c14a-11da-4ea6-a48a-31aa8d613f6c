{"_from": "@babel/plugin-proposal-decorators@^7.8.3", "_id": "@babel/plugin-proposal-decorators@7.24.0", "_inBundle": false, "_integrity": "sha512-LiT1RqZWeij7X+wGxCoYh3/3b8nVOX6/7BZ9wiQgAIyjoeQWdROaodJCgT+dwtbjHaz0r7bEbHJzjSbVfcOyjQ==", "_location": "/@babel/plugin-proposal-decorators", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-proposal-decorators@^7.8.3", "name": "@babel/plugin-proposal-decorators", "escapedName": "@babel%2fplugin-proposal-decorators", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@vue/babel-preset-app"], "_resolved": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.0.tgz", "_shasum": "845b42189e7441faa60a37682de1038eae97c382", "_spec": "@babel/plugin-proposal-decorators@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@vue\\babel-preset-app", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-class-features-plugin": "^7.24.0", "@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-decorators": "^7.24.0"}, "deprecated": false, "description": "Compile class and object decorators to ES5", "devDependencies": {"@babel/core": "^7.24.0", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/traverse": "^7.24.0", "array.prototype.concat": "^1.0.2", "babel-plugin-polyfill-es-shims": "^0.10.2", "object.getownpropertydescriptors": "^2.1.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-decorators", "keywords": ["babel", "babel-plugin", "decorators"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-proposal-decorators", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-decorators"}, "type": "commonjs", "version": "7.24.0"}