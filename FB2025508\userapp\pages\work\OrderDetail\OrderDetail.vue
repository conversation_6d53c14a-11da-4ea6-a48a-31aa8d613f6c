<template>
	<view class="order-center-container">
		<view class="city-header">
			<text class="city-name">{{cityName}}</text>
		</view>
		
		<scroll-view 
			class="order-scroll-view" 
			scroll-y="true" 
			refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
		>
			<view class="order-list">
				<view class="order-item" v-for="(order, index) in orderList" :key="index">
					<view class="order-header">
						<text class="order-number">订单号: {{order.order_no}}</text>
						<text class="order-time">{{order.create_time}}</text>
					</view>
					<view class="order-content">
						<view class="order-row">
							<text class="label">女生代理：</text>
							<text class="value">{{order.female_staff_nickname || '无'}}</text>
							<text class="phone">{{order.female_staff_phone || ''}}</text>
						</view>
						<view class="order-row">
							<text class="label">男生代理：</text>
							<text class="value">{{order.male_staff_nickname || '无'}}</text>
							<text class="phone">{{order.male_staff_phone || ''}}</text>
						</view>
						<view class="order-row">
							<text class="label">女生姓名：</text>
							<text class="value">{{order.female_user_name || '无'}}</text>
						</view>
						<view class="order-row">
							<text class="label">男生姓名：</text>
							<text class="value">{{order.male_user_name || '无'}}</text>
						</view>
						<view class="order-row">
							<text class="label">订单状态：</text>
							<text class="value" :class="order.status === '1' ? 'status-active' : 'status-normal'">{{getStatusText(order.status)}}</text>
						</view>
						<view class="order-row" v-if="order.status === '1'">
							<text class="label">锁单时间：</text>
							<text class="value">{{order.update_time || ''}}</text>
						</view>
						<view class="order-row">
							<text class="label">订单周期：</text>
							<text class="value">{{order.orderDays}}天</text>
							<text class="value" v-if="order.orderMonths">/{{order.orderMonths}}个月</text>
						</view>
					</view>
					<view class="order-footer">
						<button class="btn-process" @click="processOrder(order)">订单详情</button>
					</view>
				</view>
			</view>
			
			<view class="empty-state" v-if="orderList.length === 0">
				<text>暂无订单数据</text>
			</view>
			
			<!--  <view class="load-more-container" v-if="orderList.length > 0 && !noMoreData">
				<button class="btn-load-more" @click="loadMore" :disabled="loadingMore">
					<text v-if="loadingMore">加载中...</text>
					<text v-else>加载更多</text>
				</button>
			</view>
			
			<view class="no-more-data" v-if="orderList.length > 0 && noMoreData">
				<text>没有更多数据了</text>
			</view>-->
		</scroll-view>
	</view>
</template>

<script>
	import { getOrderListFinsh } from '@/api/work/centerorder.js'
	import { getCurrentStaffInfo } from '@/api/work/centerorder.js'
	
	export default {
		data() {
			return {
				cityName: '',
				orderList: [],
				orderNos: [],
				currentPage: 1,
				pageSize: 10,
				total: 0,
				loading: false,
				loadingMore: false,
				isRefreshing: false,
				noMoreData: false,
				lastOrderNo: null,
				hasMore: true
			}
		},
		onLoad() {
			this.getStaffInfo();
	
		},
		methods: {
			// 获取当前店员信息和城市
			getStaffInfo() {
				getCurrentStaffInfo().then(res => {
					if (res.code === 200 && res.data) {
						this.cityName = res.data.region_name || '未知城市';
						this.orderNos =  res.data.order_no;
						console.log("bbbbbbbbb="+this.order_no);
						this.loadOrderData(true);
					}
				}).catch(err => {
					console.error('获取店员信息失败', err);
				});
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				this.currentPage = 1;
				this.lastOrderNo = null;
				this.noMoreData = false;
				this.hasMore = true;
				
				this.loadOrderData(true).then(() => {
					this.isRefreshing = false;
				}).catch(() => {
					this.isRefreshing = false;
				});
			},
			
			// 点击加载更多
			loadMore() {
				if (this.loading || this.loadingMore || !this.hasMore || this.noMoreData) {
					return;
				}
				
				this.loadingMore = true;
				this.loadOrderData(false);
			},
			
			// 加载订单数据
			loadOrderData(isRefresh = false) {
				// if (this.loading && !isRefresh) return Promise.reject();
				
				// this.loading = true;
				
				// // 如果是刷新，清空现有数据
				// if (isRefresh) {
				// 	this.orderList = [];
				// 	this.lastOrderNo = null;
				// }
				
				const params = {
					pageNum: this.currentPage,
					pageSize: this.pageSize,
					isRefresh: isRefresh,
					lastOrderNo: this.lastOrderNo,
					OrdersNo: this.orderNos
					
				};
				
				return getOrderListFinsh(params).then(res => {
					if (res.code === 200) {
						const newOrders = res.data.list || [];
						this.total = res.data.total || 0;
						this.hasMore = res.data.hasMore || false;
						
						if (newOrders.length === 0) {
							this.noMoreData = true;
						} else {
							// 计算订单周期（天数）
							newOrders.forEach(order => {
								const createDate = new Date(order.create_time);
								const currentDate = new Date();
								const diffTime = Math.abs(currentDate - createDate);
								const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
								order.orderDays = diffDays;
								order.orderMonths = Math.floor(diffDays / 30);
							});
							
							// 如果是刷新，直接替换数据，否则追加数据
							if (isRefresh) {
								this.orderList = newOrders;
							} else {
								this.orderList = [...this.orderList, ...newOrders];
							}
							
							// 保存最后一条记录的订单号，用于下次加载更多
							if (res.data.lastOrderNo) {
								this.lastOrderNo = res.data.lastOrderNo;
							}
							
							if (!this.hasMore) {
								this.noMoreData = true;
							}
						}
					} else {
						uni.showToast({
							title: '获取订单列表失败',
							icon: 'none'
						});
					}
					
					this.loading = false;
					this.loadingMore = false;
					return Promise.resolve();
				}).catch(err => {
					console.error('获取订单列表失败', err);
					uni.showToast({
						title: '获取订单列表失败',
						icon: 'none'
					});
					this.loading = false;
					this.loadingMore = false;
					return Promise.reject(err);
				});
			},
			
			// 获取订单状态文本
			getStatusText(status) {
				const statusMap = {
					'0': '进行中',
					'1': '已完成',
					'2': '已冻结'
				};
				return statusMap[status] || '未知';
			},
			
			// 获取订单状态样式类
			getStatusClass(status) {
				const classMap = {
					'0': 'status-ongoing',
					'1': 'status-completed',
					'2': 'status-frozen'
				};
				return classMap[status] || '';
			},
			
			// 处理订单
			processOrder(order) {
				// 跳转到订单详情页面
				uni.navigateTo({
					url: `/pages/work/OrderFinsh/OrderFinsh?orderNo=${order.order_no}`
				});
			}
		}
	}
</script>

<style lang="scss">
.order-center-container {
	padding: 0 10px;
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	
	.city-header {
		padding: 15px 10px;
		background-color: #fff;
		border-bottom: 1px solid #eee;
		
		.city-name {
			font-size: 18px;
			font-weight: bold;
		}
	}
	
	.order-scroll-view {
		flex: 1;
		height: calc(100vh - 60px); /* 减去城市头部的高度 */
	}
	
	.order-list {
		margin-top: 10px;
		
		.order-item {
			background-color: #fff;
			border-radius: 8px;
			margin-bottom: 15px;
			overflow: hidden;
			box-shadow: 0 2px 5px rgba(0,0,0,0.05);
			
			.order-header {
				padding: 12px;
				background-color: #f9f9f9;
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.order-number {
					font-size: 14px;
					color: #333;
					font-weight: 500;
				}
				
				.order-time {
					font-size: 12px;
					color: #666;
				}
			}
			
			.order-content {
				padding: 12px;
				
				.order-row {
					display: flex;
					margin-bottom: 8px;
					font-size: 14px;
					
					.label {
						width: 80px;
						color: #666;
					}
					
					.value {
						flex: 1;
						color: #333;
					}
					
					.phone {
						color: #0066cc;
						margin-left: 10px;
					}
					
					.status-ongoing {
						color: #2196F3;
					}
					
					.status-completed {
						color: #4CAF50;
					}
					
					.status-frozen {
						color: #F44336;
					}
				}
			}
			
			.order-footer {
				padding: 10px 12px;
				border-top: 1px solid #eee;
				display: flex;
				justify-content: flex-end;
				
				.btn-process {
					background-color: #2196F3;
					color: white;
					font-size: 14px;
					padding: 6px 16px;
					border-radius: 4px;
					border: none;
				}
			}
		}
	}
	
	.empty-state {
		margin-top: 100px;
		text-align: center;
		color: #999;
		font-size: 14px;
	}
	
	.load-more-container {
		text-align: center;
		padding: 15px 0;
		
		.btn-load-more {
			background-color: #f0f0f0;
			color: #666;
			font-size: 14px;
			padding: 8px 20px;
			border-radius: 4px;
			border: 1px solid #ddd;
			width: 60%;
			
			&:active {
				background-color: #e0e0e0;
			}
			
			&:disabled {
				background-color: #f5f5f5;
				color: #999;
			}
		}
	}
	
	.no-more-data {
		text-align: center;
		padding: 15px 0;
		color: #999;
		font-size: 14px;
	}
}
</style>
