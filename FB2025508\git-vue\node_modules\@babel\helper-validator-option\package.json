{"_from": "@babel/helper-validator-option@^7.23.5", "_id": "@babel/helper-validator-option@7.23.5", "_inBundle": false, "_integrity": "sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==", "_location": "/@babel/helper-validator-option", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-validator-option@^7.23.5", "name": "@babel/helper-validator-option", "escapedName": "@babel%2fhelper-validator-option", "scope": "@babel", "rawSpec": "^7.23.5", "saveSpec": null, "fetchSpec": "^7.23.5"}, "_requiredBy": ["/@babel/helper-compilation-targets", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz", "_shasum": "907a3fbd4523426285365d1206c423c4c5520307", "_spec": "@babel/helper-validator-option@^7.23.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-compilation-targets", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Validate plugin/preset options", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-validator-option", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-option"}, "type": "commonjs", "version": "7.23.5"}