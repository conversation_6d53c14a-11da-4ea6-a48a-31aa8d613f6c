{"_from": "@babel/plugin-transform-unicode-property-regex@^7.23.3", "_id": "@babel/plugin-transform-unicode-property-regex@7.23.3", "_inBundle": false, "_integrity": "sha512-KcLIm+pDZkWZQAFJ9pdfmh89EwVfmNovFBcXko8szpBeF8z68kWIPeKlmSOkT9BXJxs2C0uk+5LxoxIv62MROA==", "_location": "/@babel/plugin-transform-unicode-property-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-unicode-property-regex@^7.23.3", "name": "@babel/plugin-transform-unicode-property-regex", "escapedName": "@babel%2fplugin-transform-unicode-property-regex", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.23.3.tgz", "_shasum": "19e234129e5ffa7205010feec0d94c251083d7ad", "_spec": "@babel/plugin-transform-unicode-property-regex@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-unicode-property-regex", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "type": "commonjs", "version": "7.23.3"}