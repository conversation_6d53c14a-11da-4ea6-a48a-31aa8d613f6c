{"_from": "@babel/generator@^7.23.6", "_id": "@babel/generator@7.23.6", "_inBundle": false, "_integrity": "sha512-qrSfCYxYQB5owCmGLbl8XRpX1ytXlpueOb0N0UmQwA073KZxejgQTzAmJezxvpwQD9uGtK2shHdi55QT+MbjIw==", "_location": "/@babel/generator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/generator@^7.23.6", "name": "@babel/generator", "escapedName": "@babel%2fgenerator", "scope": "@babel", "rawSpec": "^7.23.6", "saveSpec": null, "fetchSpec": "^7.23.6"}, "_requiredBy": ["/@babel/core", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.23.6.tgz", "_shasum": "9e1fca4811c77a10580d17d26b57b036133f3c2e", "_spec": "@babel/generator@^7.23.6", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.23.6", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}, "deprecated": false, "description": "Turns an AST into code.", "devDependencies": {"@babel/helper-fixtures": "^7.23.4", "@babel/parser": "^7.23.6", "@jridgewell/sourcemap-codec": "^1.4.15", "@types/jsesc": "^2.5.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "files": ["lib"], "homepage": "https://babel.dev/docs/en/next/babel-generator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/generator", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "type": "commonjs", "version": "7.23.6"}