{"_from": "@babel/helpers@^7.24.0", "_id": "@babel/helpers@7.24.0", "_inBundle": false, "_integrity": "sha512-ulDZdc0Aj5uLc5nETsa7EPx2L7rM0YJM8r7ck7U73AXi7qOV44IHHRAYZHY6iU1rr3C5N4NtTmMRUJP6kwCWeA==", "_location": "/@babel/helpers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helpers@^7.24.0", "name": "@babel/helpers", "escapedName": "@babel%2fhelpers", "scope": "@babel", "rawSpec": "^7.24.0", "saveSpec": null, "fetchSpec": "^7.24.0"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.24.0.tgz", "_shasum": "a3dd462b41769c95db8091e49cfe019389a9409b", "_spec": "@babel/helpers@^7.24.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/template": "^7.24.0", "@babel/traverse": "^7.24.0", "@babel/types": "^7.24.0"}, "deprecated": false, "description": "Collection of helper functions used by Babel transforms.", "devDependencies": {"@babel/generator": "^7.23.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/parser": "^7.24.0", "regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helpers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "type": "commonjs", "version": "7.24.0"}