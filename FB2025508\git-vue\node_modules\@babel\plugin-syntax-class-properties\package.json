{"_from": "@babel/plugin-syntax-class-properties@^7.12.13", "_id": "@babel/plugin-syntax-class-properties@7.12.13", "_inBundle": false, "_integrity": "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==", "_location": "/@babel/plugin-syntax-class-properties", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-class-properties@^7.12.13", "name": "@babel/plugin-syntax-class-properties", "escapedName": "@babel%2fplugin-syntax-class-properties", "scope": "@babel", "rawSpec": "^7.12.13", "saveSpec": null, "fetchSpec": "^7.12.13"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "_shasum": "b5c987274c4a3a82b89714796931a6b53544ae10", "_spec": "@babel/plugin-syntax-class-properties@^7.12.13", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "deprecated": false, "description": "Allow parsing of class properties", "devDependencies": {"@babel/core": "7.12.13"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-properties", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-class-properties", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-properties"}, "version": "7.12.13"}