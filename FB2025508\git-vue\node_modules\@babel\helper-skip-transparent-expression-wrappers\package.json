{"_from": "@babel/helper-skip-transparent-expression-wrappers@^7.22.5", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.22.5", "_inBundle": false, "_integrity": "sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==", "_location": "/@babel/helper-skip-transparent-expression-wrappers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-skip-transparent-expression-wrappers@^7.22.5", "name": "@babel/helper-skip-transparent-expression-wrappers", "escapedName": "@babel%2fhelper-skip-transparent-expression-wrappers", "scope": "@babel", "rawSpec": "^7.22.5", "saveSpec": null, "fetchSpec": "^7.22.5"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "/@babel/plugin-transform-for-of", "/@babel/plugin-transform-optional-chaining", "/@babel/plugin-transform-spread"], "_resolved": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz", "_shasum": "007f15240b5751c537c40e77abb4e89eeaaa8847", "_spec": "@babel/helper-skip-transparent-expression-wrappers@^7.22.5", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\helper-create-class-features-plugin", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/types": "^7.22.5"}, "deprecated": false, "description": "Helper which skips types and parentheses", "devDependencies": {"@babel/traverse": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-skip-transparent-expression-wrappers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "type": "commonjs", "version": "7.22.5"}