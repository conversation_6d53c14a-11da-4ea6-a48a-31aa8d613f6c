{"_from": "@babel/plugin-syntax-optional-chaining@^7.8.3", "_id": "@babel/plugin-syntax-optional-chaining@7.8.3", "_inBundle": false, "_integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==", "_location": "/@babel/plugin-syntax-optional-chaining", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-optional-chaining@^7.8.3", "name": "@babel/plugin-syntax-optional-chaining", "escapedName": "@babel%2fplugin-syntax-optional-chaining", "scope": "@babel", "rawSpec": "^7.8.3", "saveSpec": null, "fetchSpec": "^7.8.3"}, "_requiredBy": ["/@babel/plugin-transform-optional-chaining", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "_shasum": "4f69c2ab95167e0180cd5336613f8c5788f7d48a", "_spec": "@babel/plugin-syntax-optional-chaining@^7.8.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "deprecated": false, "description": "Allow parsing of optional properties", "devDependencies": {"@babel/core": "^7.8.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/plugin-syntax-optional-chaining", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "version": "7.8.3"}