{"_from": "@babel/plugin-syntax-import-attributes@^7.23.3", "_id": "@babel/plugin-syntax-import-attributes@7.23.3", "_inBundle": false, "_integrity": "sha512-pawnE0P9g10xgoP7yKr6CK63K2FMsTE+FZidZO/1PwRdzmAPVs+HS1mAURUsgaoxammTJvULUdIkEK0gOcU2tA==", "_location": "/@babel/plugin-syntax-import-attributes", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-syntax-import-attributes@^7.23.3", "name": "@babel/plugin-syntax-import-attributes", "escapedName": "@babel%2fplugin-syntax-import-attributes", "scope": "@babel", "rawSpec": "^7.23.3", "saveSpec": null, "fetchSpec": "^7.23.3"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.23.3.tgz", "_shasum": "992aee922cf04512461d7dae3ff6951b90a2dc06", "_spec": "@babel/plugin-syntax-import-attributes@^7.23.3", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "deprecated": false, "description": "Allow parsing of the module attributes in the import statement", "devDependencies": {"@babel/core": "^7.23.3"}, "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-syntax-import-attributes", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-attributes"}, "type": "commonjs", "version": "7.23.3"}