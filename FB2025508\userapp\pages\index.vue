<template>
  <view class="content">
    <!-- <image class="logo" src="@/static/logo.png"></image> -->
	<!-- <div class="full-screen-background"></div> -->
	<!-- <image class="full-screen-background" src="@/static/logo.jpg" ></image> -->
	<img src="@/static/logo.png" draggable="false" style="width: 200px;height: 100px;padding: 80px 0 20px 0;">
    <view class="text-area">
      <text class="title">Hello MES</text>
    </view>
  </view>
</template>

<script>
  export default {
    onLoad: function() {
    }
  }
</script>

<style>
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .logo {
    height: 200rpx;
    width: 200rpx;
    margin-top: 200rpx;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 50rpx;
  }

  .text-area {
    display: flex;
    justify-content: center;
  }

  .title {
    font-size: 36rpx;
    color: #8f8f94;
  }
  
  .full-screen-background {
    position: fixed; /* 或 absolute，取决于布局需求 */
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-image: url('@/static/logo.jpg'); /* 替换为你的图片路径 */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1; /* 确保背景在其他内容下方 */
  }
</style>
