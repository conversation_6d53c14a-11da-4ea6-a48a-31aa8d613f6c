{"_from": "@babel/regjsgen@^0.8.0", "_id": "@babel/regjsgen@0.8.0", "_inBundle": false, "_integrity": "sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==", "_location": "/@babel/regjsgen", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/regjsgen@^0.8.0", "name": "@babel/regjsgen", "escapedName": "@babel%2fregjsgen", "scope": "@babel", "rawSpec": "^0.8.0", "saveSpec": null, "fetchSpec": "^0.8.0"}, "_requiredBy": ["/regexpu-core"], "_resolved": "https://registry.npmjs.org/@babel/regjsgen/-/regjsgen-0.8.0.tgz", "_shasum": "f0ba69b075e1f05fb2825b7fad991e7adbb18310", "_spec": "@babel/regjsgen@^0.8.0", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\regexpu-core", "author": {"name": "<PERSON>", "url": "https://ofcr.se/"}, "bugs": {"url": "https://github.com/bnjmnt4n/regjsgen/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Generate regular expressions from <PERSON>g<PERSON><PERSON><PERSON><PERSON>’s AST.", "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "regjsparser": "^0.9.1", "request": "^2.88.2"}, "files": ["LICENSE-MIT.txt", "regjsgen.js"], "homepage": "https://github.com/bnjmnt4n/regjsgen", "keywords": ["ast", "generate", "regex", "regexp", "regular expressions"], "license": "MIT", "main": "regjsgen.js", "name": "@babel/regjsgen", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/bnjmnt4n/regjsgen.git"}, "scripts": {"coverage": "nyc --reporter=html npm test", "report-coverage": "nyc --reporter=lcov npm test && codecov", "test": "node tests/tests.js", "update-fixtures": "node tests/update-fixtures.js"}, "version": "0.8.0"}