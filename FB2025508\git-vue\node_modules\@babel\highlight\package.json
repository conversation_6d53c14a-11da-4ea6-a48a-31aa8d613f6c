{"_from": "@babel/highlight@^7.23.4", "_id": "@babel/highlight@7.23.4", "_inBundle": false, "_integrity": "sha512-acGdbYSfp2WheJoJm/EBBBLh/ID8KDc64ISZ9DYtBmC8/Q204PZJLHyzeB5qMzJ5trcOkybd78M4x2KWsUq++A==", "_location": "/@babel/highlight", "_phantomChildren": {"ansi-styles": "3.2.1", "escape-string-regexp": "1.0.5", "supports-color": "5.5.0"}, "_requested": {"type": "range", "registry": true, "raw": "@babel/highlight@^7.23.4", "name": "@babel/highlight", "escapedName": "@babel%2fhighlight", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/code-frame"], "_resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.23.4.tgz", "_shasum": "edaadf4d8232e1a961432db785091207ead0621b", "_spec": "@babel/highlight@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\code-frame", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-validator-identifier": "^7.22.20", "chalk": "^2.4.2", "js-tokens": "^4.0.0"}, "deprecated": false, "description": "Syntax highlight JavaScript strings for output in terminals.", "devDependencies": {"strip-ansi": "^4.0.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-highlight", "license": "MIT", "main": "./lib/index.js", "name": "@babel/highlight", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-highlight"}, "type": "commonjs", "version": "7.23.4"}