{"_from": "@babel/plugin-transform-json-strings@^7.23.4", "_id": "@babel/plugin-transform-json-strings@7.23.4", "_inBundle": false, "_integrity": "sha512-81nTOqM1dMwZ/aRXQ59zVubN9wHGqk6UtqRK+/q+ciXmRy8fSolhGVvG09HHRGo4l6fr/c4ZhXUQH0uFW7PZbg==", "_location": "/@babel/plugin-transform-json-strings", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-json-strings@^7.23.4", "name": "@babel/plugin-transform-json-strings", "escapedName": "@babel%2fplugin-transform-json-strings", "scope": "@babel", "rawSpec": "^7.23.4", "saveSpec": null, "fetchSpec": "^7.23.4"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.23.4.tgz", "_shasum": "a871d9b6bd171976efad2e43e694c961ffa3714d", "_spec": "@babel/plugin-transform-json-strings@^7.23.4", "_where": "D:\\JAVA-MES\\git-vue\\node_modules\\@babel\\preset-env", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-json-strings": "^7.8.3"}, "deprecated": false, "description": "Escape U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings", "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-json-strings", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-json-strings", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-json-strings"}, "type": "commonjs", "version": "7.23.4"}